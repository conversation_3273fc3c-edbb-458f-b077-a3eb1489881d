type City = {
  name: string
  province: string
  provinceCode: string
}

const CITIES: City[] = [
  { name: "Roma", province: "Roma", provinceCode: "RM" },
  { name: "Milano", province: "Milano", provinceCode: "MI" },
  { name: "Napoli", province: "Napoli", provinceCode: "NA" },
  { name: "Torino", province: "Torino", provinceCode: "TO" },
  { name: "Palermo", province: "Palermo", provinceCode: "PA" },
  { name: "Genova", province: "Genova", provinceCode: "GE" },
  { name: "Bologna", province: "Bologna", provinceCode: "BO" },
  { name: "Firenze", province: "Firenze", provinceCode: "FI" },
  { name: "Bari", province: "Bari", provinceCode: "BA" },
  { name: "Catania", province: "Catania", provinceCode: "CT" },
  { name: "Venezia", province: "Venezia", provinceCode: "VE" },
  { name: "Verona", province: "Verona", provinceCode: "VR" },
  { name: "Messina", province: "Messina", provinceCode: "ME" },
  { name: "Padova", province: "Padova", provinceCode: "PD" },
  { name: "Trieste", province: "Trieste", provinceCode: "TS" },
  { name: "Brescia", province: "Brescia", provinceCode: "BS" },
  { name: "Taranto", province: "Taranto", provinceCode: "TA" },
  { name: "Prato", province: "Prato", provinceCode: "PO" },
  { name: "Modena", province: "Modena", provinceCode: "MO" },
  { name: "Reggio Calabria", province: "Reggio Calabria", provinceCode: "RC" },
  { name: "La Spezia", province: "La Spezia", provinceCode: "SP" },
]

export function isCityPresent(cityName: string | null | undefined): boolean {
  if (!cityName) return false
  return CITIES.some(
    (city) => city.name.toLowerCase() === cityName?.toLowerCase()
  )
}

// check if city present or online
export function isValidLocation(cityName: string | null | undefined): boolean {
  if (!cityName) return false
  return isCityPresent(cityName) || cityName.toLowerCase() === "online"
}
