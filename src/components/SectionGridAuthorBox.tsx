"use client"

import React, { FC } from "react"
import { useGetTopTenConsultantsQuery } from "@/api/gql/generated"
import BackgroundSection from "@/components/BackgroundSection"
import { AuthorType } from "@/data/types"
import Heading from "@/shared/Heading"
import Card<PERSON>uthorBox from "@/components/CardAuthorBox"

export interface SectionGridAuthorBoxProps {
  className?: string
  authors?: AuthorType[]
  boxCard?: "box1" | "box2"
  gridClassName?: string
}

const SectionGridAuthorBox: FC<SectionGridAuthorBoxProps> = ({
  className = "",
  gridClassName = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 ",
}) => {
  const { data, isLoading } = useGetTopTenConsultantsQuery(
    {
      page: 1,
      itemsPerPage: 10,
    },
    {
      select: (data) => data?.listConsultants?.collection,
    }
  )


  return (
    <div className="relative my-8 py-16">
      <BackgroundSection className="bg-neutral-100 dark:bg-black dark:bg-opacity-20 " />
      <div
        className={`nc-SectionGridAuthorBox relative ${className}`}
        data-nc-id="SectionGridAuthorBox"
      >
        <Heading desc="Competenza e affidabilità garantite" isCenter>
          I nostri migliori 10 professionisti
        </Heading>
        <div className={`grid gap-6 p-4 md:gap-8 ${gridClassName}`}>
          {data?.map((author, index) => (
            <CardAuthorBox
              index={index < 3 ? index + 1 : undefined}
              key={author?.urlKey}
              author={author}
            />
          ))}
          {isLoading &&
            Array.from({ length: 10 }, (_, i) => (
              <CardAuthorBox className="h-60" key={i} author={null} />
            ))}
        </div>
      </div>
    </div>
  )
}

export default SectionGridAuthorBox
