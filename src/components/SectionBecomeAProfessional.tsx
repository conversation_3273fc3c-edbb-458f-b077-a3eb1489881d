import React, { <PERSON> } from "react"
import Image, { StaticImageData } from "next/image"
import rightImgDemo from "@/images/professionisti-qualificati.png"
import { ArrowRightIcon } from "@heroicons/react/24/outline"

import ButtonPrimary from "@/shared/ButtonPrimary"

export interface SectionBecomeAProfessionalProps {
  title: string
  desc: string
  className?: string
  rightImg?: StaticImageData
  btnText?: string
  btnHref?: string
}

const SectionBecomeAProfessional: FC<SectionBecomeAProfessionalProps> = ({
  title,
  desc,
  btnText,
  btnHref,
  className = "",
  rightImg = rightImgDemo,
}) => {
  return (
    <div
      className={`nc-SectionBecomeAnAuthor relative flex flex-col items-center lg:flex-row  ${className}`}
      data-nc-id="SectionBecomeAnAuthor"
    >
      <div className="mb-16 flex-shrink-0 lg:mb-0 lg:mr-10 lg:w-2/5">
        <h2 className="mt-6 text-3xl font-semibold sm:mt-11 sm:text-4xl">
          {title}
        </h2>
        <span className="mt-6 block text-neutral-500 dark:text-neutral-400">
          {desc}
        </span>
        {btnText ? (
          <ButtonPrimary className="mt-6 sm:mt-11" href={btnHref}>
            {btnText}
            <span>
              <ArrowRightIcon className="ml-2 h-5 w-5 " />
            </span>
          </ButtonPrimary>
        ) : null}
      </div>
      <div className="flex-grow">
        <Image alt="" src={rightImg} />
      </div>
    </div>
  )
}

export default SectionBecomeAProfessional
