import React, { <PERSON> } from "react"
import Image, { StaticImageData } from "next/image"
import rightImgPng from "@/images/our-features.png"

import Badge from "@/shared/Badge"

export interface Benefit {
  badgeName: string
  badgeColor?: string
  title: string
  description: string
}

export interface SectionOurFeaturesProps {
  className?: string
  rightImg?: StaticImageData
  type?: "type1" | "type2"
  benefits: Benefit[]
  title: string
}

const SectionOurFeatures: FC<SectionOurFeaturesProps> = ({
  title,
  className = "lg:py-14",
  rightImg = rightImgPng,
  type = "type1",
  benefits,
}) => {
  return (
    <div
      className={`nc-SectionOurFeatures relative hidden flex-col items-center lg:flex ${type === "type1" ? "lg:flex-row" : "lg:flex-row-reverse"
        } ${className}`}
      data-nc-id="SectionOurFeatures"
    >
      <div className="flex-grow">
        <Image src={rightImg} alt="" className="rounded-xl" />
      </div>
      <div
        className={`mt-10 max-w-2xl flex-shrink-0 lg:mt-0 lg:w-2/5 ${type === "type1" ? "lg:pl-16" : "lg:pr-16"
          }`}
      >
        <h2 className="mt-5 text-4xl font-semibold">{title}</h2>

        <ul className="mt-16 space-y-10">
          {benefits.map((benefit, index) => (
            <li key={index} className="space-y-4">
              <Badge color={benefit.badgeColor} name={benefit.badgeName} />
              <span className="block text-xl font-semibold">
                {benefit.title}
              </span>
              <span className="mt-5 block text-neutral-500 dark:text-neutral-400">
                {benefit.description}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

export default SectionOurFeatures
