const RecaptchaDisclaimer: React.FC = () => {
  return (
    <p className="mt-6 text-sm text-neutral-400 dark:text-neutral-400">
      Questo form è protetto da reCAPTCHA e si applicano le{" "}
      <a
        rel="noopener noreferrer"
        target="_blank"
        href="https://policies.google.com/privacy"
        title="Privacy Policy"
        className="text-neutral-600"
      >
        Privacy Policy
      </a>{" "}
      e i{" "}
      <a
        rel="noopener noreferrer"
        target="_blank"
        href="https://policies.google.com/terms"
        title="Terms of Service"
        className="text-neutral-600"
      >
        Termini di Servizio
      </a>{" "}
      di Google.
    </p>
  )
}

export default RecaptchaDisclaimer
