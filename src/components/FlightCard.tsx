"use client"

import React, { FC, useState } from "react"
import { appointmentTimeframe, splitTimeframe } from "@/utils/timeframes"
import { isNumber } from "lodash"
import { DateTime } from "luxon"
import { array, object, InferOutput, string, enum_ } from "valibot"
import formatDate from 'intl-dateformat'
import ButtonPrimary from "@/shared/ButtonPrimary"
import { useRouter } from "next/navigation"
import { CheckIcon } from "@heroicons/react/24/solid"

// As TypeScript enum
enum AppointmentPaymentStatus {
  Paid = "PAID",
  Unpaid = "UNPAID",
}

export const AppointmentsSchema = array(
  object({
    id: string(),
    customer: object({
      fullName: string(),
      telephone: string(),
      email: string(),
    }),
    scheduledAt: string(),
    customerOrder: object({
      price: string(),
      service: object({
        name: string(),
        consultingServiceCategory: object({
          name: string(),
        }),
      }),
    }),
    paymentStatus: enum_(AppointmentPaymentStatus)
  })
)

export type Appointments = InferOutput<typeof AppointmentsSchema>
export interface FlightCardProps {
  className?: string
  data: Appointments[0]
}

const FlightCard: FC<FlightCardProps> = ({ className = "", data }) => {
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter();

  const renderDetailTop = () => {
    return (
      <div>
        <div className="flex flex-col md:flex-row ">
          <div className="my-5 flex md:my-0">
            <div className="ml-4 space-y-10 text-sm">
              <div className="flex flex-col space-y-1">
                <ul className="space-y-1 text-sm text-neutral-500 dark:text-neutral-400 md:space-y-2">
                  <li>
                    <span className="font-semibold">Nome cliente:</span>{" "}
                    {data?.customer?.fullName}
                  </li>
                  <li>
                    <span className="font-semibold">Servizio richiesto:</span>{" "}
                    {
                      data?.customerOrder?.service?.name
                    }
                  </li>
                  <li>
                    <span className="font-semibold">Prezzo:</span>{" "}
                    {data?.customerOrder?.price} €
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div className="border-l border-neutral-200 dark:border-neutral-700 md:mx-6 lg:mx-10"></div>
          <ul className="space-y-1 text-sm text-neutral-500 dark:text-neutral-400 md:space-y-2">
            <li>
              <span className="font-semibold">Telefono:</span>{" "}
              {data?.customer?.telephone}
            </li>
            <li>
              <span className="font-semibold">Email:</span>{" "}
              {data?.customer?.email}
            </li>
          </ul>
        </div>
      </div>
    )
  }

  const renderDetail = () => {
    if (!isOpen) return null
    return (
      <div className="rounded-2xl border border-neutral-200 p-4 dark:border-neutral-700 md:p-8 ">
        {renderDetailTop()}
      </div>
    )
  }

  const timeframe = appointmentTimeframe(
    DateTime.fromISO(data.scheduledAt, { zone: "utc" })
  )
  const { start, end } = splitTimeframe(timeframe)
  const appointmentDate = formatDate(new Date(data.scheduledAt), 'DD/MM/YYYY');

  const handleApproveClick = () => {
    router.push(`/appointment-payment?appointmentId=${data.id}`)
  }

  return (
    <div
      className={`nc-FlightCardgroup relative space-y-6 overflow-hidden rounded-2xl border border-neutral-100 bg-white p-4
     transition-all hover:shadow-lg dark:border-neutral-800 dark:bg-neutral-900 sm:p-6 ${className}`}
    >
      <div
        className={` relative sm:pr-20  ${className}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {/*  eslint-disable-next-line jsx-a11y/anchor-has-content */}

        <span
          className={`absolute bottom-0 right-0 flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-neutral-50 dark:bg-neutral-800 sm:bottom-auto sm:top-1/2 sm:-translate-y-1/2 ${isOpen ? "-rotate-180 transform" : ""
            }`}
          onClick={() => setIsOpen(!isOpen)}
        >
          <i className="las la-angle-down text-xl"></i>
        </span>

        <div className="flex  flex-col space-y-6 sm:flex-row sm:items-start sm:space-y-0">
          {/* FOR MOBILE RESPONSIVE */}
          <div className="block space-y-1 lg:hidden">
            <div className="text-neutral-500">
              <span>{appointmentDate}</span>
            </div>
            <div className="flex font-semibold">
              <div>
                <span>{start}</span>
              </div>
              <span className="flex w-12 justify-center">
                <i className=" las la-long-arrow-alt-right text-2xl"></i>
              </span>
              <div>
                <span>{end}</span>
              </div>
            </div>

            <div className="mt-0.5 text-sm font-normal text-neutral-500">
              <span>{data?.customerOrder?.service?.name}</span>
            </div>
          </div>

          {/* DATE */}
          <div className="hidden min-w-[120px]  flex-[1] lg:block ">
            <div className="text-sm font-medium text-neutral-500">Giorno</div>
            <div className="mt-0.5 text-md font-normal ">
              {appointmentDate}
            </div>
          </div>

          {/* TIME - NAME */}
          <div className="hidden min-w-[150px]  flex-[1] lg:block ">
            <div className="text-sm font-medium text-neutral-500">Orario</div>
            <div className="mt-0.5 text-md font-normal ">
              {appointmentTimeframe(
                DateTime.fromISO(data.scheduledAt, { zone: "utc" })
              )}
            </div>
          </div>

          {/* TYPE */}
          <div className="hidden flex-[3] min-w-[180px] whitespace-nowrap lg:block">
            <div className="text-sm font-medium text-neutral-500">Cliente</div>
            <div className="mt-0.5 text-md font-normal max-w-full whitespace-break-spaces">
              {data?.customer?.fullName}
            </div>
          </div>

          {/* TYPE */}
          <div className="hidden flex-[4] whitespace-nowrap lg:block">
            <div className="text-sm font-medium text-neutral-500">
              Servizio richiesto
            </div>
            <div className="mt-0.5 text-md font-normal whitespace-break-spaces">
              {data?.customerOrder?.service?.name}
            </div>
          </div>

          {/* Price */}
          <div className="hidden flex-[3] whitespace-nowrap lg:block">
            <div className="text-sm font-medium text-neutral-500">
              Servizio prezzo
            </div>
            <div className="mt-0.5 text-md font-normal ">
              {data?.customerOrder?.price
                ? isNumber(data.customerOrder.price) &&
                data.customerOrder.price
                : "N/A"} €
            </div>
          </div>

          {/* PRICE */}
          <div className="flex-[3] whitespace-nowrap sm:text-right">
            <div className="flex sm:text-right justify-end space-x-2 items-center">
              {data?.paymentStatus === AppointmentPaymentStatus.Paid ?
                <span className="text-green-500 flex items-center">
                  <CheckIcon className="w-5 h-5 mr-1" />
                  Paid
                </span>
                :
                <ButtonPrimary
                  onClick={handleApproveClick}
                  className="!px-3 !py-2 ml-2 !text-md"
                >
                  Approva
                </ButtonPrimary>
              }
            </div>
          </div>
        </div>
      </div>

      {renderDetail()}
    </div>
  )
}

export default FlightCard
