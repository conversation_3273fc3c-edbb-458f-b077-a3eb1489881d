"use client";
import { useEffect } from "react";

const DomModifier = () => {
    useEffect(() => {
        const element = document.querySelector(".top-10-section");
        if (element) {
            element.classList.add("hidden");
        }
        return () => {
            if (element) {
                element.classList.remove("hidden");
            }
        };

    }, []);
    return null;
};
export default DomModifier;
