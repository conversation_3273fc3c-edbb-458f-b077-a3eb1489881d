import React, { <PERSON> } from "react"
import { Route } from "next"
import Link from "next/link"
import { Consultant, Maybe, Offer } from "@/api/gql/generated"
import { MapPinIcon } from "@heroicons/react/24/outline"
import { isEmpty } from "lodash"

import Avatar from "@/shared/Avatar"
import Badge from "@/shared/Badge"
import StartRating from "@/components/StartRating"

import ServicesTable from "./ServicesTable"

export interface PropertyCardHProps {
  className?: string
  data?: Maybe<Consultant>
  serviceSearch?: string;
}

const PropertyCardH: FC<PropertyCardHProps> = ({
  data,
  className,
  serviceSearch
}) => {
  if (isEmpty(data)) {
    return null
  }

  const { avatar, fullName, offers, city, reviews, urlKey, professions } = data

  const href = `/consulente/${urlKey}` as Route

  const reviewsMap = reviews?.collection?.map((item) => item)
  const accReviews =
    reviewsMap?.reduce((acc, item) => {
      return acc + (item?.rating ?? 0)
    }, 0) ?? 0



  const medianReviews =
    (reviewsMap?.length ?? 0) > 0
      ? accReviews / Math.round(reviewsMap?.length ?? 0)
      : 0

  const renderContent = () => {
    return (
      <div className="listingSection__wrap !space-y-6">
        {/* 1 */}
        <div className="flex flex-wrap items-center gap-2">
          {!isEmpty(professions)
            ? professions?.collection?.map((profession, index) => {
              if (profession?.name) {
                return (
                  <Badge key={index} name={profession.name} />
                )
              }
            })
            : null}
        </div>

        {/* 2 */}
        <div className="flex items-center">
          <Avatar
            imgUrl={avatar ?? undefined}
            sizeClass="h-12 w-12"
            radius="rounded-full"
          />
          <h2 className="ml-2.5 text-lg font-semibold sm:text-xl lg:text-2xl">
            {fullName}
          </h2>
        </div>

        {/* 3 */}
        <div className="flex items-center space-x-4">
          {accReviews > 0 ? (
            <>
              <StartRating
                reviewCount={reviewsMap?.length}
                point={medianReviews}
              />

              <span>·</span>
            </>
          ) : null}
          <div className="flex items-center space-x-2">
            <MapPinIcon className="h-5 w-5" />

            <span>
              {city?.name !== "Online" ? `${city?.name}, Italia` : "Online"}
            </span>
          </div>
        </div>

        {/* 5 */}
        <div className="w-full border-b border-neutral-100 dark:border-neutral-700" />

        {/* 6 */}
        {!isEmpty(offers) && (
          <ServicesTable services={serviceSearch ? offers?.collection?.filter((item) => item?.consultingService?.urlKey === serviceSearch) as Offer[] : offers?.collection?.map((item) => item) as Offer[]} limit={1} />
        )}
      </div>
    )
  }
  return (
    <div
      className={`nc-PropertyCardH group relative overflow-hidden ${className}`}
      onClick={() =>
        localStorage?.setItem("scrollPosition", window.scrollY.toString())
      }
    >
      <Link href={href} className="absolute inset-0"></Link>
      <div className="flex h-full w-full flex-col sm:flex-row sm:items-center">
        {renderContent()}
      </div>
    </div>
  )
}

export default PropertyCardH
