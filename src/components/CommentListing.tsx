import React, { <PERSON> } from "react"
import { StarIcon } from "@heroicons/react/24/solid"

import Avatar from "@/shared/Avatar"

interface CommentListingDataType {
  __typename?: "ConsultantReview" | undefined
  authorName?: string | null | undefined
  createdAt?: string | null | undefined
  description?: string | null | undefined
  rating?: number | null | undefined
}

export interface CommentListingProps {
  className?: string
  data?: CommentListingDataType | null
  hasListingTitle?: boolean
}

const CommentListing: FC<CommentListingProps> = ({
  className = "",
  data,
  hasListingTitle,
}) => {
  return (
    <div
      className={`nc-CommentListing flex space-x-4 ${className}`}
      data-nc-id="CommentListing"
    >
      <div className="flex-grow">
        <div className="flex justify-between space-x-3">
          <div className="flex flex-col">
            <div className="text-sm font-semibold">
              <span>{data?.authorName}</span>
            </div>
            <span className="mt-0.5 text-sm text-neutral-500 dark:text-neutral-400">
              {new Date(data?.createdAt ?? "").toLocaleString("it-IT", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </span>
          </div>
          <div className="flex text-yellow-500">
            {Array.from({ length: 5 }).map((_, index) => (
              <StarIcon
                key={index}
                className={`h-4 w-4 ${
                  data?.rating && index < data?.rating
                    ? "text-yellow-500"
                    : "text-neutral-300"
                }`}
              />
            ))}
          </div>
        </div>
        <span className="mt-3 block text-neutral-6000 dark:text-neutral-300">
          {data?.description}
        </span>
      </div>
    </div>
  )
}

export default CommentListing
