import React, { FC } from "react"
import { Offer } from "@/api/gql/generated"
import { cn } from "@/utils/cn"
import {
  groupByCategory,
  groupByCategoryProfessional,
} from "@/utils/groupServices"
import { isNumber } from "lodash"

export interface ServicesTableProps {
  services: Offer[]
  limit?: number
}

const ServicesTable: FC<ServicesTableProps> = ({ services, limit }) => {
  if (!services) {
    return null
  }
  const showServices = limit ? services?.slice(0, limit) : services

  return (
    <div className="flow-root space-y-6">
      {showServices.map((offer, index) => {
        return (
          <div key={index}>
            <div className="mt-1 grid grid-cols-1">


              <div
                key={index}
                className={cn(
                  `flex items-center justify-between space-x-4 rounded-lg p-4`,
                  index % 2 === 0
                    ? "bg-neutral-100 dark:bg-neutral-800"
                    : ""
                )}
              >
                <span>{offer?.consultingService?.name}</span>
                <div className="flex items-baseline space-x-2">
                  <span className="flex gap-x-1 text-sm text-neutral-6000 dark:text-neutral-300">
                    <span className="hidden md:block">A partire</span>{" "}
                    <span className="capitalize md:normal-case">da</span>
                  </span>{" "}
                  <span className="font-semibold">
                    {offer.price || (offer?.consultingService?.price && isNumber(offer?.consultingService?.price) ? offer.consultingService.price / 100 : 0)}
                  </span>
                  <div className="pointer-events-none flex items-center">
                    <span className="text-gray-500">€</span>
                  </div>
                </div>
              </div>

            </div>
          </div>
        )
      })}
    </div>
  )
}

export default ServicesTable
