"use client"

import React from "react"
import Link from "next/link"

import { CustomLink } from "@/data/types"
import { cn } from "@/lib/utils"
import Logo from "@/shared/Logo"
import SocialsList1 from "@/shared/SocialsList1"

import FooterNav from "./FooterNav"

export interface CustomLinkWithOnclick extends CustomLink {
  onClick?: () => void
}
export interface WidgetFooterMenu {
  id: string
  title: string
  menus: CustomLinkWithOnclick[]
}

const widgetMenus: WidgetFooterMenu[] = [
  {
    id: "5",
    title: "Come funziona",
    menus: [
      { href: "/come-funziona", label: "Come trovare il mio professionista?" },
      { href: "/sei-un-professionista", label: "Sei un professionista?" },
      { href: "/consulenza", label: "Servizi" },
    ],
  },
  {
    id: "1",
    title: "Termini e Condizioni",
    menus: [
      { href: "/termini-e-condizioni", label: "Termini e Condizioni" },
      {
        href: "#",
        label: "Cookie Policy",
        onClick: () => {
          if (typeof window === "undefined") return
          return window._iub.cs.api.ui.showCP() as void
        },
      },
    ],
  },
  {
    id: "3",
    title: "FAQ",
    menus: [
      { href: "/come-funziona#faq", label: "Hai qualche domanda?" },
      {
        href: "/sei-un-professionista#faq",
        label: "Domande frequenti per i professionisti",
      },
    ],
  },
]

const Footer: React.FC = () => {
  const renderWidgetMenuItem = (menu: WidgetFooterMenu, index: number) => {
    const className = cn("text-sm", "col-span-1")
    const innerClassName = cn("text-sm mt-5")

    return (
      <div className={className} key={index}>
        <h2 className="font-semibold text-neutral-700 dark:text-neutral-200">
          {menu.title}
        </h2>
        <div className={innerClassName}>
          <ul className="space-y-4">
            {menu.menus.map((item, index) => (
              <li key={index}>
                <Link
                  key={index}
                  className="text-neutral-6000 hover:text-black dark:text-neutral-300 dark:hover:text-white"
                  href={item.href}
                  onClick={item.onClick}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="nc-Footer relative border-t border-neutral-200 py-24 dark:border-neutral-700 lg:py-28">
        <div className="container grid grid-cols-1 gap-x-5 gap-y-10 sm:gap-x-8 md:grid-cols-4 lg:grid-cols-5 lg:gap-x-10 ">
          <div className="grid grid-cols-1  gap-5 md:col-span-4 lg:md:col-span-1 lg:flex lg:grid-flow-col-dense lg:flex-col">
            <div className="col-span-1">
              <Logo className="max-w-[240px]" />
            </div>
            <div className="col-span-1 flex items-center md:col-span-3">
              <SocialsList1 className="flex items-center space-x-3 lg:flex-col lg:items-start lg:space-x-0 lg:space-y-2.5" />
            </div>
          </div>
          {widgetMenus.map(renderWidgetMenuItem)}
        </div>
        <div className="flex w-full justify-between container mt-16 border-t border-gray-900/10 pt-8 sm:mt-20 lg:mt-24">
          <p className="text-xs leading-5 text-gray-500">
            &copy; 2024 Consulente Ideale P.IVA 07099500824. Tutti i diritti
            riservati.
          </p>
          <p className="text-xs leading-5 text-gray-500">
            Developed by&nbsp;
            <Link href={process.env.NEXT_PUBLIC_EMERGENTO_WEBSITE ?? ""} target="_blank" className="text-primary-500 underline">Emergento</Link>
          </p>
        </div>
      </div>
    </>
  )
}

export default Footer
