"use client"

import React, { <PERSON>, useEffect, useMemo, useState } from "react"
import {
  useGetFeaturedConsultantProfessionsQuery,
  useGetFeaturedConsultingServiceCategoriesQuery,
  useGetFeaturedConsultingServicesQuery,
} from "@/api/gql/generated"
import { variants } from "@/utils/animationVariants"
import ncNanoId from "@/utils/ncNanoId"
import { useWindowSize } from "@uidotdev/usehooks"
import { AnimatePresence, motion, MotionConfig } from "framer-motion"
import { useSwipeable } from "react-swipeable"

import { TaxonomyOurProfessionals } from "@/data/types"
import Heading from "@/shared/Heading"
import CardCategory4 from "@/components/CardCategory4"

import NextBtn from "./NextBtn"
import PrevBtn from "./PrevBtn"

export interface SectionSliderNewCategoriesProps {
  className?: string
  itemClassName?: string
  heading?: string
  subHeading?: string
  categories?: TaxonomyOurProfessionals[]
  categoryCardType?: "card3" | "card4" | "card5" | "card6"
  itemPerRow?: 4 | 5
  sliderStyle?: "style1" | "style2"
}

const DEMO: TaxonomyOurProfessionals[] = [
  {
    id: "1",
    href: "/",
    count: 0,
    name: "Commercialisti",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "2",
    href: "/",
    count: 0,
    name: "Avvocati",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "3",
    href: "/",
    count: 0,
    name: "Consulenti finanziari",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "4",
    href: "/",
    count: 0,
    name: "Consulenti del lavoro",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "5",
    href: "/",
    count: 0,
    name: "Consulenti fiscali",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "6",
    href: "/",
    count: 0,
    name: "Consulenti aziendali",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "7",
    href: "/",
    count: 0,
    name: "Esperti contabili",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
  {
    id: "8",
    href: "/",
    count: 0,
    name: "Finanza agevolata",
    thumbnail: "https://images.unsplash.com/photo-1612830725324-3b3b3b3b3b3b",
  },
]

const SectionSliderNewCategories: FC<SectionSliderNewCategoriesProps> = ({
  heading = "Trova subito il servizio e il consulente che cerchi",
  className = "",
  itemClassName = "",
  categories = DEMO,
  itemPerRow = 5,
  sliderStyle = "style1",
}) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [direction, setDirection] = useState(0)
  const [numberOfItems, setNumberOfitem] = useState(0)
  const windowWidth = useWindowSize()?.width
  useEffect(() => {
    if (!windowWidth) return
    if (windowWidth < 320) {
      return setNumberOfitem(1)
    }
    if (windowWidth < 640) {
      return setNumberOfitem(2)
    }
    if (windowWidth < 1280) {
      return setNumberOfitem(itemPerRow - 1)
    }

    setNumberOfitem(itemPerRow)
  }, [itemPerRow, windowWidth])

  function changeItemId(newVal: number) {
    if (newVal > currentIndex) {
      setDirection(1)
    } else {
      setDirection(-1)
    }
    setCurrentIndex(newVal)
  }

  const handlers = useSwipeable({
    onSwipedLeft: () => {
      if (currentIndex < categories?.length - 1) {
        changeItemId(currentIndex + 1)
      }
    },
    onSwipedRight: () => {
      if (currentIndex > 0) {
        changeItemId(currentIndex - 1)
      }
    },
    trackMouse: true,
  })

  const renderCard = (item: TaxonomyOurProfessionals) => (
    <CardCategory4 taxonomy={item} />
  )

  const {
    data: consultingServiceCategories,
    isLoading: isLoadingConsultingServiceCategories,
    isError: isErrorConsultingServiceCategories,
  } = useGetFeaturedConsultingServiceCategoriesQuery(
    {},
    {
      select: (data) => data?.consultingServiceCategories,
    }
  )

  const {
    data: featuredConsultingServices,
    isLoading: isLoadingFeaturedConsultingServices,
    isError: isErrorFeaturedConsultingServices,
  } = useGetFeaturedConsultingServicesQuery(
    {},
    {
      select: (data) => data?.consultingServices,
    }
  )

  const {
    data: featuredConsultantProfessions,
    isLoading: isLoadingFeaturedConsultantProfessions,
    isError: isErrorFeaturedConsultantProfessions,
  } = useGetFeaturedConsultantProfessionsQuery(
    {},
    {
      select: (data) => {
        return data?.consultantProfessions
      },
    }
  )

  const elements: TaxonomyOurProfessionals[] = useMemo(() => {
    const allProfessions = featuredConsultantProfessions?.collection || []
    const allServices = featuredConsultingServices?.collection || []
    const allCategories = consultingServiceCategories?.collection || []

    const professions = allProfessions.map((item) => ({
      id: item?.id || ncNanoId(),
      name: item?.name || "",
      count: 0,
      thumbnail: item?.image
        ? `${process.env.NEXT_PUBLIC_BACKEND_MEDIA_URL}/consulting/professions/${item?.image}`
        : "",
      href: `/consulenti/${item?.urlKey}`,
    }))

    const services = allServices.map((item) => ({
      id: item?.id || ncNanoId(),
      name: item?.name || "",
      count: 0,
      thumbnail: item?.image
        ? `${process.env.NEXT_PUBLIC_BACKEND_MEDIA_URL}/consulting/services/${item?.image}`
        : "",
      href: `/consulenza/${item?.urlKey}`,
    }))

    const categories = allCategories.map((item) => ({
      id: item?.id || ncNanoId(),
      name: item?.name || "",
      count: 0,
      thumbnail: item?.image
        ? `${process.env.NEXT_PUBLIC_BACKEND_MEDIA_URL}/consulting/categories/${item?.image}`
        : "",
      href: `/servizi/${item?.urlKey}`,
    }))

    return [...professions, ...categories, ...services]
  }, [
    featuredConsultantProfessions,
    featuredConsultingServices,
    consultingServiceCategories,
  ])

  const isLoading = useMemo(
    () =>
      isLoadingConsultingServiceCategories ||
      isLoadingFeaturedConsultingServices ||
      isLoadingFeaturedConsultantProfessions,
    [
      isLoadingConsultingServiceCategories,
      isLoadingFeaturedConsultingServices,
      isLoadingFeaturedConsultantProfessions,
    ]
  )

  if (!numberOfItems) return null

  return (
    <div className={`nc-SectionSliderNewCategories ${className}`}>
      <Heading isCenter={sliderStyle === "style2"}>{heading}</Heading>
      <MotionConfig
        transition={{
          x: { type: "spring", stiffness: 300, damping: 30 },
          opacity: { duration: 0.2 },
        }}
      >
        <div className={`relative flow-root`} {...handlers}>
          <div className={`flow-root overflow-hidden rounded-xl`}>
            <motion.ul
              initial={false}
              className="relative -mx-2 whitespace-nowrap xl:-mx-4"
            >
              <AnimatePresence initial={false} custom={direction}>
                {elements.map((item, indx) => (
                  <motion.li
                    className={`relative inline-block px-2 xl:px-4 ${itemClassName}`}
                    custom={direction}
                    initial={{
                      x: `${(currentIndex - 1) * -100}%`,
                    }}
                    animate={{
                      x: `${currentIndex * -100}%`,
                    }}
                    variants={variants(200, 1)}
                    key={ncNanoId()}
                    style={{
                      width: `calc(1/${numberOfItems} * 100%)`,
                    }}
                  >
                    {!isLoadingConsultingServiceCategories && renderCard(item)}
                  </motion.li>
                ))}
                {isLoading
                  ? Array.from({ length: 5 }).map((_, index) => (
                    <motion.li
                      className={`relative inline-block px-2 xl:px-4 ${itemClassName}`}
                      custom={direction}
                      initial={{
                        x: `${(currentIndex - 1) * -100}%`,
                      }}
                      animate={{
                        x: `${currentIndex * -100}%`,
                      }}
                      variants={variants(200, 1)}
                      key={ncNanoId()}
                      style={{
                        width: `calc(1/${numberOfItems} * 100%)`,
                      }}
                    >
                      <CardCategory4 skeleton />
                    </motion.li>
                  ))
                  : null}
              </AnimatePresence>
            </motion.ul>
          </div>

          {currentIndex ? (
            <PrevBtn
              style={{ transform: "translate3d(0, 0, 0)" }}
              onClick={() => changeItemId(currentIndex - 1)}
              className="absolute -left-3 top-1/3 z-[1] h-9 w-9 -translate-y-1/2 text-lg xl:-left-6 xl:h-12 xl:w-12"
            />
          ) : null}
          {categories.length > currentIndex + numberOfItems ? (
            <NextBtn
              style={{ transform: "translate3d(0, 0, 0)" }}
              onClick={() => changeItemId(currentIndex + 1)}
              className="absolute -right-3 top-1/3 z-[1] h-9 w-9 -translate-y-1/2 text-lg xl:-right-6 xl:h-12 xl:w-12"
            />
          ) : null}
        </div>
      </MotionConfig>
    </div>
  )
}

export default SectionSliderNewCategories
