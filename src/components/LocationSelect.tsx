"use client"

import * as React from "react"
import { useCallback, useEffect, useRef, useState } from "react"
import { useGetCitiesQuery } from "@/api/gql/generated"

import { cn } from "@/lib/utils"
import {
  CleanUiCommandInput,
  CommandCleanUI,
  CommandEmpty,
  CommandGroup,
  CommandItemCleanUI,
} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"
var capitalize = require('capitalize')

export type MainSearchProps = {
  onChange: (value: string) => void
  value: string
  onBlur: () => void
  placeHolder?: string
  desc?: string
  isError?: boolean
}

const LocationSelect: React.FC<MainSearchProps> = ({
  value,
  onChange,
  onBlur,
  isError,
}) => {
  const [showPopover, setShowPopover] = useState(false)

  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const firstMatchRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowPopover(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])
  const handleCleanup = useCallback(() => {
    onChange("")
    inputRef.current?.focus()
  }, [onChange])

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  const { data, isLoading } = useGetCitiesQuery(
    {
      name: "",
    },
    {
      select: (data) => {
        const cityNames = data?.cities?.collection?.map((city) => city?.name)
        const cities = cityNames?.filter(
          (name): name is string => typeof name === "string"
        )
        return cities
      },
    }
  )

  const handleValueChange = (newValue: string) => {
    onChange(newValue)
    if (!showPopover) {
      setShowPopover(true)
    }
  }

  const handleSelect = async (currentValue: string) => {
    setShowPopover(false)
    onChange(capitalize.words(currentValue))
  }

  useEffect(() => {
    if (firstMatchRef.current) {
      firstMatchRef.current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      })
    }
  }, [value])

  return (
    <div className="relative flex flex-1" ref={containerRef}>
      <CommandCleanUI
        shouldFilter={true}
        className={cn(isError && "border-red-500")}
        filter={(value, search) => {
          const normalizedValue = value.toLowerCase()
          const normalizedSearch = search.toLowerCase()
          if (normalizedValue.startsWith(normalizedSearch)) {
            return 1
          } else if (normalizedValue.includes(normalizedSearch)) {
            return 0.5
          }
          return 0
        }}
      >
        <CleanUiCommandInput
          placeholder="Città"
          value={value ? value : ""}
          className="overflow-hidden"
          onValueChange={handleValueChange}
          ref={inputRef}
          onFocus={() => {
            setShowPopover(true)
          }}
          onBlur={onBlur}
          onKeyDown={(e) => {
            if (e.key === "Escape") {
              handleCleanup()
            } else if (e.key === "Tab") {
              setShowPopover(false)
            }
          }}
        />
        {value && (
          <ClearDataButton
            onClick={(e) => {
              e.stopPropagation()
              handleCleanup()
            }}
          />
        )}

        <div
          className={cn(
            "absolute left-0 top-full z-40 mt-5 w-full  min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-2 transition-all focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-neutral-700 dark:bg-neutral-800 dark:focus:ring-primary-6000 dark:focus:ring-opacity-25 sm:py-6",
            !showPopover && "hidden"
          )}
        >
          <div className="max-h-48 overflow-y-auto md:max-h-96">
            <CommandEmpty className="flex w-full items-center justify-center gap-x-2">
              <p>Nessuna città trovata.</p>
              <ClearDataButton
                className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                onClick={(e) => {
                  e.stopPropagation()
                  handleCleanup()
                }}
              >
                Cancella Filtri
              </ClearDataButton>
            </CommandEmpty>
            <CommandGroup>
              {data?.map((city) => {
                const matches = city
                  .toLowerCase()
                  .includes(value?.toLowerCase())
                const ref =
                  matches && !firstMatchRef.current ? firstMatchRef : null
                if (matches && ref) firstMatchRef.current = ref.current
                return (
                  <CommandItemCleanUI
                    key={city}
                    value={city}
                    onSelect={handleSelect}
                    ref={ref}
                  >
                    {city}
                  </CommandItemCleanUI>
                )
              })}
            </CommandGroup>
          </div>
        </div>
      </CommandCleanUI>
    </div>
  )
}

export default LocationSelect
