"use client"

import React, { useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { PathName } from "@/routers/types"
import isInViewport from "@/utils/isInViewport"
import {
  HeartIcon,
  LifebuoyIcon,
  MagnifyingGlassIcon,
  UserCircleIcon,
  UserIcon,
} from "@heroicons/react/24/outline"

import MenuBar from "@/shared/MenuBar"
import MobileChat from "@/shared/MobileChat"

let WIN_PREV_POSITION = 0
if (typeof window !== "undefined") {
  WIN_PREV_POSITION = window.pageYOffset
}

interface NavItem {
  name: string
  link?: PathName
  icon: any
}

const NAV: NavItem[] = [
  {
    name: "Cerca",
    link: "/",
    icon: MagnifyingGlassIcon,
  },
  {
    name: "Account",
    link: "/account",
    icon: UserCircleIcon,
  },
  {
    name: "<PERSON><PERSON>",
    icon: MobileChat,
  },
  {
    name: "<PERSON><PERSON>",
    icon: <PERSON>uBar,
  },
]

const FooterNav = () => {
  const containerRef = useRef<HTMLDivElement>(null)

  const pathname = usePathname()

  const renderItem = (item: NavItem, index: number) => {
    const isActive = pathname === item.link

    return item.link ? (
      <Link
        key={index}
        href={item.link}
        className={`flex flex-col items-center justify-between text-neutral-500 dark:text-neutral-300/90 ${
          isActive ? "text-primary-900 dark:text-neutral-100" : ""
        }`}
      >
        <item.icon
          className={`h-6 w-6 ${isActive ? "text-primary-6000" : ""}`}
        />
        <span
          className={`mt-1 text-[11px] leading-none ${
            isActive ? "text-primary-6000" : ""
          }`}
        >
          {item.name}
        </span>
      </Link>
    ) : (
      <div
        key={index}
        className={`flex flex-col items-center justify-between text-neutral-500 dark:text-neutral-300/90 ${
          isActive ? "text-primary-6000 dark:text-neutral-100" : ""
        }`}
      >
        <item.icon iconClassName="w-6 h-6" className={``} />
        <span className="mt-1 text-[11px] leading-none">{item.name}</span>
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className="FooterNav fixed inset-x-0 bottom-0 top-auto z-30 block border-t border-neutral-300 bg-white p-2 transition-transform duration-300 ease-in-out 
      dark:border-neutral-700 dark:bg-neutral-800 md:!hidden"
    >
      <div className="mx-auto flex w-full max-w-lg justify-around text-center text-sm ">
        {/* MENU */}
        {NAV.map(renderItem)}
      </div>
    </div>
  )
}

export default FooterNav
