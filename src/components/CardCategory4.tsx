import React, { FC } from "react"
import Image from "next/image"
import Link from "next/link"
import convertNumbThousand from "@/utils/convertNumbThousand"

import { TaxonomyOurProfessionals } from "@/data/types"

type CardCategory4BaseProps = {
  className?: string
}

type CardCategory4SkeletonProps = CardCategory4BaseProps & {
  skeleton: true
}

type CardCategory4TaxonomyProps = CardCategory4BaseProps & {
  taxonomy: TaxonomyOurProfessionals
  skeleton?: false
}

export type CardCategory4Props =
  | CardCategory4SkeletonProps
  | CardCategory4TaxonomyProps

const CardCategory4: FC<CardCategory4Props> = (props) => {
  const { className = "", skeleton = false } = props

  if (skeleton) {
    return (
      <div
        className={`nc-CardCategory4 flex flex-col ${className}`}
        data-nc-id="CardCategory4"
      >
        <div className="aspect-h-5 aspect-w-5 rounded-2xl bg-neutral-200 sm:aspect-h-6 dark:bg-neutral-800"></div>
        <div className="mt-4 flex flex-col items-center gap-y-2 truncate px-2">
          <div className="mt-1 h-4 w-5/6 rounded bg-neutral-200 dark:bg-neutral-800"></div>
        </div>
      </div>
    )
  } else {
    const { taxonomy } = props as CardCategory4TaxonomyProps
    const { name, href = "/", thumbnail } = taxonomy

    return (
      <Link
        href={href}
        className={`nc-CardCategory4 flex flex-col ${className}`}
        data-nc-id="CardCategory4"
      >
        <div
          className={`group aspect-h-6 aspect-w-5 relative h-0 w-full flex-shrink-0 overflow-hidden rounded-2xl`}
        >
          <Image
            src={thumbnail || ""}
            className="h-full w-full rounded-2xl object-cover"
            fill
            alt="archive"
            sizes="(max-width: 400px) 100vw, 400px"
          />
          <span className="absolute inset-0 bg-black bg-opacity-10 opacity-0 transition-opacity group-hover:opacity-100"></span>
        </div>
        <div className="mt-4 truncate px-2 text-center">
          <h2 className="text-base font-medium text-neutral-900 [text-wrap:balance] dark:text-neutral-100 sm:text-lg">
            {name}
          </h2>
        </div>
      </Link>
    )
  }
}

export default CardCategory4
