"use client"

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as React<PERSON>ouse<PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useEffect,
  useRef,
  useState,
} from "react"
import { useGetConsultantQuery } from "@/api/gql/generated"
import { formState<PERSON>tom } from "@/store/checkout"
import { searchPathServiceAtom } from "@/store/search"
import { groupByCategoryProfessional } from "@/utils/groupServices"
import { Transition } from "@headlessui/react"
import { AcademicCapIcon } from "@heroicons/react/24/outline"
import { sendGTMEvent } from "@next/third-parties/google"
import { CheckIcon } from "@radix-ui/react-icons"
import { useAtom, useAtomValue } from "jotai"
import { useForm } from "react-hook-form"
import { date, number, object, string, type InferOutput } from "valibot"

import { cn } from "@/lib/utils"
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { <PERSON>, <PERSON>Field, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/form"
import SelectTimeframe, { Timeframe } from "@/components/SelectTimeframe"
import ConfirmButton from "@/app/(listing-detail)/(components)/ConfirmButton"
import StayDatesRangeInput from "@/app/(listing-detail)/consulente/[[...professional]]/StayDatesRangeInput"
import { isNumber } from "lodash"
import CompleteFormDialog from "@/app/(listing-detail)/(components)/CompleteFormDialog"

const FormSchema = object({
  service: string(),
  date: date(),
  consultant: string(),
  serviceId: number(),
})

export type FormSchemaType = InferOutput<typeof FormSchema>

export function ComboboxForm({ consultant }: { consultant: string }) {
  const [formAtom, setFormAtom] = useAtom(formStateAtom)
  const initialServiceUrlKey = useAtomValue(searchPathServiceAtom)
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const [showPopover, setShowPopover] = useState(false)

  const { data } = useGetConsultantQuery(
    {
      urlKey: consultant,
    },
    {
      select: (data) => data?.byUrlKeyConsultant?.offers?.collection ?? [],
    }
  )

  const { data: consultantData } = useGetConsultantQuery(
    {
      urlKey: consultant,
    },
    {
      select: (data) => {
        return {
          id: data?.byUrlKeyConsultant?._id,
          name: data?.byUrlKeyConsultant?.fullName,
        }
      },
    }
  )

  const findServiceLabelByUrlKey = useCallback(
    (urlKey: string | undefined) => {
      if (!urlKey) return
      return data?.find((item) => item?.consultingService?.urlKey === urlKey)?.consultingService?.urlKey
    },
    [data]
  )

  const form = useForm<FormSchemaType>()

  // initial value on data loading

  useEffect(() => {
    form.setValue(
      "service",
      findServiceLabelByUrlKey(initialServiceUrlKey?.split("/")?.[2]) ?? ""
    )
    if (consultantData?.id && consultantData?.name) {
      sendGTMEvent({
        event: "view_item",
        view_item: [
          {
            item_id: consultantData?.id,
            item_name: consultantData?.name,
          },
        ],
      })
      if (typeof window !== "undefined" && window.fbq) {
        try {
          window.fbq("track", "ViewContent", {
            content_ids: consultantData?.id,
            content_type: "product",
          })
        } catch (e) {
          console.error(e)
        }
      }
    }
  }, [
    consultantData?.id,
    consultantData?.name,
    data,
    findServiceLabelByUrlKey,
    form,
    initialServiceUrlKey,
  ])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowPopover(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  const handleValueChange = useCallback(
    (newValue: string) => {
      form.setValue("service", newValue)
      form.setValue(
        "serviceId",
        data?.find((item) => item?.consultingService?.urlKey === newValue)?.consultingService?._id ?? 0
      )
      setShowPopover(false)
    },
    [data, form]
  )

  const handleButtonClick = () => {
    setShowPopover((prev) => !prev)
  }

  const handleChangeDate = (date: Date | null) => {
    setFormAtom({
      day: date,
    })
  }

  const handleTimeframeChange = (time: Timeframe) => {
    setFormAtom({
      time,
    })
  }

  const onSubmit = (data: FormSchemaType) => {
    const { service, consultant, serviceId } = data
    setFormAtom({
      ...formAtom,
      professional: consultant,
      service,
      serviceId,
    })
  }

  const onInvalid = (errors: any) => {
    console.log("errors", errors)
  }

  const isButtonDisabled =
    !form.watch("service") || !formAtom.day || formAtom.time.time === ""

  const watchService = form.watch("service")

  const findCurrentOfferPrice = useCallback(
    (service: string) => {
      const item = data
        ?.find((item) => item?.consultingService?.urlKey === service)
      const price = item?.price || (item?.consultingService?.price && isNumber(item?.consultingService?.price) ? item.consultingService.price / 100 : 0)

      return price ?? 0
    },
    [data]
  )

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
          <div className="flex flex-col rounded-3xl border border-neutral-200 dark:border-neutral-700">
            <StayDatesRangeInput
              className="z-[11] flex-1"
              handleChangeDate={handleChangeDate}
            />
            <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>
            <SelectTimeframe handleTimeframeChange={handleTimeframeChange} />
            <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>

            <FormField
              control={form.control}
              name="service"
              render={({ field }) => (
                <FormItem className="relative space-y-0" ref={containerRef}>
                  <FormLabel className="hidden">Servizi</FormLabel>
                  <button
                    type="button"
                    onClick={handleButtonClick}
                    className={cn(
                      "relative flex w-full flex-1 items-center gap-x-3 p-3 font-semibold focus:outline-none xl:text-lg",
                      !field.value && "text-muted-foreground"
                    )}
                  >
                    <span className="text-neutral-300 dark:text-neutral-400">
                      <AcademicCapIcon className="h-5 w-5 lg:h-7 lg:w-7" />
                    </span>
                    <div className="flex-grow text-left">
                      <span>
                        {field.value
                          ? data?.find(
                            (item) => item?.consultingService?.urlKey === field.value
                          )?.consultingService?.name

                          : "Scegli un servizio"}
                      </span>
                      <span className="mt-1 block text-sm font-light leading-none text-neutral-400">
                        {"Di cosa hai bisogno?"}
                      </span>
                    </div>
                  </button>
                  <Transition
                    as={Fragment}
                    show={showPopover}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <div className="absolute top-full z-10 w-full">
                      <Command
                        shouldFilter={false}
                        className="mt-2 rounded-3xl bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-neutral-800"
                      >
                        <CommandList className="overflow-contain max-h-[300px] min-h-[200px] w-full overflow-auto">
                          {data?.map((item) => (
                            <CommandItem
                              key={item?.consultingService?.name}
                              value={item?.consultingService?.name ?? ""}
                              onSelect={() => {
                                handleValueChange(item?.consultingService?.urlKey ?? "")
                              }}
                              className="flex items-center gap-x-3 rounded-sm p-3 text-sm font-semibold focus:outline-none"
                            >
                              {item?.consultingService?.name}
                              <CheckIcon
                                className={cn(
                                  "ml-auto h-4 w-4",
                                  item?.consultingService?.name === field.value
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                            </CommandItem>
                          ))}
                        </CommandList>
                      </Command>
                    </div>
                  </Transition>
                  {/* <FormMessage /> */}
                </FormItem>
              )}
            />

            {/* SUBMIT */}
          </div>
          <div className="mt-4 flex flex-col space-y-4">
            <div className="border-b border-neutral-200 dark:border-neutral-700"></div>
            <div className="flex justify-between">
              <span className="text-neutral-6000 dark:text-neutral-300">
                A partire da
              </span>
              <span className=" font-semibold">
                {findCurrentOfferPrice(watchService)}
                <span className="text-gray-500 ml-1">€</span>
              </span>

            </div>
          </div>
          <CompleteFormDialog />
          <ConfirmButton
            type="submit"
            className="mt-4 w-full disabled:pointer-events-none disabled:opacity-50"
            disabled={isButtonDisabled}
          >
            Prenota
          </ConfirmButton>
        </form>
      </Form>
    </>
  )
}
