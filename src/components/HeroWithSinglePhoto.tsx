import Image from "next/image"
import DiventaConsulente from "@/images/diventa-consulente-1.png"
import { ArrowRightIcon } from "@heroicons/react/24/outline"

import { cn } from "@/lib/utils"
import ButtonPrimary from "@/shared/ButtonPrimary"

export const HeroWithSinglePhoto = () => {
  return (
    <div
      className="nc-SectionHeroFull relative items-center sm:mb-20 md:mb-0"
      data-nc-id="SectionHeroFull"
    >
      <div className="absolute inset-0 z-10 bg-primary-6000 opacity-40"></div>
      <div
        className={cn(
          "container absolute inset-0 z-10 flex w-full  flex-col justify-center space-y-12"
        )}
      >
        <div className="max-w-xl">
          <h1 className=" text-xl font-bold !leading-[115%] text-white [text-wrap:balance] sm:text-4xl md:text-5xl ">
            Diventa un consulente ideale
          </h1>
          <span className=" mt-6 block text-lg font-medium text-white">
            Entra nella nostra community di professionisti e metti a
            disposizione la tua esperienza.
          </span>
        </div>
        <div className="mt-10 flex items-center gap-x-6">
          <ButtonPrimary className="mt-6 sm:mt-11" href={"/auth/registrazione"}>
            Unisciti a noi
            <span>
              <ArrowRightIcon className="ml-2 h-5 w-5 " />
            </span>
          </ButtonPrimary>
        </div>
      </div>
      <div className="aspect-h-3 aspect-w-4 relative sm:aspect-h-3 sm:aspect-w-4 lg:aspect-h-9 lg:aspect-w-16 xl:aspect-h-8 ">
        <Image
          className="absolute inset-0 object-cover"
          src={DiventaConsulente}
          alt="hero"
          priority
        />
      </div>
    </div>
  )
}

export default HeroWithSinglePhoto
