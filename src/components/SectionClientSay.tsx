"use client"

import React, { <PERSON>, useState } from "react"
import Image from "next/image"
import clientSay1 from "@/images/2.png"
import clientSay2 from "@/images/3.png"
import clientSay3 from "@/images/7.png"
import clientSay4 from "@/images/11.png"
import clientSay5 from "@/images/13.png"
import clientSay6 from "@/images/14.png"
import quotationImg from "@/images/quotation.png"
import quotationImg2 from "@/images/quotation2.png"
import { variants } from "@/utils/animationVariants"
import { MapPinIcon } from "@heroicons/react/24/outline"
import { AnimatePresence, motion, MotionConfig } from "framer-motion"
import { useSwipeable } from "react-swipeable"
import ClientImage1 from "@/images/16.png"
import ClientImage2 from "@/images/9.png"
import ClientImage3 from "@/images/6.png"

import Heading from "@/shared/Heading"

export interface SectionClientSayProps {
  className?: string
  data: typeof DEMO_DATA
  desc?: string
}

const DEMO_DATA = [
  {
    id: 1,
    clientName: "Tiana Abie",
    clientAddress: "Malaysia",
    content:
      "This place is exactly like the picture posted on Chisfis. Great service, we had a great stay!",
    clientImage: ClientImage1
  },
  {
    id: 2,
    clientName: "Lennie Swiffan",
    clientAddress: "London",
    content:
      "This place is exactly like the picture posted on Chisfis. Great service, we had a great stay!",
    clientImage: ClientImage2
  },
  {
    id: 3,
    clientName: "Berta Emili",
    clientAddress: "Tokyo",
    content:
      "This place is exactly like the picture posted on Chisfis. Great service, we had a great stay!",
    clientImage: ClientImage3
  },
]

const SectionClientSay: FC<SectionClientSayProps> = ({
  className = "",
  data,
  desc = "Leggi tutte le recensioni lasciate dai nostri clienti sui diversi consulenti.",
}) => {
  const [index, setIndex] = useState(0)
  const [direction, setDirection] = useState(0)
  const [isSwiping, setIsSwiping] = useState(false);

  function changeItemId(newVal: number) {
    if (newVal > index) {
      setDirection(1)
    } else {
      setDirection(-1)
    }
    setIndex(newVal)
  }

  const handlers = useSwipeable({
    onSwipedLeft: () => {
      if (index < data?.length - 1) {
        changeItemId(index + 1);
      }
      setIsSwiping(false);
    },
    onSwipedRight: () => {
      if (index > 0) {
        changeItemId(index - 1);
      }
      setIsSwiping(false);
    },
    onSwipeStart: () => {
      setIsSwiping(true);
    },
    onSwiped: () => {
      setIsSwiping(false);
    },
    trackMouse: true,
  });

  let currentItem = data[index]

  const renderBg = () => {
    return (
      <div className="hidden md:block">
        <Image
          className="absolute -left-20 top-9 w-12 rounded-full"
          src={clientSay1}
          alt="client 1"
        />
        <Image
          className="absolute bottom-[100px] right-full mr-40 w-12 rounded-full"
          src={clientSay2}
          alt="client 2"
        />
        <Image
          className="absolute left-[140px] top-full w-12 rounded-full"
          src={clientSay3}
          alt="client 3"
        />
        <Image
          className="absolute -bottom-10 right-[140px] w-12 rounded-full"
          src={clientSay4}
          alt="client 4"
        />
        <Image
          className="absolute bottom-[80px] left-full ml-32 w-12 rounded-full"
          src={clientSay5}
          alt="client 5"
        />
        <Image
          className="absolute -right-10 top-10 w-12 rounded-full"
          src={clientSay6}
          alt="client 6"
        />
      </div>
    )
  }

  return (
    <div className={`nc-SectionClientSay relative ${className} `}>
      <Heading desc={desc} isCenter>
        Dicono di noi
      </Heading>
      <div
        className={`relative mx-auto max-w-2xl md:mb-16 ${isSwiping ? 'select-none pointer-events-none' : ''
          }`}
        {...handlers}
      >
        {renderBg()}
        <Image className="mx-auto w-40 h-auto object-cover rounded-full" src={currentItem.clientImage} alt="" />
        <div className={`relative mt-12 lg:mt-16 `}>
          <Image
            className="absolute right-full top-1 -mr-16 opacity-50 md:opacity-100 lg:mr-3"
            src={quotationImg}
            alt=""
          />
          <Image
            className="absolute left-full top-1 -ml-16 opacity-50 md:opacity-100 lg:ml-3"
            src={quotationImg2}
            alt=""
          />

          <MotionConfig
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
            }}
          >
            <div
              className={`relative overflow-hidden whitespace-nowrap`}
            >
              <AnimatePresence initial={false} custom={direction}>
                <motion.div
                  key={index}
                  custom={direction}
                  variants={variants(200, 1)}
                  initial="enter"
                  animate="center"
                  // exit="exit"
                  className="inline-flex flex-col items-center whitespace-normal text-center"
                >
                  <>
                    <span className="block text-lg">
                      {currentItem.content}
                    </span>
                    <span className="mt-8 block text-lg font-semibold">
                      {currentItem.clientName}
                    </span>
                    <div className="mt-2 flex items-center space-x-2 text-base text-neutral-400">
                      <MapPinIcon className="h-5 w-5" />
                      <span>{currentItem.clientAddress}</span>
                    </div>
                  </>
                </motion.div>
              </AnimatePresence>

              <div className="mt-10 flex items-center justify-center space-x-2">
                {data.map((item, i) => (
                  <button
                    className={`h-2 w-2 rounded-full ${i === index ? "bg-black/70" : "bg-black/10 "
                      }`}
                    onClick={() => changeItemId(i)}
                    key={i}
                  />
                ))}
              </div>
            </div>
          </MotionConfig>
        </div>
      </div>
    </div>
  )
}

export default SectionClientSay
