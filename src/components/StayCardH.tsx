import React, { <PERSON> } from "react"
import Link from "next/link"
import { DEMO_STAY_LISTINGS } from "@/data/listings"
import { StayDataType } from "@/data/types"
import Badge from "@/shared/Badge"

import BtnLikeIcon from "@/components/BtnLikeIcon"
import GallerySlider from "@/components/GallerySlider"
import SaleOffBadge from "@/components/SaleOffBadge"
import StartRating from "@/components/StartRating"

export interface StayCardHProps {
  className?: string
  data?: StayDataType
}

const DEMO_DATA = DEMO_STAY_LISTINGS[0]

const StayCardH: FC<StayCardHProps> = ({
  className = "",
  data = DEMO_DATA,
}) => {
  const {
    galleryImgs,
    listingCategory,
    address,
    title,
    href,
    like,
    saleOff,
    isAds,
    price,
    reviewStart,
    reviewCount,
    id,
  } = data

  const renderSliderGallery = () => {
    return (
      <div className="relative w-full flex-shrink-0 md:w-72 ">
        <GallerySlider
          ratioClass="aspect-w-6 aspect-h-5"
          galleryImgs={galleryImgs}
          uniqueID={`StayCardH_${id}`}
          href={href}
        />
        <BtnLikeIcon isLiked={like} className="absolute right-3 top-3" />
        {saleOff && <SaleOffBadge className="absolute left-3 top-3" />}
      </div>
    )
  }

  const renderTienIch = () => {
    return (
      <div className="hidden grid-cols-3 gap-2 sm:grid">
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <i className="las la-user text-lg"></i>
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              6 guests
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <i className="las la-bed text-lg"></i>
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              6 beds
            </span>
          </div>
        </div>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <i className="las la-bath text-lg"></i>
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              3 baths
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <i className="las la-smoking-ban text-lg"></i>
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              No smoking
            </span>
          </div>
        </div>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <i className="las la-door-open text-lg"></i>
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              6 bedrooms
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <i className="las la-wifi text-lg"></i>
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              Wifi
            </span>
          </div>
        </div>
      </div>
    )
  }

  const renderContent = () => {
    return (
      <div className="flex flex-grow flex-col p-3 sm:p-5">
        <div className="space-y-2">
          <div className="text-sm text-neutral-500 dark:text-neutral-400">
            <span>
              {listingCategory.name} in {address}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {isAds && <Badge name="ADS" color="green" />}
            <h2 className="text-lg font-medium capitalize">
              <span className="line-clamp-1">{title}</span>
            </h2>
          </div>
        </div>
        <div className="my-4 hidden w-14 border-b border-neutral-100 dark:border-neutral-800 sm:block"></div>
        {renderTienIch()}
        <div className="my-4 w-14 border-b border-neutral-100 dark:border-neutral-800"></div>
        <div className="flex items-end justify-between">
          <StartRating reviewCount={reviewCount} point={reviewStart} />
          <span className="text-base font-semibold text-secondary-500">
            {price}
            {` `}
            <span className="text-sm font-normal text-neutral-500 dark:text-neutral-400">
              /night
            </span>
          </span>
        </div>
      </div>
    )
  }

  return (
    <div
      className={`nc-StayCardH group relative overflow-hidden rounded-2xl border border-neutral-100 bg-white transition-shadow hover:shadow-xl dark:border-neutral-800 dark:bg-neutral-900 ${className}`}
    >
      <Link href={href} className="absolute inset-0"></Link>
      <div className="grid grid-cols-1 md:flex md:flex-row ">
        {renderSliderGallery()}
        {renderContent()}
      </div>
    </div>
  )
}

export default StayCardH
