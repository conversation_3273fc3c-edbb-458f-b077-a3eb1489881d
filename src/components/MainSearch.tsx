"use client"

import {FC, useCallback, useEffect, useRef, useState} from "react"
import {useGetConsultantProfessionsAllQuery, useGetConsultingServicesQuery,} from "@/api/gql/generated"
import {
    isSearchLoading,
    professionOrService,
    searchInputErrorAtom,
    searchPathServiceAtom,
    searchServiceAtom,
} from "@/store/search"
import {AcademicCapIcon} from "@heroicons/react/24/outline"
import {useAtom} from "jotai"
import {isEmpty} from "lodash"

import {Command, CommandInput, CommandItem,} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"
import ncNanoId from "@/utils/ncNanoId";

export type MainSearchProps = {
  autoFocus?: boolean
}


const MainSearch: FC<MainSearchProps> = ({ autoFocus = false }) => {
  const [showPopover, setShowPopover] = useState(autoFocus)
  const [value, setValue] = useAtom(searchServiceAtom)
  const [, setPath] = useAtom(searchPathServiceAtom)
    const [, setProfOrService] = useAtom(professionOrService)
  const [, setInputError] = useAtom(searchInputErrorAtom)
  const [, setLoading] = useAtom(isSearchLoading)
    const [searchSuggetions, setSearchSuggetions] = useState<any[]>([]);
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleCleanup = useCallback(() => {
    setValue("")
    setPath(null)
    inputRef.current?.focus()
  }, [setValue, setPath])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowPopover(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
      if (window !== undefined) {
        const searchContainer = document.getElementsByClassName('nc-HeroSearchForm')[0]
        if (searchContainer && searchContainer.parentElement && searchContainer.parentElement.offsetTop) {
          window.scrollTo({ top: searchContainer.parentElement?.offsetTop * (-2), behavior: 'smooth' })
        }
      }
    }
  }, [showPopover])

  const { data, isLoading } = useGetConsultingServicesQuery(
    {},
    {
      select: (data) =>
        data?.consultingServices?.collection ?? [],
    }
  )

    const {
        data: featuredConsultantProfessions,
        isLoading: isLoadingFeaturedConsultantProfessions,
    } = useGetConsultantProfessionsAllQuery(
        {},
        {
            select: (data) => {
                return data?.consultantProfessions?.collection ?? []
            },
        }
    )



  useEffect(() => {
    setLoading(isLoading)
  }, [isLoading, setLoading])

    useEffect(() => {
        setLoading(isLoadingFeaturedConsultantProfessions)
    }, [isLoadingFeaturedConsultantProfessions]);

  const handleValueChange = (newValue: string) => {
    setValue(newValue)
    if (!showPopover) {
      setShowPopover(true)
    }
  }

  useEffect(() => {
    const isInData = searchSuggetions?.some((service) =>

      service?.name?.toLowerCase() === value.toLowerCase()

    )

    setInputError(!isInData || value === "")
  }, [searchSuggetions, setInputError, value])

  const handleSelect = useCallback(
      (currentValue: any) => {
      let pickPath

      for (const service of searchSuggetions || []) {
        if (service?.name === currentValue.name) {
          pickPath = service?.urlKey
          break
        }
      }

      const servicePath = pickPath ? `/${pickPath}` : ""
      const completePath = `${servicePath}`
      setPath(completePath || null)
          setProfOrService(currentValue?.type || '');
      setValue(currentValue?.name)
      setShowPopover(false)
    },
    [searchSuggetions, setPath, setValue, setShowPopover]
  )


    useEffect(() => {
        const services: any[] = (data ?? []).map(s => ({
            ...s,
            type: 'service',
        }))

        const professions: any[] = (featuredConsultantProfessions ?? []).map(p => ({
            ...p,
            type: 'profession',
        }))

        const all = [...services, ...professions]

        const byName = new Map<string, any>()
        all.forEach(item => {
            // normalize casing if you want case-insensitive dedupe:
            const key = item.name.toLowerCase()
            byName.set(key, item)
        })

        setSearchSuggetions(Array.from(byName.values()))
    }, [data, featuredConsultantProfessions])


    return (
    <div className="relative flex flex-1" ref={containerRef}>
      <Command shouldFilter={false}>
        <div
          onClick={() => setShowPopover(true)}
          className={`[ nc-hero-field-padding ] relative z-10 flex flex-1 flex-shrink-0 cursor-pointer items-center space-x-3 text-left focus:outline-none`}
        >
          <div className="text-neutral-300 dark:text-neutral-400">
            <AcademicCapIcon className="h-7 w-7" />
          </div>
          <div className="flex-grow">
            <CommandInput
              placeholder="Ricerca servizio"
              value={value}
              onValueChange={handleValueChange}
              ref={inputRef}
              className="capitalize"
              onFocus={() => setShowPopover(true)}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  handleCleanup()
                } else if (e.key === "Tab") {
                  setShowPopover(false)
                }
              }}
            />
            <span className="mt-0.5 block text-sm font-light text-neutral-400 ">
              <span className="line-clamp-1">
                {!!value ? "Servizio" : "Di cosa hai bisogno?"}
              </span>
            </span>
            {value && (
              <ClearDataButton
                onClick={(e) => {
                  e.stopPropagation()
                  handleCleanup()
                }}
              />
            )}
          </div>
        </div>
        {showPopover && (
          <div className="absolute left-0 top-[70%] md:top-full z-40 mt-5  w-full min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-4 ring-primary-300 ring-offset-2 transition-all dark:bg-neutral-800 sm:py-6">
            <div className="max-h-80 overflow-y-auto md:max-h-96">
              {!isLoading && isEmpty(searchSuggetions) ? (
                <div className="flex items-center justify-start gap-x-2 px-5 text-base">
                  <span className="text-base">
                    Nessun Professionista trovato.
                  </span>
                  <ClearDataButton
                    className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCleanup()
                    }}
                  >
                    Cancella Filtri
                  </ClearDataButton>
                </div>
              ) : null}
                {searchSuggetions?.filter((item) => item?.name?.toLowerCase().includes(value.toLowerCase()))?.map((service) => {
                if (service) {
                  return (
                    <CommandItem
                        key={ncNanoId()}
                        value={service._id.toString() ?? ""}
                        onSelect={() => handleSelect(service ?? "")}
                    >
                      {service.name}
                    </CommandItem>
                  )
                }
              }
              )}
            </div>
          </div>
        )}
      </Command>
    </div>
  )
}

export default MainSearch
