import React, { <PERSON> } from "react"
import Link from "next/link"
import { StarIcon } from "@heroicons/react/24/solid"
import { isNumber } from "lodash"

import { cn } from "@/lib/utils"
import Avatar from "@/shared/Avatar"
import Badge from "@/shared/Badge"

import Spinner from "./Spinner"
import roundToHalf from "@/utils/numberToHalf"

export interface CardAuthorBoxProps {
  className?: string
  author?: {
    __typename?: "Consultant"
    fullName: string
    avatar?: string | null
    urlKey?: string | null
    city?: { __typename?: "City"; name: string } | null
    reviews?: {
      __typename?: "ConsultantReviewPageConnection"
      collection?: Array<{
        __typename?: "ConsultantReview"
        rating?: number | null
      } | null> | null
    } | null
  } | null
  index?: number
}

const CardAuthorBox: FC<CardAuthorBoxProps> = ({
  className = "",
  author,
  index,
}) => {
  if (!author)
    return (
      <div
        className={cn(
          "nc-CardAuthorBox [ nc-box-has-hover ] [ nc-dark-box-bg-has-hover ] relative flex flex-col items-center  justify-center px-3 py-5 text-center sm:px-6 sm:py-7",
          className
        )}
      >
        <Spinner />
      </div>
    )
  const { urlKey, city, avatar, fullName, reviews } = author

  const starRating =
    reviews?.collection?.reduce((acc, item) => {
      return acc + (item?.rating ?? 0)
    }, 0) ?? 0

  const medianReviews =
    (reviews?.collection?.length ?? 0) > 0
      ? starRating / Math.round(reviews?.collection?.length ?? 0)
      : 0

  return (
    <Link
      href={urlKey ? `/consulente/${urlKey}` : "#"}
      className={`nc-CardAuthorBox [ nc-box-has-hover ] [ nc-dark-box-bg-has-hover ] relative flex flex-col items-center  justify-center px-3 py-5 text-center sm:px-6 sm:py-7 ${className}`}
    >
      {index && <Badge className="absolute left-3 top-3" name={`#${index}`} />}
      <Avatar
        sizeClass="w-20 h-20 text-2xl"
        radius="rounded-full"
        userName={fullName}
        imgUrl={avatar}
      />
      <div className="mt-3">
        <h2 className={`text-base font-medium`}>
          <span className="line-clamp-1">{fullName}</span>
        </h2>
        <span
          className={`mt-1.5 block text-sm text-neutral-500 dark:text-neutral-400`}
        >
          {city?.name || "Online"}
        </span>
      </div>
      {isNumber(starRating) && medianReviews !== 0 && (
        <div className="mt-4 flex items-center justify-center rounded-full bg-neutral-100 px-5 py-2 dark:bg-neutral-800 ">
          <span className="pt-[1px] text-xs font-medium">{roundToHalf(medianReviews)}</span>
          <StarIcon className="ml-2 h-5 w-5 text-amber-500 " />
        </div>
      )}
    </Link>
  )
}

export default CardAuthorBox
