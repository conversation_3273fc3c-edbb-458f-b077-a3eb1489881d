import React, { <PERSON> } from "react"
import { MapPinIcon } from "@heroicons/react/24/outline"

import Avatar from "@/shared/Avatar"
import { Skeleton } from "@/components/ui/skeleton"
import StartRating from "@/components/StartRating"

export interface ProfessionalCardSkeletonProps {
  className?: string
}

const ProfessionalCardSkeleton: FC<ProfessionalCardSkeletonProps> = ({
  className = "",
}) => {
  const renderContent = () => {
    return (
      <div className="listingSection__wrap !space-y-6">
        {/* 1 */}
        <div className="flex flex-wrap items-center gap-2">
          <Skeleton className="h-5 w-20 rounded-full" />
          <Skeleton className="h-5 w-20 rounded-full" />
        </div>

        {/* 2 */}
        <div className="flex items-center">
          <Skeleton className="h-10 w-10 rounded-full" />
          <Skeleton className="ml-2.5 h-6 w-48 text-lg font-semibold text-primary-100 sm:text-xl lg:text-2xl"></Skeleton>
        </div>

        {/* 3 */}
        <div className="flex items-center space-x-4">
          <Skeleton className="h-5 w-20 rounded-md" />
          <span>·</span>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-5 w-32 rounded-md" />
          </div>
        </div>

        {/* 5 */}
        <div className="w-full border-b border-neutral-100 dark:border-neutral-700" />

        {/* 6 */}
        <Skeleton className="h-5 w-full rounded-md" />
      </div>
    )
  }
  return (
    <div
      className={`nc-PropertyCardH group relative overflow-hidden ${className}`}
    >
      <div className="flex h-full w-full flex-col sm:flex-row sm:items-center">
        {renderContent()}
      </div>
    </div>
  )
}

export default ProfessionalCardSkeleton
