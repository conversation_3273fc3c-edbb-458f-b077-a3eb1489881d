"use client"

import React, { FC, Fragment, useCallback, useEffect, useState } from "react"
import { formStateAtom } from "@/store/checkout"
import { Popover, RadioGroup, Transition } from "@headlessui/react"
import { ClockIcon } from "@heroicons/react/24/outline"
import { useAtom, useAtomValue } from "jotai"
import { DateTime } from "luxon"
import { date } from "valibot"

export interface GuestsInputProps {
  className?: string
  handleTimeframeChange?: (date: Timeframe) => void
}

export type Timeframe = {
  id: number
  name: string
  time: string
  isoTime: string
  isoTimeEnd: string
}

const TIMEFRAMES: Timeframe[] = [
  {
    id: 0,
    name: "<PERSON><PERSON>",
    time: "09:00 - 12:00",
    isoTime: "T09:00:00",
    isoTimeEnd: "T12:00:00",
  },
  {
    id: 1,
    name: "Pomeriggio",
    time: "12:00 - 16:00",
    isoTime: "T12:00:00",
    isoTimeEnd: "T16:00:00",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    time: "16:00 - 20:00",
    isoTime: "T16:00:00",
    isoTimeEnd: "T20:00:00",
  },
]

const SelectTimeframe: FC<GuestsInputProps> = ({
  className = "flex-1",
  handleTimeframeChange,
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<Timeframe | null>(
    null
  )
  const [formState] = useAtom(formStateAtom)

  const onChangeTimeframe = (timeframe: Timeframe) => {
    setSelectedTimeframe(timeframe)
    handleTimeframeChange && handleTimeframeChange(timeframe)
  }

  const [uiTimeframe] = useState<Timeframe | null>(null)

  useEffect(() => {
    setSelectedTimeframe(null)
  }, [formState.day])

  const hasPassed = useCallback(
    (isoTime: string) => {
      if (formState.day) {
        const selectedDayISO = DateTime.fromJSDate(formState.day).toISODate()
        const selectedDayWithTime = DateTime.fromISO(
          `${selectedDayISO}${isoTime}`
        )

        if (!selectedDayWithTime.isValid) {
          console.error("Failed to create DateTime with time:", isoTime)
          return false
        }
        return selectedDayWithTime < DateTime.local()
      } else {
        return false
      }
    },
    [formState.day]
  )

  return (
    <Popover className={`relative flex ${className}`}>
      {({ open, close }) => (
        <>
          <div
            className={`flex flex-1 items-center focus:outline-none ${
              open ? "shadow-lg" : ""
            }`}
          >
            <Popover.Button
              className={`relative z-10 flex flex-1 items-center space-x-3 p-3 text-left focus:outline-none`}
            >
              <div className="text-neutral-300 dark:text-neutral-400">
                <ClockIcon className="h-5 w-5 lg:h-7 lg:w-7" />
              </div>
              <div className="flex-grow">
                <span className="block font-semibold xl:text-lg">
                  {selectedTimeframe?.time || "Scegli un orario"}
                </span>
                <span className="mt-1 block text-sm font-light leading-none text-neutral-400">
                  {"A che ora vuoi essere ricontattato?"}
                </span>
              </div>
            </Popover.Button>
          </div>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-200"
            enterFrom="opacity-0 translate-y-1"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-150"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 translate-y-1"
          >
            <Popover.Panel className="absolute right-0 top-full z-10 mt-3 w-full min-w-full max-w-sm rounded-3xl bg-white px-2 py-5 shadow-xl ring-1 ring-black ring-opacity-5 dark:bg-neutral-800  sm:min-w-[340px] sm:px-2 sm:py-2 ">
              <RadioGroup
                value={uiTimeframe}
                onChange={(value) => {
                  onChangeTimeframe(value as Timeframe)
                  close()
                }}
              >
                <RadioGroup.Label className="sr-only">
                  Select a timeframe
                </RadioGroup.Label>
                <div className="flex flex-col sm:flex-row sm:space-x-4">
                  {TIMEFRAMES.map((timeframe) => (
                    <RadioGroup.Option
                      key={timeframe.id}
                      value={timeframe}
                      disabled={hasPassed(timeframe.isoTime)}
                      className={({ checked }) =>
                        `${
                          checked
                            ? "bg-neutral-100 dark:bg-neutral-700"
                            : "bg-white dark:bg-neutral-800"
                        } relative z-10 flex flex-1 cursor-pointer items-center justify-center rounded-3xl py-3 text-sm font-semibold leading-none hover:bg-neutral-100 focus:outline-none data-[headlessui-state="disabled"]:cursor-not-allowed data-[headlessui-state="disabled"]:opacity-50`
                      }
                    >
                      {timeframe.time}
                    </RadioGroup.Option>
                  ))}
                </div>
              </RadioGroup>
            </Popover.Panel>
          </Transition>
        </>
      )}
    </Popover>
  )
}

export default SelectTimeframe
