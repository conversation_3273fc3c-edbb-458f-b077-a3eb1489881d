import { useState } from "react"

// Define a type for the function parameters if you haven't already
interface ConvertToWebPParams {
  imageFile: File
  quality?: number
}

const useConvertImageToWebP = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const convertToWebP = async ({
    imageFile,
    quality = 0.92,
  }: ConvertToWebPParams): Promise<Blob> => {
    setIsLoading(true)

    const convert = () =>
      new Promise<Blob>((resolve, reject) => {
        const url = URL.createObjectURL(imageFile)
        const img = new Image()
        img.onload = () => {
          const canvas = document.createElement("canvas")
          canvas.width = img.width
          canvas.height = img.height

          const ctx = canvas.getContext("2d")
          if (!ctx) {
            reject(new Error("Failed to get canvas context"))
            return
          }

          ctx.drawImage(img, 0, 0)

          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error("Failed to convert image to WebP"))
              }

              URL.revokeObjectURL(url)
            },
            "image/webp",
            quality
          )
        }
        img.onerror = () => {
          reject(new Error("Image loading error"))
        }
        img.src = url
      })

    try {
      const webPBlob = await convert()
      return webPBlob
    } catch (error) {
      console.error("Error converting image:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return { convertToWebP, isLoading }
}

export default useConvertImageToWebP
