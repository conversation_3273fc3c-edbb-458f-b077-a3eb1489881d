import { useCallback, useEffect, useRef } from "react"

export function useTimeoutFn(
  fn: () => void,
  ms: number = 0
): [() => boolean, () => void, () => void] {
  const ready = useRef<boolean>(false)
  const timeout = useRef<NodeJS.Timeout | null>(null)
  const callback = useRef<() => void>(fn)

  const isReady = useCallback(() => ready.current, [])

  const set = useCallback(() => {
    ready.current = false
    timeout.current && clearTimeout(timeout.current)
    timeout.current = setTimeout(() => {
      ready.current = true
      callback.current()
    }, ms)
  }, [ms])

  const clear = useCallback(() => {
    ready.current = false
    timeout.current && clearTimeout(timeout.current)
  }, [])

  // Update ref when the function changes
  useEffect(() => {
    callback.current = fn
  }, [fn])

  // Set on mount, clear on unmount
  useEffect(() => {
    set()
    return clear
  }, [clear, ms, set])

  return [isReady, clear, set]
}
