import { useEffect, useState } from "react"
import { useGetCitiesQuery } from "@/api/gql/generated"
import { EditorState } from "draft-js"
import { isEmpty } from "lodash"
import {
  boolean,
  custom,
  forward,
  instance,
  intersect,
  minLength,
  nullable,
  nullish,
  object,
  picklist,
  string,
  pipe
} from "valibot"

export const UserFormSchema = object({
  fullName: pipe(string("Per favore inserisci un nome."),
    minLength(1, "Per favore inserisci un nome.")
  ),
  email: pipe(string("Per favore inserisci un email."),
    minLength(1, "Per favore inserisci un email.")
  ),
  avatar: nullish(string()),
  bio: nullable(instance(EditorState)),
  dateOfBirth: pipe(string("Per favore inserisci una data di nascita."),
    minLength(10, "Per favore inserisci una data di nascita corretta."),
  ),
  city: string("Per favore inserisci una città."),
  isRemote: boolean(),
  telephone: pipe(string("Per favore inserisci un numero di telefono."),
    minLength(6, "Per favore inserisci un numero di telefono.")
  ),
  fiscalCode: nullable(string("Per favore inserisci un codice fiscale.")),
  taxId: nullable(string("Per favore inserisci una partita iva.")),
})

const useAccountDynamicFormSchema = () => {
  const [schema, setSchema] = useState<typeof UserFormSchema>(UserFormSchema)

  const {
    data: citiesData,
    isLoading,
    isError,
  } = useGetCitiesQuery(
    { name: "" },
    {
      select: (data) =>
        data?.cities?.collection
          ?.map((city) => city?.name)
          .filter((name): name is string => typeof name === "string") || [],
    }
  )

  useEffect(() => {
    if (!isLoading && !isError && citiesData) {
      const citySchema = object({
        city: picklist(citiesData, "Seleziona una città dalla lista."),
      })

      const dynamicFormSchema = intersect([UserFormSchema, citySchema])

      setSchema(dynamicFormSchema as any)
    }
  }, [citiesData, isLoading, isError])

  return schema
}

export default useAccountDynamicFormSchema
