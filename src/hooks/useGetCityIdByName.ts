import { useEffect, useState } from "react"
import { useGetCitiesQuery } from "@/api/gql/generated"

const useCityIdByName = (cityName: string) => {
  const [cityId, setCityId] = useState<string | undefined>(undefined)

  const { data, isLoading, error } = useGetCitiesQuery(
    {
      name: cityName,
    },
    {
      select: (data) => {
        const city = data?.cities?.collection?.find(
          (city) => city?.name.toLowerCase() === cityName.toLowerCase()
        )

        return city?.id
      },
    }
  )

  useEffect(() => {
    setCityId(undefined)
    if (data) {
      setCityId(data)
    }
  }, [data, isLoading, error, cityName])

  return { cityId, isLoading, error }
}

export default useCityIdByName
