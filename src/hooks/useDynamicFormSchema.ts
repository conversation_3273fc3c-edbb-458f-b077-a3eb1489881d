import { useEffect, useState } from "react"
import { useGetCitiesQuery } from "@/api/gql/generated"
import {
  boolean,
  custom,
  email,
  forward,
  maxLength,
  intersect,
  minLength,
  nullable,
  object,
  picklist,
  string,
  pipe,
  value
} from "valibot"
var capitalize = require('capitalize')

const baseFormSchema = object({
  fullName: pipe(string("Per favore inserisci un nome."),
    minLength(1, "Per favore inserisci un nome.")
  ),
  email: pipe(string("Per favore inserisci una mail corretta."),
    minLength(1, "Per favore inserisci una mail."),
    email("Per favore inserisci una mail corretta.")
  ),
  password: pipe(string("Per favore inserisci una password."),
    minLength(8, "Per favore inserisci una password più lunga."),
    maxLength(40, "Per favore inserisci una password più corta.")
  ),
  dateOfBirth: pipe(string("Per favore inserisci una data di nascita."),
    minLength(10, "Per favore inserisci una data di nascita corretta.")
  ),
  city: string("Per favore inserisci una città."),
  remote: boolean(),
  image: nullable(string()),
  telephone: pipe(string("Per favore inserisci un numero di telefono."),
    minLength(6, "Per favore inserisci un numero di telefono.")
  ),
  acceptedConditions: pipe(boolean("Per favore accetta i termini e le condizioni."),
    value(true, "Per favore accetta i termini e le condizioni.")
  ),
})

const useDynamicFormSchema = () => {
  const [schema, setSchema] = useState<typeof baseFormSchema>(baseFormSchema)

  const {
    data: citiesData,
    isLoading,
    isError,
  } = useGetCitiesQuery(
    { name: "" },
    {
      select: (data) =>
        data?.cities?.collection
          ?.map((city) => capitalize.words(city?.name))
          .filter((name): name is string => typeof name === "string") || [],
    }
  )

  useEffect(() => {
    if (!isLoading && !isError && citiesData) {
      // Dynamically create the picklist with actual city names
      const citySchema = object({
        city: picklist(citiesData, "Seleziona una città dalla lista."),
      })

      // Here we merge the schemas without directly updating the state
      const dynamicFormSchema = intersect([baseFormSchema, citySchema])

      setSchema(dynamicFormSchema as any)
    }
  }, [citiesData, isLoading, isError])

  return schema
}

export default useDynamicFormSchema
