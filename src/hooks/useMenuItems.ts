import ncNanoId from "@/utils/ncNanoId"
import { IdentificationIcon, UserCircleIcon } from "@heroicons/react/24/outline"

import { NAVIGATION } from "@/data/navigation"

import { useCurrentUser } from "./useCurrentUser"

export const useMenuItems = () => {
  const user = useCurrentUser()
  let navigationElements = []
  if (user) {
    navigationElements = [
      ...NAVIGATION,
      {
        id: ncNanoId(),
        name: "Account",
        href: "/account",
        icon: UserCircleIcon,
      },
    ]
  } else {
    navigationElements = [
      ...NAVIGATION,
      {
        id: ncNanoId(),
        name: "<PERSON><PERSON><PERSON>",
        href: "/auth/registrazione",
        icon: IdentificationIcon,
      },
      {
        id: ncNanoId(),
        name: "<PERSON><PERSON>",
        href: "/auth/login",
        icon: UserCircleIcon,
      },
    ]
  }
  return navigationElements
}
