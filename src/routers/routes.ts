/**
 * An array of routes that are accessible to the public
 * These routes do not require authentication DEFAULT_LOGIN_REDIRECT
 * @type {string[]}
 */
export const publicRoutes = ["/", "/auth/new-verification"]

/**
 * An array of routes that are used for authentication
 * These routes will redirect logged in users to
 * @type {string[]}
 */
export const authRoutes = [
  "/auth/login",
  "/auth/registrazione",
  "/auth/registrazione/successo",
  "/auth/error",
  "/auth/reset",
  "/auth/new-password",
]

/**
 * An array of routes that are accessible to authenticated users
 * These routes require authentication
 * @type {string[]}
 */
export const privateRoutes = [
  "/account",
  "/account/politiche-di-servizio",
  "/account-billing",
  "/account/appuntamenti",
  "/account/servizi",
  "/appointment-payment",
  "/partner-plan"
]

/**
 * The prefix for API authentication routes
 * Routes that start with this prefix are used for API authentication purposes. They are available to the public.
 * @type {string}
 */
export const apiAuthPrefix = "/api/auth"

/**
 * The default redirect path after logging in
 * @type {string}
 */
export const DEFAULT_LOGIN_REDIRECT = "/account"
