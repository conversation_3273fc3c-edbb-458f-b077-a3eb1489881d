export const changePasswordByTokenConsultant = async (
  token: string,
  password: string
) => {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL!, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    cache: "no-cache",
    body: JSON.stringify({
      query: `
            mutation changePasswordByTokenConsultant {
                changePasswordByTokenConsultant(input: {token: "${token}", password: "${password}"}) {
                    consultant {
                        id
                    }
                }
            }
            `,
    }),
  })
  const response = await res.json()
  if (response.errors) {
    throw new Error(response.errors[0].message)
  }
}
