export const getUserByEmailAndPassword = async (
  email: string,
  password: string
) => {
  const authResponse = await fetch(
    process.env.NEXT_PUBLIC_GRAPHQL_URL as string,
    {
      method: "POST",
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: `
              mutation {
                loginConsultantAuth(input: { email: "${email}", password: "${password}" }) {
                  consultantA<PERSON> {
                    token
                  }
                }
              }
            `,
      }),
    }
  )
  const authData = await authResponse.json()
  if (authData.errors) {
    throw new Error(authData.errors[0].message)
  }

  if (authData?.data?.loginConsultantAuth?.consultantAuth?.token) {
    return {
      email: email,
      token: authData.data.loginConsultantAuth.consultantAuth.token,
    }
  } else {
    return null
  }
}
