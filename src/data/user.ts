export const getUserByEmailAndPassword = async (
  email: string,
  password: string
) => {
  // Demo user for development - remove this in production
  if (process.env.NODE_ENV === 'development' && email === '<EMAIL>' && password === 'demopassword123') {
    return {
      email: email,
      token: 'demo-token-' + Date.now(), // Generate a demo token
    }
  }

  // Check if GraphQL URL is configured
  if (!process.env.NEXT_PUBLIC_GRAPHQL_URL) {
    throw new Error("GraphQL URL not configured. Please set NEXT_PUBLIC_GRAPHQL_URL in your environment variables.")
  }

  const authResponse = await fetch(
    process.env.NEXT_PUBLIC_GRAPHQL_URL as string,
    {
      method: "POST",
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: `
              mutation {
                loginConsultantAuth(input: { email: "${email}", password: "${password}" }) {
                  consultant<PERSON><PERSON> {
                    token
                  }
                }
              }
            `,
      }),
    }
  )
  const authData = await authResponse.json()
  if (authData.errors) {
    throw new Error(authData.errors[0].message)
  }

  if (authData?.data?.loginConsultantAuth?.consultantAuth?.token) {
    return {
      email: email,
      token: authData.data.loginConsultantAuth.consultantAuth.token,
    }
  } else {
    return null
  }
}
