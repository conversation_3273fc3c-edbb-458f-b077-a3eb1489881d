export const sendResetTokenEmail = async (email: string) => {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL!, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    cache: "no-cache",
    body: JSON.stringify({
      query: `
            mutation requestChangePasswordByEmailConsultant {
                requestChangePasswordByEmailConsultant(input: {email: "${email}"}) {
                    consultant {
                        id
                    }
                }
            }
            `,
    }),
  })
  const response = await res.json()
  if (response.errors) {
    throw new Error(response.errors[0].message)
  }
}
