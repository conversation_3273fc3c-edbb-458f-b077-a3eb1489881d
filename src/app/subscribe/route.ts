import { NextResponse } from "next/server"
import * as v from "valibot"

const Input = v.object({
  captcha: v.string(),
})

const verifyEndpoint = "https://www.google.com/recaptcha/api/siteverify"

export async function POST(req: Request) {
  const requestBody = await req.json()
  const data = v.parse(Input, requestBody)
  const { captcha } = data

  const captchaResponse = await fetch(verifyEndpoint, {
    method: "POST",
    headers: { "Content-type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams({
      secret: process.env.RECAPTCHA_SECRET!,
      response: captcha,
    }),
  }).then((res) => res.json())

  if (!captchaResponse.success) {
    return new NextResponse(
      JSON.stringify({
        status: "error",
        error: captchaResponse["error-codes"][0],
      }),
      { status: 500 }
    )
  }

  return new NextResponse(JSON.stringify({ status: "ok" }), { status: 200 })
}
