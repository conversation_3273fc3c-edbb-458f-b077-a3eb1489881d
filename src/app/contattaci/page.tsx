import React, { FC } from "react"

import SocialsList from "@/shared/SocialsList"

import ContactForm from "./ContactForm"

export interface PageContactProps { }

const info = [
  {
    title: "✉️ EMAIL",
    desc: (
      <a href="mailto:<EMAIL>">
        <EMAIL>
      </a>
    ),
  },
  {
    title: "☎ NUMERO VERDE",
    desc: <a href="tel:800031941">800 031 941</a>,
  },
]

const PageContact: FC<PageContactProps> = ({ }) => {
  return (
    <div className={`nc-PageContact overflow-hidden`}>
      <div className="mb-24 lg:mb-32">
        <div className="mx-auto my-16 mb-4 w-full max-w-2xl text-center md:my-24">
          <h1 className="flex items-center justify-center text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
            Con<PERSON><PERSON>ci
          </h1>
          <h2 className="mt-2 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-3">
            <PERSON> bisogno di aiuto? Scrivici!
          </h2>
        </div>
        <div className="container mx-auto max-w-7xl">
          <div className="grid flex-shrink-0 grid-cols-1 gap-12 sm:grid-cols-2 ">
            <div className="max-w-sm space-y-8">
              {info.map((item, index) => (
                <div key={index}>
                  <h3 className="text-sm font-semibold uppercase tracking-wider dark:text-neutral-200">
                    {item.title}
                  </h3>
                  <span className="mt-2 block text-neutral-500 dark:text-neutral-400">
                    {item.desc}
                  </span>
                </div>
              ))}
              <div>
                <h3 className="text-sm font-semibold uppercase tracking-wider dark:text-neutral-200">
                  🌏 SOCIALS
                </h3>
                <SocialsList className="mt-2" />
              </div>
            </div>
            <div>
              <ContactForm />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PageContact
