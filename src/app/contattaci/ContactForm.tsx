"use client"

import { use, useEffect, useRef } from "react"
import { useCreateToAdminContactRequestMutation } from "@/api/gql/generated"
import { valibotResolver } from "@hookform/resolvers/valibot"
import Captcha from "react-google-recaptcha"
import { SubmitHandler, useForm } from "react-hook-form"
import { toast } from "sonner"
import { email, minLength, object, InferOutput, string, pipe } from "valibot"

import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import Textarea from "@/shared/Textarea"
import FormItem from "@/app/(Form)/FormItem"

export const FormSchema = object({
  name: pipe(string("Per favore inserisci un nome."),
    minLength(1, "Per favore inserisci un nome."),
  ),
  mail: pipe(string("Per favore inserisci una mail corretta."),
    minLength(1, "Per favore inserisci una mail."),
    email("Per favore inserisci una mail corretta."),
  ),
  message: pipe(string("Per favore inserisci un messaggio."),
    minLength(1, "Per favore inserisci un messaggio."),
  ),
  telephone: pipe(string("Per favore inserisci un numero di telefono."),
    minLength(6, "Per favore inserisci un numero di telefono."),
  ),
})

export type FormSchemaType = InferOutput<typeof FormSchema>

const ContactForm: React.FC = () => {
  const form = useForm<FormSchemaType>({
    resolver: valibotResolver(FormSchema),
    mode: "onSubmit",
  })

  const captchaRef = useRef<Captcha>(null)

  const { mutate, isSuccess, isError } =
    useCreateToAdminContactRequestMutation()

  const onSubmit: SubmitHandler<FormSchemaType> = async (data) => {
    const captcha = await captchaRef.current?.executeAsync()
    const res = await fetch("/subscribe", {
      method: "POST",
      body: JSON.stringify({ captcha }),
      headers: { "Content-type": "application/json" },
    })

    if (res.status === 200) {
      mutate({
        input: {
          name: data.name,
          email: data.mail,
          message: data.message,
          telephone: data.telephone,
        },
      })
    } else {
      toast.error("Errore di CAPTCHA.")
    }
  }

  useEffect(() => {
    if (isSuccess) {
      form.reset()
      toast.success("Il tuo messaggio è stato inviato.", {
        description: "Verrai ricontattato al più presto.",
      })
    }
  }, [form, isSuccess])

  useEffect(() => {
    if (isError) {
      toast.error("Errore nell'invio del messaggio, prova di nuovo.")
    }
  }, [isError])

  return (
    <form
      onSubmit={form.handleSubmit(onSubmit)}
      className="grid grid-cols-1 gap-3"
    >
      <FormItem
        label="Nome"
        visibleDesc={!!form.formState.errors.name}
        descClassName="text-red-500"
        desc={
          form.formState.errors.name
            ? form.formState.errors.name.message
            : "Inserisci il tuo nome"
        }
      >
        <Input
          placeholder="Nome"
          {...form.register("name")}
          isError={!!form?.formState?.errors?.name?.message}
        />
      </FormItem>
      <FormItem
        label="Email"
        visibleDesc={!!form.formState.errors.mail}
        descClassName="text-red-500"
        desc={
          form.formState.errors.mail
            ? form.formState.errors.mail.message
            : "Inserisci la tua email"
        }
      >
        <Input
          placeholder="Email"
          {...form.register("mail")}
          isError={!!form?.formState?.errors?.mail?.message}
        />
      </FormItem>
      <FormItem
        label="Telefono"
        desc="Inserisci il tuo numero di telefono"
        descClassName="text-red-500"
        visibleDesc={!!form.formState.errors.telephone}
      >
        <Input
          placeholder="Telefono"
          {...form.register("telephone")}
          isError={!!form?.formState?.errors?.telephone?.message}
        />
      </FormItem>
      <FormItem
        label="Messaggio"
        descClassName="text-red-500"
        visibleDesc={!!form.formState.errors.message}
        desc={
          form.formState.errors.message
            ? form.formState.errors.message.message
            : "Inserisci la tua email"
        }
      >
        <Textarea
          className="mt-1"
          rows={6}
          placeholder="Scrivi il tuo messaggio"
          isError={!!form?.formState?.errors?.message?.message}
          {...form.register("message")}
        />
      </FormItem>
      <div>
        <ButtonPrimary type="submit">Invia Messaggio</ButtonPrimary>
      </div>
      <Captcha
        ref={captchaRef}
        size="invisible"
        sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
      />
    </form>
  )
}

export default ContactForm
