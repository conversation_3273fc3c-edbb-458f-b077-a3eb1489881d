"use client"
import React, {FC, useEffect} from "react"
import Heading2 from "@/shared/Heading2"
import <PERSON> from "next/link";
import {sendGTMEvent} from "@next/third-parties/google";
import {GetCitiesQuery, GetConsultingServicesForLinksQuery} from "@/api/gql/generated";
import { usePathname } from "next/navigation";


interface SectionGridLinkCardServiceProps {
    data?: GetConsultingServicesForLinksQuery[] | GetCitiesQuery[]
    pageTitle?: string
    className?: string
}

const SectionGridLinksCard: FC<SectionGridLinkCardServiceProps> = ({
                                                                       className = "",
                                                                       data = [],
                                                                       pageTitle,
                                                                   }) => {
    const pathname = usePathname();

    useEffect(() => {
        sendGTMEvent({
            event: "view_service_city_list",
            view_items_list: data?.map((item: any) => {
                return {
                    item_name: item?.name,
                    id: item?.id
                }
            }),
        })
        if (typeof window !== "undefined" && window.fbq) {
            if (
                Array.isArray(data) && data.length > 0
            ) {
                try {
                    window.fbq("track", "ViewServiceCity", {
                        content_ids: data
                            .map((item: any) => item?.id),
                    })
                } catch (e) {
                    console.error(e)
                }
            }
        }
    }, [data]);

    return (
        <div className={`nc-SectionGridFilterCard ${className} space-y-6`}>
            <div className="mb-8 lg:mb-11 border border-neutral-200 rounded-xl p-8 px-10">
                {pageTitle ? (
                    <Heading2
                        className=" !mb-2"
                        heading={
                            <span>{pageTitle}</span>
                        }

                    />
                ) : null}
                <div className=" grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 ">
                    {data && data.length > 0 && data.map((item: any ,  index: number) => {
                        return (
                            <Link key={index + Math.random()} href={`${pathname}/${item.urlKey}`}>
                                <span className="block text-medium text-neutral-500 text-sm hover:underline">
                                      {item?.name}
                                </span>
                            </Link>
                        )
                    })}
                </div>
            </div>
        </div>
    )
}
export default SectionGridLinksCard;
