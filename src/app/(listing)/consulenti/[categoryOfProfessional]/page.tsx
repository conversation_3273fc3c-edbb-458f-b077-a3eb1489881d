import React, { <PERSON> } from "react"
import { notFound, redirect } from "next/navigation"
import {
    GetFilteredConsultantsQueryVariables,
    useContentsQuery,
    useGetFilteredConsultantsQuery,
    useGetTagsQuery,
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"

import SectionGridFilterCard from "../../SectionGridFilterCard"
import { capitalizeAndSeparateString } from "@/utils/stringUtils"

export interface ListingConsultantsByProfessionCategoryProps {
    params: {
        categoryOfProfessional: string
    }
    searchParams: {
        page: number
        itemsPerPage: number
        online: string
        tags?: string
        city?: string
    }
}

export async function generateMetadata({ params: { categoryOfProfessional } }: { params: { categoryOfProfessional: any } }) {

    if (!categoryOfProfessional) redirect("/")

    let query
    try {
        query = await serverFetch(useContentsQuery, {
            variables: {
                city_id: null,
                consultantProfession_urlKey: categoryOfProfessional,
                consultantService_urlKey: null
            },
            cache: "no-cache",
            select: (data) => {
                return data.contents?.collection?.[0]
            },
        })
    } catch (error) {
        notFound()
    }

    return {
        title: query?.metadataTitle ?? capitalizeAndSeparateString(categoryOfProfessional),
        description: query?.metadataDescription,
        metadataBase: new URL(process.env.NEXT_PUBLIC_WEBSITE_URL ?? ""),

        openGraph: {
            url: `/consulenti/${query?.consultantProfession?.urlKey}`,
            title: query?.metadataTitle ?? capitalizeAndSeparateString(categoryOfProfessional),
            description: query?.metadataDescription,
            siteName: "Consulente Ideale",
        }
    };
}

const ListingConsultantsByProfessionCategory: FC<ListingConsultantsByProfessionCategoryProps> = async ({
    params,
    searchParams,
}) => {
    if (!params.categoryOfProfessional) {
        return redirect("/")
    }
    const { online, tags } = searchParams
    const categoryOfProfessional = params.categoryOfProfessional;

    const variables: GetFilteredConsultantsQueryVariables = {
        professionsUrlKey: categoryOfProfessional,
        page: Number(searchParams.page) || 1,
        ...(online && { isOnline: online === "true" }),
        ...(tags && { tags: tags.split(",").map(Number) }),
    }
    if (searchParams.city) {
        variables.cityName = searchParams.city;
    }
    const data = await serverFetch(useGetFilteredConsultantsQuery, {
        cache: "no-cache",
        variables,
        select: (data) => data,
    })

    const tagsData = await serverFetch(useGetTagsQuery, {
        select: (data) =>
            data?.tags?.collection
                ?.map((tag) => {
                    if (!tag) return
                    return {
                        name: tag.name,
                        id: tag._id,
                    }
                })
                .filter((tag): tag is { name: string; id: number } => !!tag) || [],
    })

    const contentData = await serverFetch(useContentsQuery, {
        variables: {
            city_id: null,
            consultantProfession_urlKey: categoryOfProfessional,
            consultantService_urlKey: null
        },
        select: (data) => data.contents?.collection?.[0],
    })

    return (
        <div className="container relative">
            <SectionGridFilterCard
                className="py-8 lg:py-16"
                data={data}
                pageTitle={contentData?.title ?? capitalizeAndSeparateString(categoryOfProfessional)}
                params={{
                    queryParams: {
                        page: Number(searchParams.page) || 1,
                    },
                }}
                tags={tagsData || []}
                shouldShowCityFilter={true}
                htmlBody={contentData?.body ?? ""}
            />
        </div>
    )
}

export default ListingConsultantsByProfessionCategory
