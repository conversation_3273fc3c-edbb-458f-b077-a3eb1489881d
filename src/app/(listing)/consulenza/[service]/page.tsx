
import React, { <PERSON> } from "react"
import { notFound, redirect } from "next/navigation"
import {
    GetFilteredConsultantsQueryVariables,
    useContentsQuery, useGetCitiesQuery, useGetConsultingServiceIdByUrlKeyQuery,
    useGetFilteredConsultantsQuery,
    useGetTagsQuery, useRatingsForServicesQuery
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import SectionGridFilterCard from "../../SectionGridFilterCard"
import { capitalizeAndSeparateString } from "@/utils/stringUtils"
import SectionGridLinksCard from "@/app/(listing)/SectionGridLinksCard";
import DomModifier from "@/components/DomModifier";
import BackgroundSection from "@/components/BackgroundSection";
import SectionGridAuthorBox from "@/components/SectionGridAuthorBox";

export interface ListingConsultantsByServiceProps {
    params: {
        service: string
    }
    searchParams: {
        page: number
        itemsPerPage: number
        online: string
        tags?: string
        city?: string
    }
}

export async function generateMetadata({ params: { service } }: { params: { service: string } }) {

    if (!service) redirect("/")

    let query
    try {
        query = await serverFetch(useContentsQuery, {
            variables: {
                city_id: null,
                consultantService_urlKey: service,
                consultantProfession_urlKey: null
            },
            cache: "no-cache",
            select: (data) => {
                return data.contents?.collection?.[0]
            },
        })
    } catch (error) {
        notFound()
    }

    return {
        title: query?.metadataTitle ?? capitalizeAndSeparateString(service),
        description: query?.metadataDescription,
        metadataBase: new URL(process.env.NEXT_PUBLIC_WEBSITE_URL ?? ""),

        openGraph: {
            url: `/consulenza/${query?.consultantService?.urlKey}`,
            title: query?.metadataTitle ?? capitalizeAndSeparateString(service),
            description: query?.metadataDescription,
            siteName: "Consulente Ideale",
        },

    };
}

const ListingConsultantsByService: FC<ListingConsultantsByServiceProps> = async ({
    params,
    searchParams,
}) => {
    if (!params.service) {
        return redirect("/")
    }
    const { online, tags } = searchParams
    const service = params.service;

    const variables: GetFilteredConsultantsQueryVariables = {
        serviceUrlKey: service,
        page: Number(searchParams.page) || 1,
        ...(online && { isOnline: online === "true" }),
        ...(tags && { tags: tags.split(",").map(Number) }),
    }
    if (searchParams.city) {
        variables.cityName = searchParams.city;
    }
    const data = await serverFetch(useGetFilteredConsultantsQuery, {
        cache: "no-cache",
        variables,
        select: (data) => data,
    })
    


    const tagsData = await serverFetch(useGetTagsQuery, {
        select: (data) =>
            data?.tags?.collection
                ?.map((tag) => {
                    if (!tag) return
                    return {
                        name: tag.name,
                        id: tag._id,
                    }
                })
                .filter((tag): tag is { name: string; id: number } => !!tag) || [],
    })

    const citiesData = await serverFetch(useGetCitiesQuery, {
        select: (data: any) => data?.cities?.collection ?? []
    })

    const contentData = await serverFetch(useContentsQuery, {
        cache: "no-cache",
        variables: {
            city_id: null,
            consultantService_urlKey: service,
            consultantProfession_urlKey: null
        },
        select: (data) => data.contents?.collection?.[0],
    })

    const serviceId: any = await serverFetch(useGetConsultingServiceIdByUrlKeyQuery, {
        cache: "no-cache",
        variables: { urlKey: params.service },
        select: (data) => data?.consultingServices?.collection?.[0],
    });

    let stat: any
    try {
            stat = await serverFetch(useRatingsForServicesQuery, {
                variables: { serviceId: serviceId?._id },
                select: (data) => data?.statsByServiceIdServiceReviewStatistic,
            });
    } catch (error) {
        stat = null;
    }

    const ldJson = stat ? {
        "@context": "https://schema.org",
        "@type": "Product",
        name: stat?.service?.name,
        aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: Number(stat.averageRating)?.toFixed(1),
            reviewCount: stat.reviewCount?.toString(),
            bestRating: stat.bestRating?.toString(),
            worstRating: stat.worstRating?.toString(),
        },
    } : null;

    return (
        <div className="container relative">
            {ldJson && (
                <script
                    type="application/ld+json"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify(ldJson)
                    }}
                />
            )}
            <DomModifier />
            <SectionGridFilterCard
                className="py-8 lg:py-16"
                data={data}
                pageTitle={contentData?.title ?? capitalizeAndSeparateString(service)}
                params={{
                    queryParams: {
                        page: Number(searchParams.page) || 1,
                    },
                }}
                tags={tagsData || []}
                shouldShowCityFilter={true}
                htmlBody={contentData?.body ?? ""}
            />
            <div className="overflow-hidden">
                {/* SECTION */}
                <div className="relative mb-24 py-16 lg:mb-28">
                    <BackgroundSection className="bg-neutral-100 dark:bg-black dark:bg-opacity-20 " />
                    <SectionGridAuthorBox />
                </div>
            </div>
            <SectionGridLinksCard
                className="py-8 lg:py-16"
                data={citiesData ?? []}
                pageTitle="Città"
            />
        </div>
    )
}

export default ListingConsultantsByService
