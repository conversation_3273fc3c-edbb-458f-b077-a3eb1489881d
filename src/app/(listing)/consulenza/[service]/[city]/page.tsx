import React, { FC } from "react"
import { notFound, redirect } from "next/navigation"
import {
    GetFilteredConsultantsQueryVariables,
    useContentsQuery,
    useGetFilteredConsultantsQuery,
    useGetTagsQuery,
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import SectionGridFilterCard from "../../../SectionGridFilterCard"
import { capitalizeAndSeparateString } from "@/utils/stringUtils"
var capitalize = require('capitalize')

export interface ListingConsultantsByServiceProps {
    params: {
        service: string
        city: string
    }
    searchParams: {
        page: number
        itemsPerPage: number
        online: string
        tags?: string
    }
}

export async function generateMetadata({ params: { service, city } }: { params: { service: any, city: any } }) {

    if (!service) redirect("/")

    let query
    try {
        query = await serverFetch(useContentsQuery, {
            variables: {
                city_name: city,
                consultantService_urlKey: service,
                consultantProfession_urlKey: null
            },
            cache: "no-cache",
            select: (data) => {
                return data.contents?.collection?.[0]
            },
        })
    } catch (error) {
        notFound()
    }

    return {
        title: query?.metadataTitle ?? capitalizeAndSeparateString(service),
        description: query?.metadataDescription,
        metadataBase: new URL(process.env.NEXT_PUBLIC_WEBSITE_URL ?? ""),

        openGraph: {
            url: `/consulenza/${query?.consultantService?.urlKey}/${city}`,
            title: query?.metadataTitle ?? capitalizeAndSeparateString(service),
            description: query?.metadataDescription,
            siteName: "Consulente Ideale",
        }
    };
}

const ListingConsultantsByService: FC<ListingConsultantsByServiceProps> = async ({
    params,
    searchParams,
}) => {
    if (!params.service) {
        return redirect("/")
    }
    const { online, tags } = searchParams
    const service = params.service;
    const city = capitalize.words(params.city.replace(/-/g, " "));

    const variables: GetFilteredConsultantsQueryVariables = {
        serviceUrlKey: service,
        cityName: city,
        page: Number(searchParams.page) || 1,
        ...(online && { isOnline: online === "true" }),
        ...(tags && { tags: tags.split(",").map(Number) }),
    }

    const data = await serverFetch(useGetFilteredConsultantsQuery, {
        cache: "no-cache",
        variables,
        select: (data) => data,
    })

    const tagsData = await serverFetch(useGetTagsQuery, {
        select: (data) =>
            data?.tags?.collection
                ?.map((tag) => {
                    if (!tag) return
                    return {
                        name: tag.name,
                        id: tag._id,
                    }
                })
                .filter((tag): tag is { name: string; id: number } => !!tag) || [],
    })

    const contentData = await serverFetch(useContentsQuery, {
        variables: {
            city_name: city,
            consultantService_urlKey: service,
            consultantProfession_urlKey: null
        },
        select: (data) => data.contents?.collection?.[0],
    })

    return (
        <div className="container relative">
            <SectionGridFilterCard
                className="py-8 lg:py-16"
                data={data}
                pageTitle={`${contentData?.title ?? capitalizeAndSeparateString(service)} - ${city}`}
                params={{
                    queryParams: {
                        page: Number(searchParams.page) || 1,
                    },
                }}
                tags={tagsData || []}
                htmlBody={contentData?.body ?? ""}
            />
        </div>
    )
}

export default ListingConsultantsByService
