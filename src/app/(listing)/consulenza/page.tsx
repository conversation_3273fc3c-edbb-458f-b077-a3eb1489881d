import React, {<PERSON>} from "react";
import {
    useGetConsultingServicesForLinksQuery,
} from "@/api/gql/generated";
import SectionGridLinksCard from "@/app/(listing)/SectionGridLinksCard";
import {serverFetch} from "@/api/gql/queryUtils";

const ListingConsultantsByService: FC = async () => {
    const data = await serverFetch(useGetConsultingServicesForLinksQuery, {
        cache: "no-cache",
        select: (data: any) => data?.consultingServices?.collection ?? []
    })


    return (
        <div className="container relative">
            <SectionGridLinksCard
                className="py-8 lg:py-16"
                data={data ?? []}
                pageTitle="Servizi"
            />
        </div>
    );
};

export default ListingConsultantsByService;
