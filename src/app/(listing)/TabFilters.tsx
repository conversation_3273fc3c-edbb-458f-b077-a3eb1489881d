"use client"

import React, { useEffect, useState } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { useGetCitiesQuery, useGetTagsQuery } from "@/api/gql/generated"
import { XMarkIcon } from "@heroicons/react/24/outline"

import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

const ToggleSwitch = ({
  label,
  value,
  onChange,
}: {
  label: string
  value: boolean
  onChange: (value: boolean) => void
}) => {
  const renderXClear = () => (
    <span className="ml-3 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-primary-500 text-white">
      <XMarkIcon className="h-3 w-3" />
    </span>
  )

  return (
    <div
      className={`flex cursor-pointer items-center justify-center rounded-full border px-4 py-2 text-sm transition-all focus:outline-none ${value
        ? "border-primary-500 text-primary-700"
        : "border-neutral-300 dark:border-neutral-700"
        }`}
      onClick={() => onChange(!value)}
    >
      <span>{label}</span>
      {value && renderXClear()}
    </div>
  )
}

const CitiesFilter = () => {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const { data: citiesData, isLoading } = useGetCitiesQuery(
    {},
    {
      select: (data) =>
        data?.cities?.collection
          ?.map((city) => {
            if (!city) return
            return {
              name: city.name,
              id: city.id,
            }
          })
          .filter((city): city is { name: string; id: string } => !!city) || [],
    }
  )

  const onChange = (cityName: string) => {
    const newSearchParams = new URLSearchParams(searchParams.toString())
    if (cityName) {
      newSearchParams.set("city", cityName)
    } else {
      newSearchParams.delete("city")
    }
    newSearchParams.delete("page")
    router.push(`${pathname}?${newSearchParams.toString()}`)
  }
  const [selectedCity, setSelectedCity] = useState(
    searchParams.get("city") || ""
  )

  useEffect(() => {
    setSelectedCity(searchParams.get("city") || "")
  }, [searchParams])

  return (
    <Select onValueChange={onChange} value={selectedCity}>
      <SelectTrigger className="min-w-[180px]">
        <SelectValue placeholder="Filtra per città" />
      </SelectTrigger>
      <SelectContent side="bottom" avoidCollisions={false}>
        <SelectGroup>
          {citiesData?.sort((a, b) => a.name.localeCompare(b.name))?.map((city) => (
            <SelectItem value={city.name} key={city.name}>
              {city.name}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

const TabFilters = ({
  shouldShowCityFilter,
}: {
  shouldShowCityFilter: boolean
}) => {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const { data: tagsData, isLoading } = useGetTagsQuery(
    {},
    {
      select: (data) =>
        data?.tags?.collection
          ?.map((tag) => {
            if (!tag) return
            return {
              name: tag.name,
              id: tag._id,
            }
          })
          .filter((tag): tag is { name: string; id: number } => !!tag) || [],
    }
  )

  const activeTags = searchParams.get("tags")?.split(",")?.filter(Boolean) || []

  const toggleTag = (tagId: number) => {
    const newTagsSet = new Set(activeTags)
    const tagIdStr = tagId.toString()

    if (newTagsSet.has(tagIdStr)) {
      newTagsSet.delete(tagIdStr)
    } else {
      newTagsSet.add(tagIdStr)
    }

    const newTags = Array.from(newTagsSet)
    const newSearchParams = new URLSearchParams(searchParams.toString())
    if (newTags.length > 0) {
      newSearchParams.set("tags", newTags.join(","))
    } else {
      newSearchParams.delete("tags")
    }
    newSearchParams.delete("page")

    router.push(`${pathname}?${newSearchParams.toString()}`)
  }

  const deleteAllFilters = () => {
    const newSearchParams = new URLSearchParams(searchParams.toString())
    newSearchParams.delete("tags")
    newSearchParams.delete("city")
    newSearchParams.delete("page")
    router.push(`${pathname}?${newSearchParams.toString()}`)
  }

  return (
    <div className="flex flex-wrap gap-4 lg:items-center">
      <div className="flex flex-wrap gap-4">
        {tagsData?.map((tag) => (
          <ToggleSwitch
            key={tag?.id}
            label={tag?.name}
            value={activeTags.includes(tag.id.toString())}
            onChange={() => toggleTag(tag.id)}
          />
        ))}
        <div className="max-w-lg">{shouldShowCityFilter && <CitiesFilter />}</div>
      </div>
      {activeTags.length || searchParams.get("city") ? (
        <Button
          className="flex cursor-pointer items-center justify-center rounded-full border border-neutral-300 bg-secondary-700 px-4 py-2 text-sm text-white shadow-none transition-all focus:outline-none dark:border-neutral-700"
          onClick={deleteAllFilters}
        >
          Rimuovi tutti i filtri
        </Button>
      ) : null}
    </div>
  )
}

export default TabFilters
