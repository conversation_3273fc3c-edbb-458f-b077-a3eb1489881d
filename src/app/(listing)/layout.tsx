import React, { ReactNode } from "react"

import BackgroundSection from "@/components/BackgroundSection"
import BgGlassmorphism from "@/components/BgGlassmorphism"
import SectionGridAuthorBox from "@/components/SectionGridAuthorBox"
import SectionSliderNewCategories from "@/components/SectionSliderNewCategories"
import SectionSubscribe2 from "@/components/SectionSubscribe2"

import SectionHero2ArchivePage from "../(server-components)/SectionHero2ArchivePage"
import SectionHeroArchivePage from "../(server-components)/SectionHeroArchivePage"

const Layout = ({ children }: { children: ReactNode }) => {
  return (
    <div className="nc-ListingRealEstateMapPage  relative ">
      {/* <BgGlassmorphism /> */}

      {/* SECTION HERO
      <div className="container pb-24 pt-10 lg:pb-28 lg:pt-16">
        <SectionHeroArchivePage />
      </div> */}

      {children}

      <div className="container overflow-hidden top-10-section">
        {/* SECTION */}
        <div className="relative mb-24 py-16 lg:mb-28">
          <BackgroundSection className="bg-neutral-100 dark:bg-black dark:bg-opacity-20 " />
          <SectionGridAuthorBox />
        </div>
      </div>
    </div>
  )
}

export default Layout
