import React, { <PERSON> } from "react"
import { notFound, redirect } from "next/navigation"
import {
  useGetFilteredConsultantsQuery,
  useGetTagsQuery,
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import { getListConsultantsParams } from "@/utils/getListConsultantsParams"
import { isEmpty } from "lodash"

import SectionGridFilterCard from "../../SectionGridFilterCard"

export interface ListingRealEstatePageProps {
  params: {
    listing: string[]
  }
  searchParams: {
    page: number
    itemsPerPage: number
    online: string
    tags?: string
    city?: string
  }
}

const ListingRealEstatePage: FC<ListingRealEstatePageProps> = async ({
  params,
  searchParams,
}) => {
  if (isEmpty(params.listing)) {
    return redirect("/")
  }
  const { online, tags, city: cityParam } = searchParams
  const pathname = `/ricerca/${params.listing.join("/")}`

  const consultantParams = await getListConsultantsParams(pathname)

  if (!consultantParams) {
    return notFound()
  }

  const variables = {
    city: cityParam,
    cityId: consultantParams?.url?.city,
    macroService: consultantParams?.url?.consultingServiceCategory,
    service: consultantParams?.url?.consultingService,
    profession: consultantParams?.url?.consultantProfession,
    page: Number(searchParams.page) || 1,
    ...(online && { isOnline: online === "true" }),
    ...(tags && { tags: tags.split(",").map(Number) }),
  }

  const data = await serverFetch(useGetFilteredConsultantsQuery, {
    cache: "no-cache",
    variables,
    select: (data) => data,
  })

  const tagsData = await serverFetch(useGetTagsQuery, {
    select: (data) =>
      data?.tags?.collection
        ?.map((tag) => {
          if (!tag) return
          return {
            name: tag.name,
            id: tag._id,
          }
        })
        .filter((tag): tag is { name: string; id: number } => !!tag) || [],
  })

  return (
    <div className="container relative">
      <SectionGridFilterCard
        className="py-8 lg:py-16"
        data={data}
        pageTitle={consultantParams?.url?.id}
        params={{
          queryParams: {
            page: Number(searchParams.page) || 1,
          },
        }}
        tags={tagsData || []}
        shouldShowCityFilter={!consultantParams?.url?.city}
      />
    </div>
  )
}

export default ListingRealEstatePage
