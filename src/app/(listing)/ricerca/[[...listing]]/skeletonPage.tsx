import React, { <PERSON> } from "react"

import { Skeleton } from "@/components/ui/skeleton"
import ProfessionalCardSkeleton from "@/components/ProfessionalCardSkeleton"

const SkeletonPage: FC = () => {
  return (
    <div className="container relative">
      <div className={`nc-SectionGridFilterCard space-y-18 py-8 lg:py-16`}>
        <div className="mb-12 flex flex-col lg:mb-16">
          <Skeleton className="h-10 w-[360px] rounded-md text-primary-500 dark:text-neutral-400" />
          <Skeleton className="mt-3 block h-8 w-[120px] rounded-md text-primary-500 dark:text-neutral-400" />
        </div>
        <div className="grid grid-cols-1 gap-6 md:gap-8 xl:grid-cols-2 ">
          {["1", "2"]?.map((professional) => (
            <ProfessionalCardSkeleton key={professional} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default SkeletonPage
