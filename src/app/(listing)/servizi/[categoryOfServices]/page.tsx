import React, { <PERSON> } from "react"
import { redirect } from "next/navigation"
import {
    GetFilteredConsultantsQueryVariables,
    useGetFilteredConsultantsQuery,
    useGetTagsQuery,
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import SectionGridFilterCard from "../../SectionGridFilterCard"
import { capitalizeAndSeparateString } from "@/utils/stringUtils"

export interface ListingConsultantsByCategoryOfServicesProps {
    params: {
        categoryOfServices: string
    }
    searchParams: {
        page: number
        itemsPerPage: number
        online: string
        tags?: string
        city?: string
    }
}

const ListingConsultantsByCategoryOfServices: FC<ListingConsultantsByCategoryOfServicesProps> = async ({
    params,
    searchParams,
}) => {
    if (!params.categoryOfServices) {
        return redirect("/")
    }
    const { online, tags } = searchParams
    const categoryOfServices = params.categoryOfServices;

    const variables: GetFilteredConsultantsQueryVariables = {
        serviceCategoryUrlKey: categoryOfServices,
        page: Number(searchParams.page) || 1,
        ...(online && { isOnline: online === "true" }),
        ...(tags && { tags: tags.split(",").map(Number) }),
    }
    if (searchParams.city) {
        variables.cityName = searchParams.city;
    }
    const data = await serverFetch(useGetFilteredConsultantsQuery, {
        cache: "no-cache",
        variables,
        select: (data) => data,
    })

    const tagsData = await serverFetch(useGetTagsQuery, {
        select: (data) =>
            data?.tags?.collection
                ?.map((tag) => {
                    if (!tag) return
                    return {
                        name: tag.name,
                        id: tag._id,
                    }
                })
                .filter((tag): tag is { name: string; id: number } => !!tag) || [],
    })

    return (
        <div className="container relative">
            <SectionGridFilterCard
                className="py-8 lg:py-16"
                data={data}
                pageTitle={`${capitalizeAndSeparateString(categoryOfServices)}`}
                params={{
                    queryParams: {
                        page: Number(searchParams.page) || 1,
                    },
                }}
                tags={tagsData || []}
                shouldShowCityFilter={true}
            />
        </div>
    )
}

export default ListingConsultantsByCategoryOfServices
