import React, { <PERSON> } from "react"
import { redirect } from "next/navigation"
import {
    GetFilteredConsultantsQueryVariables,
    useGetFilteredConsultantsQuery,
    useGetTagsQuery,
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import SectionGridFilterCard from "../../../SectionGridFilterCard"
import { capitalizeAndSeparateString } from "@/utils/stringUtils"
var capitalize = require('capitalize')

export interface ListingConsultantsByCategoryOfServicesAndCityProps {
    params: {
        categoryOfServices: string
        city: string
    }
    searchParams: {
        page: number
        itemsPerPage: number
        online: string
        tags?: string
    }
}

const ListingConsultantsByCategoryOfServicesAndCity: FC<ListingConsultantsByCategoryOfServicesAndCityProps> = async ({
    params,
    searchParams,
}) => {
    if (!params.categoryOfServices) {
        return redirect("/")
    }
    const { online, tags } = searchParams
    const categoryOfServices = params.categoryOfServices;
    const city = capitalize.words(params.city.replace(/-/g, " "));

    const variables: GetFilteredConsultantsQueryVariables = {
        serviceCategoryUrlKey: categoryOfServices,
        cityName: city,
        page: Number(searchParams.page) || 1,
        ...(online && { isOnline: online === "true" }),
        ...(tags && { tags: tags.split(",").map(Number) }),
    }

    const data = await serverFetch(useGetFilteredConsultantsQuery, {
        cache: "no-cache",
        variables,
        select: (data) => data,
    })

    const tagsData = await serverFetch(useGetTagsQuery, {
        select: (data) =>
            data?.tags?.collection
                ?.map((tag) => {
                    if (!tag) return
                    return {
                        name: tag.name,
                        id: tag._id,
                    }
                })
                .filter((tag): tag is { name: string; id: number } => !!tag) || [],
    })

    return (
        <div className="container relative">
            <SectionGridFilterCard
                className="py-8 lg:py-16"
                data={data}
                pageTitle={`${capitalizeAndSeparateString(categoryOfServices)} - ${city}`}
                params={{
                    queryParams: {
                        page: Number(searchParams.page) || 1,
                    },
                }}
                tags={tagsData || []}
                shouldShowCityFilter={true}
            />
        </div>
    )
}

export default ListingConsultantsByCategoryOfServicesAndCity
