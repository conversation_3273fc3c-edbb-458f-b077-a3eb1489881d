"use client"

import path from "path"
import React, { FC, useEffect, useMemo, useRef, useState } from "react"
import { useParams } from "next/navigation"
import {
  Consultant,
  GetFilteredConsultantsQuery,
} from "@/api/gql/generated"
import { isSearchLoading } from "@/store/search"
import { capitalizeAndSeparateString } from "@/utils/stringUtils"
import { sendGTMEvent } from "@next/third-parties/google"
import { useAtom } from "jotai"

import Breadcrumb from "@/shared/Breadcrumbs"
import Heading2 from "@/shared/Heading2"
import Pagination from "@/shared/Pagination"
import PropertyCardH from "@/components/PropertyCardH"

import TabFilters from "./TabFilters"
import sanitizeHtml from "sanitize-html"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { UsersIcon } from "@heroicons/react/24/outline"
import { ChevronDoubleRightIcon } from "@heroicons/react/24/solid"

export interface SectionGridFilterCardProps {
  data: GetFilteredConsultantsQuery
  className?: string
  params: {
    queryParams?: {
      page: number
      isOnline?: boolean
    }
  }
  tags: {
    name: string
    id: number
  }[]
  shouldShowCityFilter?: boolean
  pageTitle?: string
  bodyContent?: string
  htmlBody?: string
}

const SectionGridFilterCard: FC<SectionGridFilterCardProps> = ({
  className = "",
  params,
  data,
  pageTitle,
  bodyContent,
  shouldShowCityFilter,
  htmlBody
}) => {
  const totalCount = data?.listConsultants?.paginationInfo?.totalCount
  const pathNames = useParams()
  const [, setIsLoading] = useAtom(isSearchLoading)
  const [showMore, setShowMore] = useState(false);

  const breadcrumbBuilder = useMemo(() => {
    if (pathNames.listing) {
      if (Array.isArray(pathNames.listing)) {
        const breadcrumbArray = pathNames.listing.map((pathName, index) => {
          return {
            label: capitalizeAndSeparateString(pathName),
            path: path.join("/ricerca", ...pathNames.listing.slice(0, index + 1)),
          }
        })
        return breadcrumbArray
      } else {
        return [
          {
            label: capitalizeAndSeparateString(pathNames.listing),
            path: `/ricerca/${pathNames.listing}`,
          },
        ]
      }
    }
    else if (pathNames.categoryOfProfessional) {
      const list = [
        {
          label: capitalizeAndSeparateString(pathNames.categoryOfProfessional.toString()),
          path: `/consulenti/${pathNames.categoryOfProfessional}`,
        }
      ]
      if (pathNames.city) {
        list.push({
          label: capitalizeAndSeparateString(pathNames.city.toString()),
          path: `/consulenti/${pathNames.categoryOfProfessional}/${pathNames.city}`,
        })
      }
      return list
    }
    else if (pathNames.service) {
      const list = [
        {
          label: capitalizeAndSeparateString(pathNames.service.toString()),
          path: `/consulenza/${pathNames.service}`,
        }
      ]
      if (pathNames.city) {
        list.push({
          label: capitalizeAndSeparateString(pathNames.city.toString()),
          path: `/consulenza/${pathNames.service}/${pathNames.city}`,
        })
      }
      return list
    }
    else if (pathNames.categoryOfServices) {
      const list = [
        {
          label: capitalizeAndSeparateString(pathNames.categoryOfServices.toString()),
          path: `/servizi/${pathNames.categoryOfServices}`,
        }
      ]
      if (pathNames.city) {
        list.push({
          label: capitalizeAndSeparateString(pathNames.city.toString()),
          path: `/servizi/${pathNames.categoryOfServices}/${pathNames.city}`,
        })
      }
      return list
    }
  }, [pathNames])

  useEffect(() => {
    setIsLoading(false)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    sendGTMEvent({
      event: "view_item_list",
      view_items_list: data?.listConsultants?.collection?.map((item) => {
        return {
          item_id: item?._id,
          item_name: item?.fullName,
        }
      }),
    })
    if (typeof window !== "undefined" && window.fbq) {
      if (
        data?.listConsultants?.collection &&
        Array.isArray(data?.listConsultants?.collection)
      ) {
        try {
          window.fbq("track", "ViewCategory", {
            content_ids: data.listConsultants.collection
              .map((item) => item?._id)
              .filter((item) => typeof item === "number"),
            content_category: sanitizeHtml(pageTitle ?? "", {
              allowedTags: [],
              allowedAttributes: {},
            }),
          })
        } catch (e) {
          console.error(e)
        }
      }
    }
  }, [data?.listConsultants?.collection, pathNames.listing])


  return (
    <div className={`nc-SectionGridFilterCard ${className} space-y-6`}>
      <div className="border rounded-xl border-neutral-200 p-8 px-10">
        {pageTitle ? (
          <Heading2
            className=" !mb-2"
            heading={
              <span>{pageTitle}</span>
            }

          />
        ) : null}
        <Breadcrumb items={breadcrumbBuilder} />
        <div className="w-32 border-b border-neutral-200 dark:border-neutral-700 pb-2"></div>

        {htmlBody ?
          <Accordion type="single" collapsible className="w-full mt-3">
            <AccordionItem value="item-1" className="border-none">
              {!showMore &&
                <div
                  className={`prose ${showMore ? "block" : "line-clamp-3"} max-w-full prose-h1:text-lg prose-headings:font-medium prose-headings:my-2 prose-h2:text-base prose-h3:text-base prose-p:text-sm prose-p:mt-1 prose-headings:text-neutral-700 text-neutral-500 dark:text-neutral-400 ${!showMore ? "gradient-overlay" : ""
                    }`}
                  dangerouslySetInnerHTML={{
                    __html: htmlBody.substring(0, htmlBody.lastIndexOf(" ", 400)),
                  }}
                />}
              <AccordionContent>
                <div
                  className="prose max-w-full prose-h1:text-lg prose-headings:font-medium prose-headings:my-2 prose-h2:text-base prose-p:text-sm prose-p:mt-1 prose-headings:text-neutral-700 block text-neutral-500 dark:text-neutral-400"
                  dangerouslySetInnerHTML={{
                    __html: htmlBody,
                  }}
                />
              </AccordionContent>
              <div
                className={`w-full pt-5 relative`}
              >
                <AccordionTrigger
                  className={`text-primary-700 !no-underline justify-start no-chevron pb-0 pt-5 bg-white border-t border-neutral-300 `}
                  onClick={() => setShowMore(!showMore)}
                >
                  <span className=" text-neutral-900 z-50 border border-neutral-200 hover:bg-neutral-200 disabled:bg-opacity-70 rounded-full py-2 px-3 font-medium">
                    {showMore ?
                      "Nascondi"
                      :
                      <div className="flex show-chevron items-center space-x-1">
                        <span>Maggiori informazioni</span>
                        <ChevronDoubleRightIcon className="w-4 h-4 " />
                      </div>}
                  </span>
                </AccordionTrigger>
              </div>
            </AccordionItem>
          </Accordion>
          :
          bodyContent ? (
            <div
              className="prose block text-neutral-500 dark:text-neutral-400"
            >
              {bodyContent}
            </div>
          ) : null
        }
      </div>
      <div className="mb-8 lg:mb-11 border border-neutral-200 rounded-xl p-8 px-10">

        <span className=" space-x-3 text-neutral-500 dark:text-neutral-400 flex items-center">
          <UsersIcon className="w-6 h-6" />
          {totalCount === 1 ? (
            <span><span className="font-medium text-neutral-600">{totalCount} professionista </span>trovato</span>
          ) : (
            <span><span className="font-medium text-neutral-600">{totalCount} professionisti </span>trovati</span>
          )}
        </span>
        <div className="w-32 border-b border-neutral-200 dark:border-neutral-700 pb-5"></div>
        <div className="flex space-x-2 mt-5 items-start">
          <span className="whitespace-nowrap pt-2">Filtra per: </span>
          <TabFilters shouldShowCityFilter={shouldShowCityFilter ?? false} />
        </div>
      </div>
      <div className="grid grid-cols-1 gap-6 md:gap-8 xl:grid-cols-2 ">
        {data?.listConsultants?.collection?.map((item, index) => (
          <PropertyCardH
            key={index}
            data={item as Consultant}
            serviceSearch={pathNames.service?.toString() ?? null}
          />
        ))}
      </div>
      {data?.listConsultants?.paginationInfo?.lastPage &&
        data?.listConsultants?.paginationInfo?.lastPage > 1 && (
          <Pagination
            className="mt-10"
            numberOfPages={data?.listConsultants?.paginationInfo?.lastPage}
            currentPage={params?.queryParams?.page ?? 1}
          />
        )}
    </div>
  )
}

export default SectionGridFilterCard
