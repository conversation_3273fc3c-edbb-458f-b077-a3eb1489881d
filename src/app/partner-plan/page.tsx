"use client"
import { useEffect, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
    GetConsultantByTokenDocument,
    GetConsultantByTokenQuery,
    GetConsultantByTokenQueryVariables,
    UpdateConsultantDocument,
    UpdateConsultantMutation,
    UpdateConsultantMutationVariables,
} from "@/api/gql/generated"
import { fetcher } from "@/api/gql/fetcher"
import { useCurrentUser } from "@/hooks/useCurrentUser"
import Spinner from "@/components/Spinner"
import { CheckIcon } from "@heroicons/react/24/solid"
import Stripe from "stripe"
import { useSearchParams } from "next/navigation"
import { toast } from "sonner"



const PartnerPlanPayment = () => {
    const searchParams = useSearchParams();
    const sessionId = searchParams.get("sessionId");
    const [decisione, setDecisione] = useState('');
    const [loading, setLoading] = useState(false);
    const [product, setProduct] = useState<Stripe.Product>()
    const [price, setPrice] = useState<Stripe.Price>();
    const [isLoading, setIsLoading] = useState(false);
    const [isCheckingSubscriptionValidity, setIsCheckingSubscriptionValidity] = useState(false)
    const [isPartnerPlanValid, setIsPartnerPlanValid] = useState(false)

    const currentUser = useCurrentUser()
    const queryClient = useQueryClient()

    const {
        data: prefilledData,
        isLoading: prefilledLoading,
        isError: isPrefilledError,
    } = useQuery({
        queryKey: ["getConsultantByToken"],
        queryFn: fetcher<
            GetConsultantByTokenQuery,
            GetConsultantByTokenQueryVariables
        >(
            GetConsultantByTokenDocument,
            {},
            {
                Authorization: `Bearer ${currentUser?.token}`,
            }
        ),
    })

    const { mutate } = useMutation<
        UpdateConsultantMutation,
        unknown,
        UpdateConsultantMutationVariables,
        unknown
    >({
        mutationKey: ["updateConsultant"],
        mutationFn: (variables?: UpdateConsultantMutationVariables) =>
            fetcher<UpdateConsultantMutation, UpdateConsultantMutationVariables>(
                UpdateConsultantDocument,
                variables,
                {
                    Authorization: `Bearer ${currentUser?.token}`,
                }
            )(),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["getConsultantByToken"],
            })
            toast.success("Informazioni aggiornate.")
        },
        onError: (e) => {
            toast.error("Errore durante l'aggiornamento.")
            throw new Error("Error occurred while updating the consultant:  " + e)
        }
    })

    const checkSubscriptionValidity = async () => {
        setIsCheckingSubscriptionValidity(true);
        try {
            const response = await fetch('/api/check_subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ subscriptionId: prefilledData?.byTokenConsultant?.stripePartnerPlanSubscriptionId }),
                cache: 'no-store'
            });
            const subscription = await response.json();
            return subscription.isValid;
        } catch (error) {
            console.error('Error checking subscription:', error);
            throw new Error('Error checking subscription:' + error)
        } finally {
            setIsCheckingSubscriptionValidity(false);

        }
    }

    useEffect(() => {
        if (sessionId) {
            const fetchSubscriptionId = async () => {
                try {
                    const response = await fetch('/api/subscription_id?sessionId=' + sessionId, { cache: 'no-store' })
                    const responseData = await response.json();
                    if (!prefilledData?.byTokenConsultant?.id) {
                        toast.error("Errore durante l'aggiornamento.")
                        return
                    }
                    mutate({
                        input: {
                            id: prefilledData.byTokenConsultant.id,
                            stripeSubscriptionId: responseData.subscriptionId
                        },
                    })
                } catch (error) {
                    console.error("Error fetching subscription id: ", error)
                    throw new Error("Error fetching subscription id: " + error)
                }
            }
            fetchSubscriptionId()
        }
        if (prefilledData?.byTokenConsultant?.stripePartnerPlanSubscriptionId) {
            checkSubscriptionValidity().then(
                (isValid) => {
                    setIsPartnerPlanValid(isValid)
                }
            )
        }
    }, [prefilledData])


    const getCustomerInfo = async () => {
        const res = await fetch('/api/customer_info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: prefilledData?.byTokenConsultant?.email }),
        });
        return await res.json();
    };

    const handleApprove = async () => {
        setLoading(true);
        try {
            // Get customer info
            const customerInfo = await getCustomerInfo();

            if (!customerInfo.success) {
                throw new Error(customerInfo.error);
            }

            const paymentRes = await fetch('/api/subscribe-partner-plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    priceId: product?.default_price,
                    customerId: customerInfo.customerId,
                    paymentMethodId: customerInfo.paymentMethodIds[0] // Send payment method ID to the API
                }),
            });

            const data = await paymentRes.json();

            if (data.id) {
                if (prefilledData?.byTokenConsultant && prefilledData?.byTokenConsultant.id) {
                    mutate({
                        input: {
                            id: prefilledData.byTokenConsultant.id,
                            stripePartnerPlanSubscriptionId: data.id
                        },
                    })
                }
                setDecisione('Approvato');
            } else {
                throw new Error(data.error);
            }
        } catch (error: any) {
            setDecisione('Rifiutato');
        } finally {
            setLoading(false);
        }
    };

    const handleReject = () => {
        setDecisione('Rifiutato');
    };



    useEffect(() => {
        const fetchProduct = async () => {
            setIsLoading(true)
            try {
                const response = await fetch('/api/product', {
                    method: 'POST',
                    headers: { 'Cache-Control': 'no-cache' },
                    body: JSON.stringify({ productType: 'partner' })
                });
                const productsData = await response.json();
                setProduct(productsData.product);
                setPrice(productsData.price)
                setIsLoading(false)
            } catch (error) {
                console.error('Error fetching products:', error);
                throw new Error('Error fetching products: ' + error);
            }
        };

        fetchProduct();
    }, [prefilledData]);


    return (
        <div className="max-md:container flex flex-col items-center justify-center min-h-screen bg-neutral-100 -mt-14 text-center">
            <h1 className="text-3xl font-bold mb-4">Completa l&apos;attivazione del piano Partners</h1>
            <p className="text-lg mb-4">Autorizza il pagamento mensile per sbloccare subito tutti i vantaggi del piano Partners.</p>




            {(prefilledLoading || isLoading || isCheckingSubscriptionValidity) ?
                <Spinner /> :
                <>
                    {price && price.currency && price?.unit_amount && <h2 className="text-2xl font-semibold">€{price?.unit_amount / 100}</h2>}
                    {product && product.description && <p className="mb-6 mt-1">({product?.description})</p>}
                    {!decisione && !isPrefilledError && (
                        isPartnerPlanValid ?
                            <span className="text-green-500 flex items-center text-2xl">
                                <CheckIcon className="w-6 h-6 mr-1" />
                                Paid
                            </span>
                            :
                            <div className="space-x-4 text-center">
                                <button
                                    onClick={handleApprove}
                                    disabled={loading}
                                    className={`px-6 py-2 bg-green-500 text-white rounded-full shadow-md hover:bg-green-600 transition ${loading ? 'opacity-50 cursor-not-allowed' : ''
                                        }`}
                                >
                                    {loading ? 'Processing...' : 'Autorizza'}
                                </button>
                                <button
                                    onClick={handleReject}
                                    className="px-6 py-2 bg-red-500 text-white rounded-full shadow-md hover:bg-red-600 transition"
                                >
                                    Rifiuta
                                </button>
                            </div>
                    )}
                </>}

            {decisione && (
                <div className="mt-8">
                    <h2 className="text-xl font-bold">Decisione: {decisione}</h2>
                </div>
            )}
        </div>
    )
}

export default PartnerPlanPayment;