import React from "react"
import { Metadata } from "next"
import HomeImage from "@/images/31.png"
import OurFeaturesImg from "@/images/our-features-1.png"

import SectionGridAuthorBox from "@/components/SectionGridAuthorBox"
import SectionHowItWork from "@/components/SectionHowItWork"
import SectionOurFeatures from "@/components/SectionOurFeatures"
import SectionSliderNewCategories from "@/components/SectionSliderNewCategories"
import SectionHeroFull from "./(server-components)/SectionHeroFull"

export const metadata: Metadata = {
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
}

function PageHome() {
  return (
    <main className="nc-PageHome relative space-y-12 overflow-hidden">
      {/* GLASSMOPHIN */}
      <SectionHeroFull
        title="Trova il professionista giusto per te su Consulente Ideale"
        imageSrc={HomeImage}
        hasSearch
      />
      <div className="container relative mb-24 flex flex-col gap-y-24 lg:mb-28 lg:gap-y-28">
        {/* SECTION HERO */}

        <SectionSliderNewCategories
          heading="Scopri i nostri professionisti"
          subHeading="Scopri le categorie più richieste"
          categoryCardType="card4"
          itemPerRow={4}
        />
        <SectionHowItWork />
        <SectionOurFeatures
          title="Esperti affidabili"
          rightImg={OurFeaturesImg}
          benefits={[
            {
              badgeName: "Esperienza",
              title: "Le tue esigenze al centro",
              description:
                "Ogni cliente è unico e merita un'attenzione speciale. Offriamo consulenze personalizzate per garantire che ogni soluzione sia su misura per le tue specifiche necessità.",
            },
            {
              badgeName: "Esperti",
              badgeColor: "green",
              title: "Competenza",
              description:
                "Collaboriamo solo con i migliori professionisti nel campo, garantendo che riceverai una consulenza di alta qualità da esperti qualificati e affidabili.",
            },
            {
              badgeName: "Sicurezza",
              badgeColor: "red",
              title: "Tranquillità",
              description:
                "Il nostro processo di prenotazione è semplice e sicuro. Ti guidiamo in ogni passo, assicurandoci che la tua esperienza sia fluida e priva di stress, con la massima protezione dei tuoi dati.",
            },
          ]}
        />
        <SectionGridAuthorBox />
      </div>
    </main>
  )
}

export default PageHome
