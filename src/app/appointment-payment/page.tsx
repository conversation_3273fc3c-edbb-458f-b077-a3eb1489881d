"use client"
import { FC, useEffect, useState } from "react"
import { useQuery } from "@tanstack/react-query"
import {
    AppointmentPaymentStatus,
    GetAppointmentsDocument,
    GetAppointmentsQuery,
    GetAppointmentsQueryVariables,
    GetConsultantByTokenDocument,
    GetConsultantByTokenQuery,
    GetConsultantByTokenQueryVariables,
    SettingByCodeSettingDocument,
    SettingByCodeSettingQuery,
    SettingByCodeSettingQueryVariables,
} from "@/api/gql/generated"
import { fetcher } from "@/api/gql/fetcher"
import { useCurrentUser } from "@/hooks/useCurrentUser"
import Spinner from "@/components/Spinner"
import { Appointments } from "@/components/FlightCard"
import { redirect, useRouter } from "next/navigation"
import { CheckIcon } from "@heroicons/react/24/solid"

export interface AppointmentPaymentProps {
    searchParams: {
        appointmentId: string;
    }
}

const AppointmentPayment: FC<AppointmentPaymentProps> = ({ searchParams }) => {

    const [decisione, setDecisione] = useState('');
    const [loading, setLoading] = useState(false);
    const [currentAppointment, setCurrentAppointment] = useState<Appointments[0]>()

    const currentUser = useCurrentUser()

    const {
        data: settingData,
        isLoading: settingDataLoading,
        isError: settingDataError,
    } = useQuery({
        queryKey: ["settingByCodeSetting"],
        queryFn: fetcher<
            SettingByCodeSettingQuery,
            SettingByCodeSettingQueryVariables
        >(
            SettingByCodeSettingDocument,
            {},
        ),
    })
    const importo = Number(settingData?.settingByCodeSetting?.value); // Fixed amount in euros
    const vat = 0.22;
    const amountWithVat = importo + (vat * importo)
    const roundedAmountWithVat = Math.round(amountWithVat * 100) / 100;

    const {
        data: prefilledData,
        isLoading: prefilledLoading,
        isError: isPrefilledError,
    } = useQuery({
        queryKey: ["getConsultantByToken"],
        queryFn: fetcher<
            GetConsultantByTokenQuery,
            GetConsultantByTokenQueryVariables
        >(
            GetConsultantByTokenDocument,
            {},
            {
                Authorization: `Bearer ${currentUser?.token}`,
            }
        ),
    })

    const {
        data: appointments,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["getAppointments"],
        queryFn: fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
            GetAppointmentsDocument,
            {},
            {
                Authorization: `Bearer ${currentUser?.token}`,
            }
        ),
        select: (data) => {
            return data.myAppointments?.collection as unknown as Appointments
        },
    })



    const getCustomerInfo = async () => {
        const res = await fetch('/api/customer_info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: prefilledData?.byTokenConsultant?.email }),
        });
        return await res.json();
    };

    const handleApprove = async () => {
        setLoading(true);
        try {
            // Get customer info
            const customerInfo = await getCustomerInfo();

            if (!customerInfo.success) {
                throw new Error(customerInfo.error);
            }

            const paymentRes = await fetch('/api/payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: roundedAmountWithVat,
                    customerId: customerInfo.customerId,
                    paymentMethodId: customerInfo.paymentMethodIds[0], // Send payment method ID to the API
                    appointmentId: searchParams.appointmentId.split("/")[3]
                }),
            });

            const data = await paymentRes.json();

            if (data.success) {
                setDecisione('Approvato');
            } else {
                throw new Error(data.error);
            }
        } catch (error: any) {
            setDecisione('Rifiutato');
        } finally {
            setLoading(false);
        }
    };

    const handleReject = () => {
        setDecisione('Rifiutato');
    };

    useEffect(() => {
        if (!searchParams.appointmentId) {
            return redirect("/")
        }

        if (!isLoading) {
            if (
                appointments &&
                appointments?.length > 0
            ) {
                const appointment = appointments.find(app => app.id === searchParams.appointmentId);
                if (appointment) {
                    setCurrentAppointment(appointment)
                }
                else {
                    return redirect("/error")
                }
            }
            else {
                return redirect("/")

            }
        }
    }, [isLoading])


    return (
        <div className="max-md:container flex flex-col items-center justify-center min-h-screen bg-neutral-100 -mt-14 text-center">
            <h1 className="text-3xl font-bold mb-4">Richiesta di pagamento</h1>
            <p className="text-lg mb-4">Si prega di approvare o rifiutare il seguente importo:</p>

            {importo && roundedAmountWithVat ?
                <>
                    <h2 className="text-2xl font-semibold">€{roundedAmountWithVat}</h2>
                    <p className="mb-6 mt-1">({importo}€ + IVA)</p>
                </>
                : <></>
            }
            {prefilledLoading || isLoading ?
                <Spinner /> :
                !decisione && !isPrefilledError && (
                    currentAppointment?.paymentStatus === AppointmentPaymentStatus.Paid ?
                        <span className="text-green-500 flex items-center text-2xl">
                            <CheckIcon className="w-6 h-6 mr-1" />
                            Paid
                        </span>
                        :
                        <div className="space-x-4">
                            <button
                                onClick={handleApprove}
                                disabled={loading}
                                className={`px-6 py-2 bg-green-500 text-white rounded-full shadow-md hover:bg-green-600 transition ${loading ? 'opacity-50 cursor-not-allowed' : ''
                                    }`}
                            >
                                {loading ? 'Processing...' : 'Approva'}
                            </button>
                            <button
                                onClick={handleReject}
                                className="px-6 py-2 bg-red-500 text-white rounded-full shadow-md hover:bg-red-600 transition"
                            >
                                Rifiuta
                            </button>
                        </div>
                )}

            {decisione && (
                <div className="mt-8">
                    <h2 className="text-xl font-bold">Decisione: {decisione}</h2>
                </div>
            )}
        </div>
    )
}

export default AppointmentPayment;