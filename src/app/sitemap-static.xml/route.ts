import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";
export const revalidate = 0;
export const fetchCache = 'force-no-store';
export const preferredRegion = 'auto';

export async function GET() {
    const backend = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backend) {
        console.error('Missing NEXT_PUBLIC_BACKEND_URL environment variable');
        return NextResponse.json({ error: "Missing NEXT_PUBLIC_BACKEND_URL" }, { status: 500 });
    }

    // Use the exact filename for this specific route
    const backendUrl = `${backend.replace(/\/$/, "")}/sitemap-static.xml`;

    console.log(`Fetching static sitemap from: ${backendUrl}`);

    try {
        const res = await fetch(backendUrl, {
            cache: 'no-store',
            next: { revalidate: 0 },
            headers: {
                'Accept': 'application/xml',
                'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!res.ok) {
            console.error(`Backend returned ${res.status} for sitemap-static.xml`);
            return NextResponse.json({
                error: `Backend returned ${res.status} for sitemap-static.xml`,
                url: backendUrl
            }, { status: 502 });
        }

        const xml = await res.text();
        console.log(`Successfully fetched sitemap-static.xml`);

        return new NextResponse(xml, {
            status: 200,
            headers: {
                "Content-Type": "application/xml; charset=utf-8",
                "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
                "Surrogate-Control": "no-store"
            },
        });
    } catch (error) {
        console.error(`Error fetching sitemap-static.xml:`, error);
        return NextResponse.json({ 
            error: `Failed to fetch sitemap: ${error instanceof Error ? error.message : 'Unknown error'}`,
            url: backendUrl
        }, { status: 500 });
    }
}
