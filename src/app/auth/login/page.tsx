import React, { <PERSON> } from "react"
import Link from "next/link"

import LoginForm from "@/app/(client-components)/(Forms)/LoginForm"

export interface PageLoginProps {}

const PageLogin: FC<PageLoginProps> = ({}) => {
  return (
    <div className={`nc-PageLogin`}>
      <div className="container mb-24 lg:mb-32">
        <h2 className="my-20 flex items-center justify-center text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
          Login
        </h2>
        <div className="mx-auto max-w-md space-y-6">
          {/* Demo credentials info for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Demo Credentials (Development Only)
              </h3>
              <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> demopassword123</p>
              </div>
            </div>
          )}

          <LoginForm />
          {/* === */}
          <span className="block text-center text-neutral-700 dark:text-neutral-300">
            Vuoi entrare a far parte di Consulente Ideale? {` `}
            <Link
              href="/auth/registrazione"
              className="font-semibold underline"
            >
              Crea un account
            </Link>
          </span>
          <span className="block text-center text-neutral-700 dark:text-neutral-300">
            <Link href="/auth/reset" className="text-sm font-medium underline">
              Password dimenticata? Clicca qui
            </Link>
          </span>
        </div>
      </div>
    </div>
  )
}

export default PageLogin
