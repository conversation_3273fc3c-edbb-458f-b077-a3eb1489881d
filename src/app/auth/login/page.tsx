import React, { <PERSON> } from "react"
import Link from "next/link"

import LoginForm from "@/app/(client-components)/(Forms)/LoginForm"

export interface PageLoginProps {}

const PageLogin: FC<PageLoginProps> = ({}) => {
  return (
    <div className={`nc-PageLogin`}>
      <div className="container mb-24 lg:mb-32">
        <h2 className="my-20 flex items-center justify-center text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
          Login
        </h2>
        <div className="mx-auto max-w-md space-y-6">
          <LoginForm />
          {/* === */}
          <span className="block text-center text-neutral-700 dark:text-neutral-300">
            Vuoi entrare a far parte di Consulente Ideale? {` `}
            <Link
              href="/auth/registrazione"
              className="font-semibold underline"
            >
              Crea un account
            </Link>
          </span>
          <span className="block text-center text-neutral-700 dark:text-neutral-300">
            <Link href="/auth/reset" className="text-sm font-medium underline">
              Password dimenticata? Clicca qui
            </Link>
          </span>
        </div>
      </div>
    </div>
  )
}

export default PageLogin
