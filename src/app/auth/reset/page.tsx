import React, { <PERSON> } from "react"
import Link from "next/link"

import ResetForm from "@/app/(client-components)/(Forms)/ResetForm"

export interface PageLoginProps {}

const PageLogin: FC<PageLoginProps> = ({}) => {
  return (
    <div className={`nc-PageLogin`}>
      <div className="container my-24 lg:my-32">
        <div className="mx-auto mb-4 w-full max-w-2xl text-center">
          <h2 className="text-3xl font-semibold md:text-4xl">Reset Password</h2>
          <p className="mt-2 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-3">
            Inserisci la mail con il quale ti sei registrato, se esiste un
            account associato a questa mail riceverai un link per resettare la
            tua password.
          </p>
        </div>
        <div className="mx-auto mt-12 max-w-md space-y-6">
          <ResetForm />
        </div>
      </div>
    </div>
  )
}

export default PageLogin
