import React, { <PERSON> } from "react"

import NewPasswordForm from "@/app/(client-components)/(Forms)/NewPasswordForm"

export interface PageLoginProps {}

const PageNewPassword: FC<PageLoginProps> = ({}) => {
  return (
    <div className={`nc-PageLogin`}>
      <div className="container my-24 lg:my-32">
        <div className="mx-auto mb-4 w-full max-w-2xl text-center">
          <h2 className="text-3xl font-semibold md:text-4xl">Reset Password</h2>
          <p className="mt-2 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-3">
            Inserisci una nuova password per il tuo account
          </p>
        </div>
        <div className="mx-auto mt-12 max-w-md space-y-6">
          <NewPasswordForm />
        </div>
      </div>
    </div>
  )
}

export default PageNewPassword
