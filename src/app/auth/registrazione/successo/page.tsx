import { ArrowRightIcon } from "@heroicons/react/24/outline"

import ButtonPrimary from "@/shared/ButtonPrimary"

const Page = () => {
  return (
    <div className="flex w-full items-center space-y-16 py-16 lg:space-y-28 lg:py-28">
      <div className="mb-10 flex w-full max-w-2xl flex-shrink-0 flex-col items-start gap-y-8 lg:mb-0 lg:mr-10">
        <h2 className="text-4xl font-semibold">
          Benvenuto in Consulente Ideale 🎉
        </h2>
        <span className="block text-neutral-700 dark:text-neutral-400">
          Ti abbiamo inviato una mail di conferma al tuo indirizzo email.
          Controlla la tua casella di posta elettronica e clicca sul link per
          confermare il tuo account.
        </span>
        <ButtonPrimary className="mt-8" href={"/auth/login"}>
          Accedi
          <span>
            <ArrowRightIcon className="ml-2 h-5 w-5 " />
          </span>
        </ButtonPrimary>
      </div>
    </div>
  )
}

export default Page
