"use client"

import { FC, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useIsClient, useSessionStorage } from "@uidotdev/usehooks"

export type RedirectProps = {
  children?: React.ReactNode
}

const Redirect: FC<RedirectProps> = ({ children }) => {
  const navigation = useRouter()
  const isClient = useIsClient()
  const [registrationSuccess, setRegistrationSuccess] = useSessionStorage(
    "registrationSuccess",
    false
  )

  useEffect(() => {
    if (!registrationSuccess && isClient) {
      navigation.push("/")
      return
    } else if (isClient) {
      setRegistrationSuccess(false)
    }
  }, [isClient])
  return <>{children}</>
}

export default Redirect
