import React, { <PERSON> } from "react"

export interface CommonLayoutProps {
  children: React.ReactNode
  params: {
    stepIndex: string
  }
}

const CommonLayout: FC<CommonLayoutProps> = ({ children, params }) => {
  return (
    <div
      className={`nc-PageAddListing1 sm:py-18 mx-auto max-w-3xl px-4 pb-24 pt-14 lg:pb-32`}
    >
      <div className="space-y-11">
        <div className="listingSection__wrap ">{children}</div>
      </div>
    </div>
  )
}

export default CommonLayout
