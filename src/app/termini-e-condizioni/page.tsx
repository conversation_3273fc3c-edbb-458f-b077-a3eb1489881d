import React from "react"
import { notFound } from "next/navigation"
import { GetAgreementsQuery, useGetAgreementsQuery } from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import sanitizeHtml from "sanitize-html"

const TermsAndConditions = async () => {
  let data: GetAgreementsQuery
  try {
    data = await serverFetch(useGetAgreementsQuery, {
      cache: "no-cache",
      select: (data) => data,
    })
  } catch (error) {
    notFound()
  }

  return (
    <div className="container relative min-h-screen pb-24 pt-14 sm:pt-20 lg:pb-32">
      {data?.agreements?.collection?.map((agreement, idx) => (
        <div
          key={idx}
          className="prose max-w-none"
          dangerouslySetInnerHTML={{
            __html: sanitizeHtml(agreement?.content || ""),
          }}
        />
      ))}
    </div>
  )
}

export default TermsAndConditions
