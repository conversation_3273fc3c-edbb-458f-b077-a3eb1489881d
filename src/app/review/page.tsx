"use client"
import React, { useEffect } from "react"
import ReviewForm from "./ReviewForm"
import {
    ValidateByAppointmentHashConsultantReviewDocument,
    ValidateByAppointmentHashConsultantReviewMutation,
    ValidateByAppointmentHashConsultantReviewMutationVariables
} from "@/api/gql/generated"
import Link from "next/link"
import ButtonPrimary from "@/shared/ButtonPrimary"
import { fetcher } from "@/api/gql/fetcher"
import { useMutation } from "@tanstack/react-query"
import { useSearchParams } from "next/navigation"
import { useRouter } from "next/navigation"


const ReviewPage = () => {
    const searchParams = useSearchParams()
    const appointmentHash = searchParams.get('appointmentHash');
    const router = useRouter();

    const {
        mutate,
        isSuccess,
        isError,
    } = useMutation<
        ValidateByAppointmentHashConsultantReviewMutation,
        unknown,
        ValidateByAppointmentHashConsultantReviewMutationVariables,
        unknown
    >({
        mutationKey: ["assignTagsToConsultant"],
        mutationFn: (variables?: ValidateByAppointmentHashConsultantReviewMutationVariables) =>
            fetcher<
                ValidateByAppointmentHashConsultantReviewMutation,
                ValidateByAppointmentHashConsultantReviewMutationVariables
            >(ValidateByAppointmentHashConsultantReviewDocument, variables)()
    })

    useEffect(() => {
        if (appointmentHash) {
            mutate({
                appointmentHash
            })
        }
    }, [appointmentHash])



    if (isError) {
        return (
            <div className={`nc-PageContact overflow-hidden`}>
                <div className="mb-24 lg:mb-32 container">
                    <div className="mx-auto my-16 mb-4 w-full max-w-2xl md:my-10">
                        <h1 className="flex items-center justify-center text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
                            Link non valido
                        </h1>
                        <p className="mt-5 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-5">
                            Riprova ad aprire il link che hai ricevuto via email, o&nbsp;
                            <Link href="/contattaci" className="text-primary-700 underline">contattaci</Link>&nbsp;
                            se il problema persiste.
                        </p>
                        <ButtonPrimary onClick={() => router.push("/")} className="mt-10">Torna alla Homepage</ButtonPrimary>
                    </div>
                </div>
            </div>
        )
    }

    if (isSuccess) {
        return (
            <div className={`nc-PageContact overflow-hidden`}>
                <div className="mb-24 lg:mb-32 container">
                    <div className="mx-auto my-16 mb-4 w-full max-w-3xl md:my-10">
                        <h1 className="flex items-center justify-start text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
                            Condividi la tua esperienza!
                        </h1>
                        <p className="mt-5 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-5">
                            Grazie per aver scelto di condividere la tua esperienza con noi! Il tuo feedback è fondamentale per migliorare i nostri servizi e garantire un&apos;esperienza sempre più soddisfacente.
                            <br /><br />
                            Condividi le tue impressioni sull&apos;appuntamento con il nostro professionista compilando il seguente modulo.
                        </p>
                    </div>
                    <div className="mx-auto max-w-3xl">
                        <div>
                            <ReviewForm appointmentHash={appointmentHash ?? ""} />
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}

export default ReviewPage
