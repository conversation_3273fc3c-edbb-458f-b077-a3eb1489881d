"use client"

import { useEffect, useState } from "react"
import { useCreateConsultantReviewMutation } from "@/api/gql/generated"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"
import { toast } from "sonner"
import { minLength, object, InferOutput, string, number, minValue, pipe } from "valibot"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Textarea from "@/shared/Textarea"
import FormItem from "@/app/(Form)/FormItem"
import { Rating, RoundedStar } from '@smastrom/react-rating'
import '@smastrom/react-rating/style.css'
import { useRouter } from "next/navigation"

export const FormSchema = object({
    message: pipe(string("Per favore inserisci del testo."),
        minLength(1, "Per favore inserisci del testo.")
    ),
    rating: pipe(number("Per favore inserisci un valutazione."), minValue(1, "Per favore inserisci un valutazione."))
})

export type FormSchemaType = InferOutput<typeof FormSchema>

type Props = {
    appointmentHash: string;
}

const ReviewForm = (props: Props) => {
    const { appointmentHash } = props;
    const router = useRouter()
    const myStyles = {
        itemShapes: RoundedStar,
        activeFillColor: 'rgb(234 179 8)',
        inactiveFillColor: 'rgb(209,213,219)',
        width: 24
    }

    const [rating, setRating] = useState(0)
    const form = useForm<FormSchemaType>({
        resolver: valibotResolver(FormSchema),
        mode: "onSubmit",
    })


    const { mutate, isSuccess, isError } =
        useCreateConsultantReviewMutation()

    const onSubmit: SubmitHandler<FormSchemaType> = async (data) => {

        if (appointmentHash) {

            mutate({
                appointmentHash,
                description: data.message,
                rating: data.rating
            })
        } else {
            toast.error("Link non valido")
        }

    }


    useEffect(() => {
        if (isSuccess) {
            form.reset()
            router.push("/review/success")
        }
    }, [form, isSuccess])

    useEffect(() => {
        if (isError) {
            toast.error("Errore nell'invio del messaggio, prova di nuovo.")
        }
    }, [isError])

    const handleChangeRating = (value: number) => {
        setRating(value)
        form.setValue("rating", value)
    }


    return (
        <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="grid grid-cols-1 gap-3"
        >
            <FormItem
                label="La tua valutazione"
                visibleDesc={!!form.formState.errors.rating}
                descClassName="text-red-500"
                desc={
                    form.formState.errors.rating
                        ? form.formState.errors.rating.message
                        : "Inserisci il tuo nome"
                }
            >
                <Rating
                    style={{ maxWidth: 250 }}
                    value={rating}
                    onChange={(value: number) => handleChangeRating(value)}
                    itemStyles={myStyles}
                />
            </FormItem>

            <FormItem
                label="Recensione"
                descClassName="text-red-500"
                visibleDesc={!!form.formState.errors.message}
                desc={
                    form.formState.errors.message
                        ? form.formState.errors.message.message
                        : "Inserisci la tua email"
                }
            >
                <Textarea
                    className="mt-1"
                    rows={6}
                    placeholder="Descrivi la tua esperienza"
                    isError={!!form?.formState?.errors?.message?.message}
                    {...form.register("message")}
                />
            </FormItem>
            <div>
                <ButtonPrimary type="submit">Invia la tua recensione</ButtonPrimary>
            </div>
        </form>
    )
}

export default ReviewForm
