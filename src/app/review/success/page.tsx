"use client"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Link from "next/link"
import { useRouter } from "next/navigation"


const ReviewSuccessPage = () => {
    const router = useRouter()
    return (
        <div className={`nc-PageContact overflow-hidden`}>
            <div className="mb-24 lg:mb-32 container">
                <div className="mx-auto my-16 mb-4 w-full max-w-2xl md:my-10">
                    <h1 className="flex items-center justify-start text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
                        Grazie per il tuo feedback!
                    </h1>
                    <p className="mt-5 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-5">
                        Grazie per aver dedicato del tempo a condividere la tua esperienza con noi. Il tuo feedback è estremamente prezioso per noi e ci aiuta a migliorare costantemente i nostri servizi per fornirti un&apos;esperienza ancora migliore in futuro.
                        <br /><br />
                        Apprezziamo la fiducia che hai riposto in noi e speriamo di poterti assistere ancora in futuro. Se hai ulteriori domande o necessiti di assistenza, non esitare a&nbsp;
                        <Link href="/contattaci" className="text-primary-700 underline">contattarci</Link>&nbsp;
                        direttamente. Siamo qui per te.
                    </p>
                    <ButtonPrimary onClick={() => router.push("/")} className="mt-10">Torna alla Homepage</ButtonPrimary>
                </div>
            </div>
        </div>
    )
}

export default ReviewSuccessPage