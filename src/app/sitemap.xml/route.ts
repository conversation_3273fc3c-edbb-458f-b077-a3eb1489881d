// proxies GET /sitemap.xml → ${NEXT_PUBLIC_BACKEND_URL}/sitemap.xml
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET() {
    const backend = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backend) {
        return NextResponse.json({ error: "Missing NEXT_PUBLIC_BACKEND_URL" }, { status: 500 });
    }

    const url = `${backend.replace(/\/$/, "")}/sitemap.xml`;
    const res = await fetch(url, { next: { revalidate: 0 } });
    if (!res.ok) {
        return NextResponse.json({ error: `Backend returned ${res.status} for sitemap.xml` }, { status: 502 });
    }
    const xml = await res.text();
    return new NextResponse(xml, {
        status: 200,
        headers: { "Content-Type": "application/xml; charset=utf-8" },
    });
}
