import React, { <PERSON> } from "react"
import Image, { StaticImageData } from "next/image"
import imagePng from "@/images/professionista-cta.png"

import ButtonPrimary from "@/shared/ButtonPrimary"

export interface SectionHero3Props {
  subtitle: string
  title: string
  btnText: string
  imageSrc?: StaticImageData
  className?: string
  onClick?: () => void
  href?: string
}

const SectionHero3: FC<SectionHero3Props> = ({
  className = "",
  title,
  subtitle,
  btnText,
  imageSrc = imagePng,
  href,
  onClick,
}) => {
  return (
    <div
      className={`nc-SectionHero3 relative ${className} items-center`}
      data-nc-id="SectionHero3"
    >
      <div className="absolute inset-x-0 top-[15%] z-10 mx-auto flex max-w-2xl flex-col items-center space-y-4 place-self-center text-center lg:space-y-5 xl:space-y-8">
        <span className="font-semibold text-neutral-900 sm:text-lg md:text-xl">
          {subtitle}
        </span>
        <h2 className="text-3xl font-bold !leading-[115%] text-black sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl ">
          {title}
        </h2>
        <ButtonPrimary
          sizeClass="px-6 py-3 lg:px-8 lg:py-4 rounded-xl"
          fontSize="text-sm sm:text-base lg:text-lg font-medium"
          onClick={onClick}
          href={href}
        >
          {btnText}
        </ButtonPrimary>
      </div>
      <div className="aspect-h-1 aspect-w-1 relative sm:aspect-h-3 sm:aspect-w-4 lg:aspect-h-9 lg:aspect-w-16 xl:aspect-h-8 ">
        <Image
          className="absolute inset-0 rounded-xl object-cover"
          src={imageSrc}
          alt="hero"
          priority
        />
      </div>
    </div>
  )
}

export default SectionHero3
