"use client"

import { FAQPageJsonLd } from "next-seo"

import Accordion from "./Accordion"

interface ReusableFAQProps {
  faqItems: { questionName: string; acceptedAnswerText: string }[]
  type: "single" | "multiple"
  collapsible?: boolean
}

const ReusableFAQ: React.FC<ReusableFAQProps> = ({
  faqItems,
  type = "single",
  collapsible = false,
}) => {
  return (
    <>
      <FAQPageJsonLd mainEntity={faqItems} useAppDir={true} />
      <Accordion
        type={type}
        collapsible={collapsible}
        items={faqItems.map((item, index) => ({
          value: `question-${index}`,
          question: item.questionName,
          answer: item.acceptedAnswerText,
        }))}
      />
    </>
  )
}

export default ReusableFAQ
