import React, { <PERSON> } from "react"
import Image from "next/image"
import imagePng from "@/images/hero-right-2.png"

import HeroSearchForm from "../(client-components)/(HeroSearchForm)/HeroSearchForm"

export interface SectionHeroProps {
  className?: string
}

const SectionHero: FC<SectionHeroProps> = ({ className = "" }) => {
  return (
    <div className={`nc-SectionHero relative flex flex-col ${className}`}>
      <div className="flex flex-col lg:flex-row lg:items-center">
        <div className="flex flex-shrink-0 flex-col items-start space-y-8 pb-8 sm:space-y-10 lg:mr-10 lg:w-1/2 lg:pb-48 xl:mr-0 xl:pr-14">
          <h2 className="text-2xl font-medium !leading-[114%] [text-wrap:balance] md:text-2xl xl:text-4xl">
            Trova il professionista giusto per te
          </h2>
        </div>
        <div className="hidden flex-grow lg:flex">
          <Image className="w-full" src={imagePng} alt="hero" priority />
        </div>
      </div>

      <div className="z-10 mb-12 flex w-full lg:-mt-64 lg:mb-0">
        <HeroSearchForm />
      </div>
    </div>
  )
}

export default SectionHero
