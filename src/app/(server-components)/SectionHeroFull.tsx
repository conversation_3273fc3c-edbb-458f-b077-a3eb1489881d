import React, { <PERSON> } from "react"
import Image, { StaticImageData } from "next/image"
import imagePng from "@/images/professionista-cta.png"

import { cn } from "@/lib/utils"
import ButtonPrimary from "@/shared/ButtonPrimary"

import HeroSearchForm from "../(client-components)/(HeroSearchForm)/HeroSearchForm"

export interface SectionHeroFullProps {
  title: string
  btnText?: string
  imageSrc?: StaticImageData
  className?: string
  onClick?: () => void
  href?: string
  hasSearch?: boolean
  desc?: string
}

const SectionHeroFull: FC<SectionHeroFullProps> = ({
  className = "",
  title,
  btnText,
  imageSrc = imagePng,
  href,
  onClick,
  hasSearch,
  desc,
}) => {
  return (
    <div
      className={`nc-SectionHeroFull relative ${className} mb-48 items-center sm:mb-20 md:mb-0`}
      data-nc-id="SectionHeroFull"
    >
      <div className="absolute inset-0 z-10 bg-primary-6000 opacity-40"></div>
      <div
        className={cn(
          "items-left container absolute inset-0 mt-60 flex flex-col justify-center space-y-12 md:mt-0"
        )}
      >
        <h1 className="z-10  text-xl font-bold !leading-[115%] text-white [text-wrap:pretty] sm:text-4xl md:text-4xl ">
          {title}
        </h1>
        {desc ? (
          <span className="z-10 mt-6 block text-lg font-medium text-white">
            {desc}
          </span>
        ) : null}
        {btnText ? (
          <ButtonPrimary
            sizeClass="px-6 py-3 lg:px-8 lg:py-4 rounded-xl"
            fontSize="text-sm sm:text-base lg:text-lg font-medium"
            onClick={onClick}
            href={href}
          >
            {btnText}
          </ButtonPrimary>
        ) : null}
        {hasSearch && (
          <div className="z-10 mb-12 flex w-full lg:mb-0">
            <HeroSearchForm />
          </div>
        )}
      </div>

      <div className="aspect-h-3 aspect-w-4 relative sm:aspect-h-3 sm:aspect-w-4 lg:aspect-h-9 lg:aspect-w-16 xl:aspect-h-8 ">
        <Image
          className="absolute inset-0 object-cover"
          src={imageSrc}
          alt="hero"
          priority
        />
      </div>
    </div>
  )
}

export default SectionHeroFull
