import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

export type AccordionItem = {
  value: string
  question: string
  answer: string
}

export type AccordionProps = {
  items: AccordionItem[]
  type?: "single" | "multiple"
  collapsible?: boolean
}

const AccordionComponent: React.FC<AccordionProps> = ({
  items,
  type = "single",
  collapsible = false,
}) => {
  return (
    <Accordion type={type} collapsible={collapsible}>
      {items.map((item) => (
        <AccordionItem key={item.value} value={item.value}>
          <AccordionTrigger>{item.question}</AccordionTrigger>
          <AccordionContent>{item.answer}</AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  )
}

export default AccordionComponent
