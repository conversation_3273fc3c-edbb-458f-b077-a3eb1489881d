import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";
export const revalidate = 0;

// Add segment config to disable caching
export const fetchCache = 'force-no-store';
export const preferredRegion = 'auto';

export async function GET(request: Request) {
    // Get the host from the request
    const host = request.headers.get('host') || 'consulenteideale.com';
    const protocol = host.includes('localhost') || host.includes('127.0.0.1') ? 'http' : 'https';

    // Create a robots.txt that references the sitemap
    const robotsTxt = `# https://${host}/robots.txt

User-agent: *
Allow: /

Sitemap: ${protocol}://${host}/sitemap.xml
`;

    return new NextResponse(robotsTxt, {
        status: 200,
        headers: { "Content-Type": "text/plain" },
    });
}
