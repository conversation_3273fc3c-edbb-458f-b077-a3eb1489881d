import { ExclamationTriangleIcon } from "@radix-ui/react-icons"

type FormErrorProps = {
  message?: string
}

const FormError = ({ message }: FormErrorProps) => {
  if (!message) return null

  return (
    <div className="bg-red/15 text-red flex items-center gap-x-2 rounded-md p-3 text-sm">
      <ExclamationTriangleIcon className="h-4 w-4 flex-none" />
      <p>{message}</p>
    </div>
  )
}

export default FormError
