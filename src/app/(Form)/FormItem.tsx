import React, { <PERSON> } from "react"

import { cn } from "@/lib/utils"
import Label from "@/components/Label"

export interface FormItemProps {
  className?: string
  label?: string
  desc?: string
  children?: React.ReactNode
  visibleDesc?: boolean
  descClassName?: string
}

const FormItem: FC<FormItemProps> = ({
  children,
  className = "",
  label,
  desc,
  visibleDesc = true,
  descClassName,
}) => {
  return (
    <div className={className}>
      {label && <Label>{label}</Label>}
      <div className="mt-1">{children}</div>
      {desc && (
        <span
          className={cn(
            "mt-2 block text-xs text-neutral-500  dark:text-neutral-400",
            {
              invisible: !visibleDesc,
            },
            descClassName
          )}
        >
          {desc}
        </span>
      )}
    </div>
  )
}

export default FormItem
