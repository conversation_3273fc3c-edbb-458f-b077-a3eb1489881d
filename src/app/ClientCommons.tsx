"use client"

import React, { useEffect } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import { useThemeMode } from "@/utils/useThemeMode"
import { sendGTMEvent } from "@next/third-parties/google"

const ClientCommons = () => {
  //
  useThemeMode()

  const pathname = usePathname()
  // const params = useSearchParams()
  //  CUSTOM THEME STYLE
  useEffect(() => {
    const $body = document.querySelector("body")
    if (!$body) return

    let newBodyClass = ""

    newBodyClass && $body.classList.add(newBodyClass)
    return () => {
      newBodyClass && $body.classList.remove(newBodyClass)
    }
  }, [pathname])

  // GTM Event to track page views
  useEffect(() => {
    if (pathname && typeof window !== "undefined" && window.dataLayer && document) {
      window.dataLayer.push({
        event: "pageview",
        page: pathname,
        title: document.title,
      })
    }
  }, [])

  return <></>
}

export default ClientCommons
