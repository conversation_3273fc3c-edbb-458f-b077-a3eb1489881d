import React from "react"
import { SessionProvider } from "next-auth/react"
import { auth } from "root/auth"

import { Nav } from "./(components)/Nav"

export interface CommonLayoutProps {
  children?: React.ReactNode
}

export default async function CommonLayout({ children }: CommonLayoutProps) {
  const session = await auth()
  return (
    <SessionProvider session={session}>
      <div className="nc-CommonLayoutAccount bg-neutral-50 dark:bg-neutral-900">
        <div className="border-b border-neutral-200 bg-white pt-12 dark:border-neutral-700 dark:bg-neutral-800">
          <Nav />
        </div>
        <div className="container pb-24 pt-14 sm:pt-20 lg:pb-32">
          {children}
        </div>
      </div>
    </SessionProvider>
  )
}
