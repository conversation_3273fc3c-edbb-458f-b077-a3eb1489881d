'use client'
import ButtonPrimary from '@/shared/ButtonPrimary';
import { loadStripe } from '@stripe/stripe-js';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Stripe from 'stripe';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!);

type Props = {
    product?: Stripe.Product;
    isSubscriptionValid: boolean;
    email?: string;
}

const PartnerPlanCheckout = (props: Props) => {
    const { product, isSubscriptionValid, email } = props;

    const router = useRouter()


    const handleClick = async () => {

        try {
            if (product && product.default_price) {
                const priceId = product.default_price
                const stripe = await stripePromise;
                if (!stripe) {
                    console.error('Stripe.js has not loaded.');
                    throw new Error('Stripe.js has not loaded.');
                }
                // Loop through each priceId to create and handle subscriptions

                if (isSubscriptionValid) {
                    router.push(`/partner-plan`)
                }
                else {
                    const response = await fetch('/api/checkout_sessions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ priceId, isPartnerPlanIncluded: true, email }),
                        cache: 'no-store'
                    });

                    const session = await response.json();

                    // Redirect to Checkout for each session
                    const { error } = await stripe.redirectToCheckout({
                        sessionId: session.id,
                    });

                    if (error) {
                        console.error(error.message);
                        throw new Error('Error during checkout redirection: ' + error.message);
                    }
                }
            }
        } catch (error) {
            console.error('Error creating checkout sessions:', error);
            throw new Error('Error creating checkout sessions: ' + error);
        }
    };

    return (
        <div className="w-full lg:pl-10 max-lg:pt-10">
            <div className="explanation-section mb-6 text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg">
                <h3 className="text-gray-900 font-medium text-2xl mb-5">Partners Plan</h3>
                <p className="">
                    Attiva il piano Partners per massimizzare la tua presenza su <Link className="text-primary-700 underline" href="https://consulenteideale.it">consulenteideale.it</Link>! Il piano Partners include tutte le funzionalità del piano Basic, oltre a vantaggi esclusivi per far crescere il tuo business:
                </p>

                <ul className="list-disc ml-6 mt-5">
                    <li>CRM per gestire le prenotazioni.</li>
                    <li>Zero costi per accettare prenotazioni.</li>
                    <li>Accesso alle sedi aziendali.</li>
                    <li>Formazione e tutor per 12 mesi.</li>
                    <li>Recupero crediti in caso di insolvenze da parte del cliente.</li>
                    <li>Ricezione trimestrale di richieste di preventivo.</li>
                    <li>Partnership estere con traduttore gratuito (durata variabile in base all’attività richiesta dalle aziende estere).</li>
                </ul>

                <p className="mt-5">
                    Un solo abbonamento, tutto incluso! Non perdere questa occasione per massimizzare le tue opportunità.
                </p>
                <p className="mt-5 italic text-sm">
                    *Se non hai ancora attivato il piano Basic, ti verrà chiesto prima di attivare il piano Basic e poi di completare la subscription del piano Partners nella pagina successiva.
                </p>
            </div>
            {/* Replace with actual logic to determine price IDs */}
            <ButtonPrimary
                onClick={() => handleClick()}
                className="mt-5"
            >
                Attiva il piano Partner
            </ButtonPrimary>
        </div>
    );
};

export default PartnerPlanCheckout;
