"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Route } from "@/routers/types"

export const Nav = () => {
  const pathname = usePathname()

  const listNav: Record<string, Route> = {
    account: "/account",
    "I tuoi servizi": "/account/servizi",
    appuntamenti: "/account/appuntamenti",
    "Il tuo Abbonamento": "/account/subscription"
  }

  return (
    <div className="container">
      <div className="hiddenScrollbar flex space-x-8 overflow-x-auto md:space-x-14">
        {Object.entries(listNav).map(([item, route]) => {
          const isActive = pathname === route
          return (
            <Link
              key={item}
              href={route}
              className={`block flex-shrink-0 border-b-2 py-5 capitalize md:py-8 ${isActive
                ? "border-primary-500 font-medium"
                : "border-transparent"
                }`}
            >
              {item}
            </Link>
          )
        })}
      </div>
    </div>
  )
}
