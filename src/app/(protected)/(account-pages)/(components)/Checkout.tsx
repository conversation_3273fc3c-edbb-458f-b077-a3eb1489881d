import ButtonPrimary from '@/shared/ButtonPrimary';
import { loadStripe, StripeError } from '@stripe/stripe-js';
import stripe, { Stripe } from 'stripe';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!);

type Props = {
    product?: Stripe.Product;
    email?: string;
}

const Checkout = (props: Props) => {
    const { product, email } = props;

    const handleClick = async (priceId: string | stripe.Price | null | undefined) => {
        const stripe = await stripePromise;
        if (!stripe) {
            console.error('Stripe.js has not loaded.');
            return;
        }

        try {
            const response = await fetch('/api/checkout_sessions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ priceId, isPartnerPlanIncluded: false, email }),
                cache: 'no-store'
            });

            const session = await response.json();
            stripe!.redirectToCheckout({
                sessionId: session.id,
            }).then((value: { error?: StripeError }) => {
                if (value.error) {
                    console.error(value.error.message);
                }
            });

        } catch (error) {
            console.error('Error:', error);
        }
    };

    return (
        <div>
            <ButtonPrimary
                onClick={() => handleClick(product?.default_price)}
                className="mt-5">
                Completa la tua sottoscrizione, pubblica il tuo profilo
            </ButtonPrimary>
        </div>
    );
};

export default Checkout;
