"use client"

import { fetcher } from "@/api/gql/fetcher"
import {
  GetAppointmentsDocument,
  GetAppointmentsQuery,
  GetAppointmentsQueryVariables,
} from "@/api/gql/generated"
import { useQuery } from "@tanstack/react-query"
import { isEmpty } from "lodash"
import { useCurrentUser } from "@/hooks/useCurrentUser"
import FlightCard, { Appointments } from "@/components/FlightCard"
import Spinner from "@/components/Spinner"

const AppointmentsPage = () => {
  const currentUser = useCurrentUser()
  const {
    data: appointments,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["getAppointments"],
    queryFn: fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
      GetAppointmentsDocument,
      {},
      {
        Authorization: `Bearer ${currentUser?.token}`,
      }
    ),
    select: (data) => {
      return data.myAppointments?.collection as unknown as Appointments
    },
    refetchOnMount: true, // Refetches on every mount
    refetchOnWindowFocus: true, // Refetches when the window regains focus
    gcTime: 0
  })

  return (
    <div className="flex w-full flex-col space-y-8">
      <div className="flex flex-col space-y-2">
        <h2 className="text-2xl font-semibold">I tuoi appuntamenti</h2>
        <span className="block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-3">
          Qui trovi tutti gli appuntamenti che gli utenti hanno prenotato con te
        </span>
      </div>
      <div className="grid grid-cols-1 gap-6 rounded-3xl lg:bg-neutral-50 lg:dark:bg-black/20">
        {isLoading && <Spinner />}
        {appointments?.sort((a, b) => new Date(a.scheduledAt) > new Date(b.scheduledAt) ? -1 : 1).map((item) => (
          <FlightCard key={item?.id} data={item} />
        ))}
        {isEmpty(appointments) && !isLoading && !isError && (
          <p className="text-base font-medium text-neutral-6000 [text-wrap:balance] dark:text-neutral-400">
            Al momento non hai appuntamenti
          </p>
        )}
      </div>
    </div>
  )
}

export default AppointmentsPage
