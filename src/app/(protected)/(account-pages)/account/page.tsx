"use client"

import React, { useEffect, useState } from "react"
import dynamic from "next/dynamic"
import { fetcher } from "@/api/gql/fetcher"
import {
  GetConsultantByTokenDocument,
  GetConsultantByTokenQuery,
  GetConsultantByTokenQueryVariables,
  UpdateConsultantDocument,
  UpdateConsultantMutation,
  UpdateConsultantMutationVariables,
} from "@/api/gql/generated"
import { getHtml } from "@/utils/HtmlManipulation"
import { convertDateToItalian } from "@/utils/localesDate"
import { Switch } from "@headlessui/react"
import { CameraIcon } from "@heroicons/react/24/outline"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useMaskito } from "@maskito/react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { ContentState, convertFromHTML, EditorState, Modifier } from "draft-js"
import { DateTime } from "luxon"
import {
  Controller,
  ControllerRenderProps,
  Submit<PERSON><PERSON><PERSON>,
  useForm,
} from "react-hook-form"
import { toast } from "sonner"
import { InferOutput } from "valibot"

import { maskitoOptions } from "@/lib/constants"
import { cn } from "@/lib/utils"
import useAccountDynamicFormSchema from "@/hooks/useAccountDynamicFormSchema"
import useConvertImageToWebP from "@/hooks/useConvertImagetoWebp"
import { useCurrentUser } from "@/hooks/useCurrentUser"
import Avatar from "@/shared/Avatar"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import LocationSelect from "@/components/LocationSelect"
import FormItem from "@/app/(Form)/FormItem"
import useCityIdByName from "@/hooks/useGetCityIdByName"

const Editor = dynamic(
  () => import("react-draft-wysiwyg").then((mod) => mod.Editor),
  { ssr: false }
)

export interface AccountPageProps { }

const AccountPage = () => {
  const currentUser = useCurrentUser()
  const [previewSrc, setPreviewSrc] = useState<string | null>(null)
  const { convertToWebP, isLoading } = useConvertImageToWebP()
  const AccountFormSchema = useAccountDynamicFormSchema()
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const [isHovered, setIsHovered] = useState(false)

  const isAvatarHovered = (isHovered: boolean) => {
    setIsHovered(isHovered)
  }

  const queryClient = useQueryClient()

  const { mutate } = useMutation<
    UpdateConsultantMutation,
    unknown,
    UpdateConsultantMutationVariables,
    unknown
  >({
    mutationKey: ["updateConsultant"],
    mutationFn: (variables?: UpdateConsultantMutationVariables) =>
      fetcher<UpdateConsultantMutation, UpdateConsultantMutationVariables>(
        UpdateConsultantDocument,
        variables,
        {
          Authorization: `Bearer ${currentUser?.token}`,
        }
      )(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["getConsultantByToken"],
      })
      toast.success("Informazioni aggiornate.")
    },
    onError: () => {
      toast.error("Errore durante l'aggiornamento.")
    },
  })

  const onImageSubmit: SubmitHandler<InferOutput<typeof AccountFormSchema>> = async (
    data
  ) => {
    if (!prefilledData?.byTokenConsultant?.id) {
      toast.error("Errore durante l'aggiornamento.")
      return
    }
    mutate({
      input: {
        id: prefilledData.byTokenConsultant.id,
        avatar: data.avatar,
      },
    })
  }

  const form = useForm<InferOutput<typeof AccountFormSchema>>({
    resolver: valibotResolver(AccountFormSchema),
    mode: "onSubmit",
    defaultValues: {
      fullName: "",
      email: "",
      city: "",
      isRemote: false,
      bio: EditorState.createEmpty(),
      dateOfBirth: "",
      telephone: "",
      fiscalCode: "",
      taxId: "",
    },
  })

  const { cityId, isLoading: isCityIdLoading } = useCityIdByName(
    form.watch("city")
  )

  const onSubmit: SubmitHandler<InferOutput<typeof AccountFormSchema>> = async (
    data
  ) => {
    if (isCityIdLoading || !cityId) return
    if (!prefilledData?.byTokenConsultant?.id) {
      toast.error("Errore durante l'aggiornamento.")
      return
    }

    mutate({
      input: {
        id: prefilledData.byTokenConsultant.id,
        fullName: data.fullName,
        isRemote: data.isRemote,
        bio: getHtml(data.bio || EditorState.createEmpty()),
        city: cityId,
        dateOfBirth: `${DateTime.fromFormat(data.dateOfBirth, "dd/MM/yyyy", {
          zone: "utc",
        }).toISODate()}T00:00:00+00:00`,
        telephone: data.telephone,
        fiscalCode: data.fiscalCode,
        taxId: data.taxId,
      },
    })
  }

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()

    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      const file = event.dataTransfer.files[0]
      await updateFormFile(file)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
  }



  const validateFile = (file: File) => {
    const validTypes = ["image/jpeg", "image/png", "image/webp"]
    if (!validTypes.includes(file.type)) {
      toast.error("Formato non valido.")
      return false
    }
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File troppo grande. Max 5MB.")
      return false
    }
    return true
  }

  const updateFormFile = async (file?: File) => {
    if (file && validateFile(file)) {
      try {
        const webPBlob = await convertToWebP({ imageFile: file })

        if (webPBlob.size > 3 * 1024 * 1024) {
          toast.error("Immagine troppo grande", {
            description: "Prova con un file più piccolo.",
          })
          return
        }

        const webPFile = new File([webPBlob], "converted-image.webp", {
          type: "image/webp",
        })

        const reader = new FileReader()
        reader.readAsDataURL(webPFile)
        const loadFile = new Promise((resolve) => {
          reader.onloadend = async () => {
            if (reader.result && typeof reader.result === "string") {
              form.setValue("avatar", reader.result)
              await onImageSubmit(form.getValues())
            }
            resolve(reader.result)
          }
        })
        toast.promise(loadFile, {
          loading: "Caricamento immagine",
          success: "Immagine profilo aggiornata",
          error: "Errore durante il caricamento",
        })
        setPreviewSrc(URL.createObjectURL(webPFile))
      } catch (error) {
        console.error("Error converting file:", error)
        toast.error("Immagine non valida. Prova con un altro file.")
      }
    }
  }

  const {
    data: prefilledData,
    isLoading: prefilledLoading,
    isError: PrefilledLoading,
  } = useQuery({
    queryKey: ["getConsultantByToken"],
    queryFn: fetcher<
      GetConsultantByTokenQuery,
      GetConsultantByTokenQueryVariables
    >(
      GetConsultantByTokenDocument,
      {},
      {
        Authorization: `Bearer ${currentUser?.token}`,
      }
    ),
  })

  const maskedDateRef = useMaskito({ options: maskitoOptions })

  useEffect(() => {
    const initialFormData = prefilledData?.byTokenConsultant
    const blocksFromHTML = convertFromHTML(initialFormData?.bio ?? "")
    const state = ContentState.createFromBlockArray(
      blocksFromHTML.contentBlocks,
      blocksFromHTML.entityMap
    )
    form.reset({
      fullName: initialFormData?.fullName,
      email: initialFormData?.email,
      isRemote: initialFormData?.isRemote ?? false,
      dateOfBirth: convertDateToItalian(initialFormData?.dateOfBirth ?? ""),
      telephone: initialFormData?.telephone ?? "",
      bio: EditorState.createWithContent(state),
      fiscalCode: initialFormData?.fiscalCode,
      taxId: initialFormData?.taxId,
      city: initialFormData?.city?.name,
    })
    if (initialFormData?.avatar) {
      setPreviewSrc(
        `${process.env.NEXT_PUBLIC_BACKEND_MEDIA_URL}/avatars/${initialFormData?.avatar}`
      )
    }
  }, [form, prefilledData?.byTokenConsultant])

  useEffect(() => {
    return () => {
      if (previewSrc) {
        URL.revokeObjectURL(previewSrc)
      }
    }
  }, [previewSrc])

  const onEditorStateChange = (
    editorState: EditorState,
    field: ControllerRenderProps<InferOutput<typeof AccountFormSchema>, "bio">
  ) => {
    field.onChange(editorState)

    const currentContent = editorState.getCurrentContent()
    const plainText = currentContent.getPlainText("")
    const charCount = plainText.length

    if (charCount > 500) {
      form.setError("bio", {
        type: "manual",
        message: "Bio must be under 500 characters",
      })
    } else {
      form.clearErrors("bio")
    }
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* HEADING */}
      <h2 className="text-3xl font-semibold">Informazioni Personali</h2>
      <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col items-start md:flex-row"
      >
        <Controller
          name="avatar"
          control={form.control}
          render={({ field: { onChange } }) => (
            <div
              className={cn(
                "relative flex flex-shrink-0 cursor-pointer items-start hover:opacity-80"
              )}
              onMouseEnter={() => isAvatarHovered(true)}
              onTouchStart={() => isAvatarHovered(true)}
              onTouchEnd={() => isAvatarHovered(false)}
              onMouseLeave={() => isAvatarHovered(false)}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => fileInputRef.current?.click()}
            >
              {isHovered && (
                <div className="absolute z-10 flex h-full w-full flex-col items-center justify-center gap-y-2 rounded-full bg-black/50">
                  <CameraIcon className="h-8 w-8 text-white" />
                  <span className="text-sm text-white">Cambia</span>
                </div>
              )}
              <div className="flex flex-shrink-0 items-start">
                {previewSrc ? (
                  <Avatar customUrl={previewSrc} sizeClass="h-24 w-24" />
                ) : (
                  <Avatar sizeClass="h-24 w-24" />
                )}
                <div
                  className={cn(
                    "space-y-1 text-center",
                    previewSrc && "hidden"
                  )}
                >
                  <div className="flex text-sm text-neutral-6000 dark:text-neutral-300">
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={async (e) => {
                        onChange(e)
                        await updateFormFile(e?.target?.files?.[0])
                      }}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        />
        <div className="mt-10 w-full flex-grow space-y-1 md:mt-0 md:pl-16">
          <FormItem
            label="Nome completo*"
            visibleDesc={!!form.formState.errors.fullName}
            descClassName="text-red-500"
            desc={
              form.formState.errors.fullName
                ? form.formState.errors.fullName.message
                : "Inserisci il tuo nome"
            }
          >
            <Input
              {...form.register("fullName")}
              isError={!!form?.formState?.errors?.fullName?.message}
            />
          </FormItem>
          <FormItem
            label="Email*"
            visibleDesc={!!form.formState.errors.email}
            descClassName="text-red-500"
            desc={
              form.formState.errors.fullName
                ? form.formState.errors.fullName.message
                : "Inserisci il tuo email"
            }
          >
            <Input
              {...form.register("email")}
              isError={!!form?.formState?.errors?.email?.message}
              disabled={true}
            />
          </FormItem>
          {/* ---- */}
          <FormItem
            label="Città*"
            visibleDesc={!!form.formState.errors.city}
            descClassName="text-red-500"
            desc={
              form.formState.errors.city
                ? form.formState.errors.city.message
                : "Inserisci la tua città"
            }
          >
            <Controller
              control={form.control}
              name="city"
              render={({ field: { value, onBlur, onChange } }) => {
                return (
                  <LocationSelect
                    value={value}
                    onChange={onChange}
                    onBlur={onBlur}
                    isError={!!form.formState.errors.city}
                  />
                )
              }}
            />
          </FormItem>
          <FormItem
            label="Data di nascita*"
            visibleDesc={!!form.formState.errors.dateOfBirth}
            descClassName="text-red-500"
            desc={
              form.formState.errors.dateOfBirth
                ? form.formState.errors.dateOfBirth.message
                : "Inserisci la tua data di nascita"
            }
          >
            <Controller
              control={form.control}
              name="dateOfBirth"
              render={({ field }) => (
                <Input
                  {...field}
                  onInput={(e) => {
                    maskedDateRef(e.currentTarget)
                    form.setValue("dateOfBirth", e.currentTarget.value)
                  }}
                  ref={(e) => {
                    field.ref(e)
                  }}
                  placeholder="Data di nascita"
                  isError={!!form?.formState?.errors?.dateOfBirth?.message}
                />
              )}
            />
          </FormItem>
          <FormItem label="Servizi aggiuntivi">
            <div className="mb-4 flex flex-row items-center justify-between gap-2 space-y-2 rounded-lg border p-4">
              <div className="flex w-full flex-col space-y-2">
                <p className="text-sm text-neutral-900 dark:text-neutral-100">
                  Lavoro da remoto
                </p>
                <p className="text-xs text-neutral-500 dark:text-neutral-400">
                  Offri servizi di consulenza anche da remoto?
                </p>
              </div>
              <Switch
                checked={form.watch("isRemote")}
                onChange={(value) => form.setValue("isRemote", value)}
                className={`${form.watch("isRemote") ? "bg-primary-500" : "bg-neutral-200"
                  } relative inline-flex h-[22px] w-[42px] shrink-0 cursor-pointer rounded-full border-4 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2  focus-visible:ring-white focus-visible:ring-opacity-75`}
              >
                <span
                  aria-hidden="true"
                  className={`${form.watch("isRemote") ? "translate-x-5" : "translate-x-0"
                    }
            pointer-events-none inline-block h-[14px] w-[14px] transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out`}
                />
              </Switch>
            </div>
          </FormItem>
          <FormItem
            label="Telefono"
            desc="Inserisci il tuo numero di telefono"
            descClassName="text-red-500"
            visibleDesc={!!form.formState.errors.telephone}
          >
            <Input
              placeholder="Telefono"
              {...form.register("telephone")}
              isError={!!form?.formState?.errors?.telephone?.message}
            />
          </FormItem>
          <FormItem
            label="Codice fiscale"
            visibleDesc={!!form.formState.errors.fiscalCode}
            descClassName="text-red-500"
            desc={
              form.formState.errors.fiscalCode
                ? form.formState.errors.fiscalCode.message
                : "Inserisci il tuo codice fiscale"
            }
          >
            <Input
              placeholder="Codice fiscale"
              {...form.register("fiscalCode")}
              isError={!!form?.formState?.errors?.fiscalCode?.message}
            />
          </FormItem>
          <FormItem
            label="Partita IVA"
            visibleDesc={!!form.formState.errors.taxId}
            descClassName="text-red-500"
            desc={
              form.formState.errors.taxId
                ? form.formState.errors.taxId.message
                : "Inserisci la tua partita IVA"
            }
          >
            <Input
              placeholder="P.IVA"
              {...form.register("taxId")}
              isError={!!form?.formState?.errors?.taxId?.message}
            />
          </FormItem>
          {/* ---- */}
          <FormItem
            label="Bio"
            desc={
              form.formState.errors.bio
                ? form.formState.errors.bio.message
                : "Inserisci una breve descrizione"
            }
            descClassName="text-red-500"
            visibleDesc={!!form.formState.errors.bio}
          >
            <Controller
              name="bio"
              control={form.control}
              render={({ field }) => (
                <Editor
                  toolbar={{
                    options: ["inline"],
                    inline: {
                      options: ["bold", "italic"],
                    },
                  }}
                  editorState={field.value ?? EditorState.createEmpty()}
                  onEditorStateChange={(editorState) =>
                    onEditorStateChange(editorState, field)
                  }
                  wrapperClassName="flex flex-col w-full bg-white !rounded-lg  border border-neutral-200 dark:border-neutral-700"
                  toolbarClassName="flex gap-x-6 border-b border-neutral-200 dark:border-neutral-700 p-2"
                  editorClassName="prose min-h-[200px] p-4"
                  handlePastedText={(text, html, editorState, onChange) => {
                    const currentContent = editorState.getCurrentContent()
                    const selection = editorState.getSelection()
                    const newContentState = Modifier.replaceText(
                      currentContent,
                      selection,
                      text
                    )

                    onChange(
                      EditorState.push(
                        editorState,
                        newContentState,
                        "insert-characters"
                      )
                    )
                    return true
                  }}
                />
              )}
            />
          </FormItem>
          <div className="pt-2">
            <ButtonPrimary>Aggiorna profilo</ButtonPrimary>
          </div>
        </div>
      </form>
    </div>
  )
}

export default AccountPage
