"use client"

import React from "react"

import ListedTags from "@/app/(client-components)/(Forms)/ListedTags"
import ServicesManager from "@/app/(client-components)/(Forms)/ServicesManager"
import ProfessionsManager from "@/app/(client-components)/(Forms)/ProfessionsManager"

const AccountSavelists = () => {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div>
        <h2 className="text-3xl font-semibold">Gestisci i tuoi servizi</h2>
        <span className="mt-2 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-3">
          Qui puoi configurare i tuoi servizi
        </span>
      </div>
      <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>

      <div>
        <div className="flex flex-col space-y-6 sm:space-y-12">
          <ServicesManager />
          <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
          <ProfessionsManager />
          <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
          <ListedTags />
        </div>
      </div>
    </div>
  )
}

export default AccountSavelists
