"use client"

import React, { useEffect, useState } from "react"
import Checkout from "../../(components)/Checkout"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
    UpdateConsultantDocument,
    UpdateConsultantMutation,
    UpdateConsultantMutationVariables,
    GetConsultantByTokenDocument,
    GetConsultantByTokenQuery,
    GetConsultantByTokenQueryVariables
} from "@/api/gql/generated"
import { fetcher } from "@/api/gql/fetcher"
import { useCurrentUser } from "@/hooks/useCurrentUser"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Stripe from "stripe"
import Link from "next/link"
import { useSearchParams } from 'next/navigation';
import { toast } from "sonner"
import Spinner from "@/components/Spinner"
import LoadingSpinner from "@/components/LoadingSpinner"
import formatDate from 'intl-dateformat'
import { sendGTMEvent } from "@next/third-parties/google"
import PartnerPlanCheckout from "../../(components)/PartnerPlanCheckout"
import stripe from 'stripe';

const Subscription = () => {
    const currentUser = useCurrentUser()
    const queryClient = useQueryClient()
    const searchParams = useSearchParams();
    const sessionId = searchParams.get("sessionId");
    const [isSubscriptionValid, setIsSubscriptionValid] = useState(false)
    const [isPartnerPlanValid, setIsPartnerPlanValid] = useState(false)
    const [subscription, setSubscription] = useState<Stripe.Response<Stripe.Subscription>>();
    const [partnersSubscription, setPartnersSubscription] = useState<Stripe.Response<Stripe.Subscription>>();
    const [isCheckingSubscriptionValidity, setIsCheckingSubscriptionValidity] = useState(false)
    const [isFetchingSubscriptionId, setIsFetchingSubscriptionId] = useState(false)
    const [isLoadingBillingPortal, setIsLoadingBillingPortal] = useState(false)
    const [product, setProduct] = useState<stripe.Product>();

    const {
        data: prefilledData,
        isLoading: prefilledLoading,
        isError: PrefilledLoading,
    } = useQuery({
        queryKey: ["getConsultantByToken"],
        queryFn: fetcher<
            GetConsultantByTokenQuery,
            GetConsultantByTokenQueryVariables
        >(
            GetConsultantByTokenDocument,
            {},
            {
                Authorization: `Bearer ${currentUser?.token}`,
            }
        ),
    })

    const { mutate } = useMutation<
        UpdateConsultantMutation,
        unknown,
        UpdateConsultantMutationVariables,
        unknown
    >({
        mutationKey: ["updateConsultant"],
        mutationFn: (variables?: UpdateConsultantMutationVariables) =>
            fetcher<UpdateConsultantMutation, UpdateConsultantMutationVariables>(
                UpdateConsultantDocument,
                variables,
                {
                    Authorization: `Bearer ${currentUser?.token}`,
                }
            )(),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["getConsultantByToken"],
            })
            toast.success("Informazioni aggiornate.")
        },
        onError: (e) => {
            toast.error("Errore durante l'aggiornamento.")
            throw new Error("Error occurred while updating the consultant:  " + e)
        },
        onSettled: () => {
            setIsFetchingSubscriptionId(false)

        }
    })

    const checkSubscriptionValidity = async (subscriptionId: string, isPartner?: boolean) => {
        setIsCheckingSubscriptionValidity(true);
        try {
            const response = await fetch('/api/check_subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ subscriptionId: subscriptionId }),
                cache: 'no-store'
            });
            const subscription = await response.json();
            if (subscription && subscription.subscription) {
                if (!isPartner) {
                    sendGTMEvent({
                        event: "purchase",
                        purchase_currency: "€",
                        purchase_value: subscription.subscription.items.data[0].price.unit_amount / 100,
                        purchase_transaction_id: subscription.subscription.id,
                        purchase_items: [
                            {
                                item_id: subscription.subscription.items.data[0].plan.id,
                                item_name: subscription.subscription.items.data[0].plan.product,
                            },
                        ],
                    })
                    if (typeof window !== "undefined" && window.fbq) {
                        try {
                            window.fbq("track", "Subscribe", {
                                value: subscription.subscription.items.data[0].price.unit_amount / 100,
                                currency: "EUR",
                                contents: [
                                    {
                                        id: subscription.subscription.items.data[0].plan.id,
                                        quantity: 1,
                                    },
                                ],
                            })
                        } catch (e) {
                            console.error(e)
                            throw new Error('Error firing the Subscribe fbq event:' + e)
                        }
                    }
                    setSubscription(subscription.subscription);
                }
                else {
                    setPartnersSubscription(subscription.subscription);
                }
            }
            return subscription.isValid;
        } catch (error) {
            console.error('Error checking subscription:', error);
            throw new Error('Error checking subscription:' + error)
        } finally {
            setIsCheckingSubscriptionValidity(false);

        }
    }

    useEffect(() => {
        if (sessionId) {
            setIsFetchingSubscriptionId(true);
            const fetchSubscriptionId = async () => {
                try {
                    const response = await fetch('/api/subscription_id?sessionId=' + sessionId, { cache: 'no-store' })
                    const responseData = await response.json();
                    if (!prefilledData?.byTokenConsultant?.id) {
                        toast.error("Errore durante l'aggiornamento.")
                        setIsFetchingSubscriptionId(false)
                        return
                    }
                    mutate({
                        input: {
                            id: prefilledData.byTokenConsultant.id,
                            stripeSubscriptionId: responseData.subscriptionId
                        },
                    })
                } catch (error) {
                    console.error("Error fetching subscription id: ", error)
                    throw new Error("Error fetching subscription id: " + error)
                } finally {
                    setIsFetchingSubscriptionId(false)
                }
            }
            fetchSubscriptionId()
        }

        if (prefilledData?.byTokenConsultant?.stripeSubscriptionId) {
            checkSubscriptionValidity(prefilledData?.byTokenConsultant?.stripeSubscriptionId).then(
                (isValid) => {
                    setIsSubscriptionValid(isValid)
                }
            )
        }
        if (prefilledData?.byTokenConsultant?.stripePartnerPlanSubscriptionId) {
            checkSubscriptionValidity(prefilledData?.byTokenConsultant?.stripePartnerPlanSubscriptionId, true).then(
                (isValid) => {
                    setIsPartnerPlanValid(isValid)
                }
            )
        }

    }, [prefilledData])

    useEffect(() => {
        const fetchProduct = async () => {
            try {
                const response = await fetch('/api/product', {
                    method: 'POST',
                    headers: { 'Cache-Control': 'no-cache' },
                    body: JSON.stringify({ productType: 'basic' })
                });
                const productsData = await response.json();
                setProduct(productsData.product);
            } catch (error) {
                console.error('Error fetching products:', error);
                throw new Error('Error fetching products: ' + error);
            }
        };

        fetchProduct();
    }, [prefilledData]);

    const redirectToBillingPortal = async () => {
        setIsLoadingBillingPortal(true);
        try {
            const response = await fetch(`/api/customer_portal?customerId=${subscription?.customer}`, { cache: 'no-store' });
            const session = await response.json();
            window.open(session.url, "_blank");
        } catch (error) {
            console.error('Error redirecting to Customer Portal:', error);
            throw new Error('Error redirecting to Customer Portal:' + error)
        } finally {
            setIsLoadingBillingPortal(false);
        }
    }

    async function updateSubscriptions() {
        try {
            const response = await fetch('/api/update_subscriptions', {
                method: 'POST',
                body: JSON.stringify({ priceId: product?.default_price })
            });
            const data = await response.json();
            if (data.success) {
                alert('Subscriptions updated successfully!');
            } else {
                alert(`Error: ${data.error}`);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('An unexpected error occurred.');
        }
    }

    return (
        <div className="space-y-6 sm:space-y-8">
            <div>
                <h2 className="text-3xl font-semibold">Il tuo Abbonamento</h2>
            </div>
            <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
            {prefilledLoading || isCheckingSubscriptionValidity || isFetchingSubscriptionId ?
                <Spinner />
                :
                !isPartnerPlanValid && !isCheckingSubscriptionValidity ?
                    <div className="lg:flex lg:space-x-10 max-lg:space-y-10 lg:divide-x divide-gray-400 max-lg:divide-y">
                        <div className="w-full">
                            <div className="mt-2 block text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg md:mt-3">
                                {subscription && isSubscriptionValid ?
                                    <div>
                                        Il tuo abbonamento Basic è <b className="text-green-700">attivo</b> fino al <b>{formatDate(new Date(subscription.current_period_end * 1000), 'DD MMMM YYYY', { locale: 'it' })}</b>.
                                        <br /><br />
                                        In questa area puoi:
                                        <ul className="mt-5 list-disc ml-6" style={{ listStyleType: 'disc' }}>
                                            <li>Gestire i tuoi dati di fatturazione.</li>
                                            <li>Aggiornare i metodi di pagamento.</li>
                                        </ul>
                                        <br />
                                        Per qualsiasi domanda, contattaci all’indirizzo: <Link href="mailto:<EMAIL>" className="text-primary-700 underline"><EMAIL></Link>.
                                        <br /><br />
                                        Ricorda: con l’abbonamento Basic, il tuo profilo è visibile ai visitatori, e puoi ricevere richieste di appuntamento per tutto il periodo di validità.
                                    </div>
                                    :
                                    <div>
                                        <h3 className="text-gray-900 font-medium text-2xl mb-5">Basic Plan</h3>
                                        Pubblica il tuo profilo e presenta il tuo valore a nuovi clienti.
                                        <br /><br />
                                        <span>
                                            Sottoscrivendo l’abbonamento Basic, potrai attivare il tuo profilo su&nbsp;
                                            <Link
                                                href="https://consulenteideale.it"
                                                className="text-primary-700 underline"
                                            >
                                                consulenteideale.it
                                            </Link>
                                            &nbsp;e iniziare a ricevere richieste dai nostri visitatori.
                                        </span>
                                        <br /><br />
                                        Cosa include l’abbonamento Basic:
                                        <ul className="mt-5 list-disc ml-6">
                                            <li>Profilo pubblico visibile sul nostro portale.</li>
                                            <li>Opportunità di ricevere richieste di appuntamento dai clienti.</li>
                                            <li>Accesso alla newsletter con strategie e suggerimenti per il tuo business.</li>
                                            <li>Connessione a un network di esperti e collaboratori.</li>
                                        </ul>
                                        <br />
                                        Attrai nuovi clienti: i nostri 50,000 visitatori mensili ti aspettano!
                                    </div>
                                }
                            </div>
                            {isSubscriptionValid ?
                                <ButtonPrimary onClick={redirectToBillingPortal} className="!mt-10">
                                    {isLoadingBillingPortal && <LoadingSpinner />} Gestisci il tuo abbonamento
                                </ButtonPrimary>
                                :
                                <Checkout product={product} email={prefilledData?.byTokenConsultant?.email} />
                            }
                        </div>

                        <PartnerPlanCheckout
                            product={product}
                            isSubscriptionValid={isSubscriptionValid}
                            email={prefilledData?.byTokenConsultant?.email}
                        />
                    </div>
                    :
                    isPartnerPlanValid && !isCheckingSubscriptionValidity && subscription && partnersSubscription &&
                    <div>
                        <p className="explanation-section text-base font-normal text-neutral-500 dark:text-neutral-400 sm:text-lg">
                            Stai usufruendo di tutti i benefici di Consulente Ideale!
                            <br /><br />
                            Il tuo abbonamento <b>Basic</b> è attivo fino al <b>{formatDate(new Date(subscription.current_period_end * 1000), 'DD MMMM YYYY', { locale: 'it' })}</b>.
                            <br />
                            Il tuo abbonamento <b>Partners</b> è attivo fino al <b>{formatDate(new Date(partnersSubscription.current_period_end * 1000), 'DD MMMM YYYY', { locale: 'it' })}</b>.
                            <br /><br />
                            Con i tuoi abbonamenti Basic e Partners attivi, hai accesso a un set completo di strumenti per la crescita del tuo business:
                            <ul className="mt-5 list-disc ml-6">
                                <li>Profilo attivo e visibile sul portale.</li>
                                <li>Richieste di appuntamento dai visitatori.</li>
                                <li>Formazione gratuita mensile.</li>
                                <li>Supporto per accedere ai bandi.</li>
                                <li>Posizionamento nella Top10 della homepage.</li>
                            </ul>
                            <br /><br />
                            In questa area puoi gestire entrambi gli abbonamenti, aggiornare i dati di fatturazione e modificare i metodi di pagamento.
                            <br /><br />
                            Per assistenza, scrivici a: <Link href="mailto:<EMAIL>" className="text-primary-700 underline"><EMAIL></Link>.
                        </p>
                        <ButtonPrimary onClick={redirectToBillingPortal} className="!mt-20">
                            {isLoadingBillingPortal && <LoadingSpinner />} Gestisci il tuo abbonamento
                        </ButtonPrimary>
                    </div>
            }
            {/* <ButtonPrimary
                onClick={updateSubscriptions}
                className="p-2 text-sm"
            >
                Update Subscriptions
            </ButtonPrimary> */}
        </div>
    )
}

export default Subscription
