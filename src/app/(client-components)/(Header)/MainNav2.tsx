import React, { FC } from "react"

import ButtonSecondary from "@/shared/ButtonSecondary"
import Logo from "@/shared/Logo"

import LoginRegisterDropdown from "./LoginRegisterDropdown"
import MenuDropdown from "./MenuDropdown"
import { MobileDrawer } from "./MobileDrawer"

export interface MainNav2Props {
  className?: string
}

const MainNav2: FC<MainNav2Props> = ({ className = "" }) => {
  return (
    <div className={`MainNav2 relative z-10 ${className}`}>
      <div className="container flex h-auto items-center justify-between px-4">
        <div className="flex-1 justify-start">
          <Logo className="w-28 self-center py-2 lg:w-40" />
        </div>

        <div className="flex flex-1 flex-shrink-0 items-center justify-end gap-x-8 py-2 text-neutral-700 dark:text-neutral-100 lg:flex-none">
          <div className="flex items-center space-x-2">
            <ButtonSecondary
              className="hidden text-xs sm:text-xs lg:flex"
              href={"/come-funziona"}
            >
              Come funziona?
            </ButtonSecondary>
            <ButtonSecondary
              className="hidden text-xs sm:text-xs lg:flex"
              href="/sei-un-professionista"
            >
              Sei un professionista?
            </ButtonSecondary>
            <LoginRegisterDropdown />
            <MenuDropdown />
          </div>
        </div>
      </div>
    </div>
  )
}

export default MainNav2
