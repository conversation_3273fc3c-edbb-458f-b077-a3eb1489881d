"use client"

import { FC, useEffect } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { isEmpty } from "lodash"

import { fetcher } from "@/api/gql/fetcher"
import {
  GetConsultantByTokenDocument,
  GetConsultantByTokenQuery,
  GetConsultantByTokenQueryVariables,
} from "@/api/gql/generated"
import { useCurrentUser } from "@/hooks/useCurrentUser"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Spinner from "@/components/Spinner"

import AvatarDropdown from "./AvatarDropdown"

const LoginRegisterDropdown: FC = ({ }) => {
  const { data: session, status } = useSession()
  const currentUser = useCurrentUser()
  const queryClient = useQueryClient()
  const router = useRouter()

  const { data: userData, isLoading } = useQuery({
    queryKey: ["getConsultantByToken"],
    queryFn: fetcher<
      GetConsultantByTokenQuery,
      GetConsultantByTokenQueryVariables
    >(
      GetConsultantByTokenDocument,
      {},
      {
        Authorization: `Bearer ${currentUser?.token}`,
      }
    ),
    enabled: !!currentUser?.token,
  })

  useEffect(() => {
    if (status === "authenticated" || status === "unauthenticated") {
      queryClient.invalidateQueries({ queryKey: ["getConsultantByToken"] })
    }
  }, [status, queryClient])

  const handleLogout = async () => {
    try {
      const result = await signOut()
      if (result) {
        queryClient.invalidateQueries({ queryKey: ["getConsultantByToken"] })
        router.push("/")
      }
    }
    catch (error) {
      console.error("Error signing out", error)
    }
  }

  return isLoading ? (
    <Spinner />
  ) : status === "authenticated" && !isEmpty(currentUser) && userData?.byTokenConsultant?._id ? (
    <AvatarDropdown
      imgUrl={userData?.byTokenConsultant?.avatar}
      name={userData?.byTokenConsultant?.fullName}
      onLogout={handleLogout}
    />
  ) : (
    <ButtonPrimary
      className="flex bg-secondary-6000 text-xs hover:bg-secondary-700 sm:text-xs"
      href="/auth/login"
    >
      Accedi
    </ButtonPrimary>
  )
}

export default LoginRegisterDropdown
