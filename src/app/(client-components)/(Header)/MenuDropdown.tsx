"use client"

import { FC, useMemo } from "react"
import { usePathname } from "next/navigation"
import { openChat } from "@/utils/HubspotInteractions"
import { Bars3Icon, ChatBubbleLeftRightIcon } from "@heroicons/react/24/outline"

import { cn } from "@/lib/utils"
import { useMenuItems } from "@/hooks/useMenuItems"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

const MenuDropdown: FC = () => {
  const pathname = usePathname()
  const isActive = useMemo(() => {
    return (href: string) => {
      return pathname === href
    }
  }, [pathname])

  const menuItems = useMenuItems()

  return (
    <div className="MenuDropdown hidden md:flex">
      <Popover>
        <PopoverTrigger asChild>
          <Button className="nc-Button ttnc-ButtonPrimary relative flex h-auto items-center justify-center rounded-full bg-primary-6000 px-4 py-3 text-xs text-neutral-50 transition-colors hover:bg-primary-700 disabled:bg-opacity-70 sm:px-6 sm:text-xs">
            <Bars3Icon className="h-4 w-4 opacity-80" />
            <span className="ml-2 select-none">Menu</span>
          </Button>
        </PopoverTrigger>

        <PopoverContent
          align="end"
          className="mt-2 w-80 space-y-3 rounded-xl bg-white shadow-lg ring-1 ring-black ring-opacity-5"
        >
          <div className="relative grid gap-8 bg-white p-3 dark:bg-neutral-800">
            {menuItems.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className={cn(
                  `-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-secondary-500 hover:text-white focus:outline-none`,
                  isActive(item.href)
                    ? "pointer-events-none bg-primary-6000 text-white dark:bg-neutral-700"
                    : "opacity-80"
                )}
              >
                <item.icon className="h-[18px] w-[18px] " />
                <p className="ml-2 text-sm font-medium ">{item.name}</p>
              </a>
            ))}
            <PopoverClose
              onClick={openChat}
              className={cn(
                `-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-secondary-500 hover:text-white focus:outline-none`,
                "opacity-80"
              )}
            >
              <ChatBubbleLeftRightIcon className="h-[18px] w-[18px] " />
              <p className="ml-2 text-sm font-medium ">Chiedi aiuto</p>
            </PopoverClose>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
export default MenuDropdown
