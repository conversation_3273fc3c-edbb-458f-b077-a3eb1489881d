"use client"

import { useRouter } from "next/navigation"
import { logout } from "@/actions/logout"
import { useQueryClient } from "@tanstack/react-query"

import { cn } from "@/lib/utils"

const LogoutButton = ({
  children,
  className,
  onLogout
}: {
  children: React.ReactNode
  className: string
  onLogout: Function
}) => {
  const handleLogout = async () => {
    await logout()
  }

  return (
    <span
      onClick={async () => await onLogout()}
      className={cn("cursor-pointer", className)}
    >
      {children}
    </span>
  )
}

export default LogoutButton
