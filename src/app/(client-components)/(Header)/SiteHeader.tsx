"use client"

import React, { useEffect, useRef, useState } from "react"
import { usePathname } from "next/navigation"
import { PathName } from "@/routers/types"
import { useThemeMode } from "@/utils/useThemeMode"

import { PATHS_WITH_SEARCH_HEADER } from "@/lib/constants"

import Header from "./Header"
import Header3 from "./Header3"

export type SiteHeaders = "HomeHeader" | "SiteHeader"

let OPTIONS = {
  root: null,
  rootMargin: "0px",
  threshold: 1.0,
}
let OBSERVER: IntersectionObserver | null = null
const PAGES_HIDE_HEADER_BORDER: PathName[] = []

const SiteHeader = () => {
  const anchorRef = useRef<HTMLDivElement>(null)

  const [headerSelected, setHeaderSelected] =
    useState<SiteHeaders>("HomeHeader")

  const [isTopOfPage, setIsTopOfPage] = useState(true)

  useEffect(() => {
    setIsTopOfPage(window.pageYOffset < 5)
  }, [])
  //
  useThemeMode()
  //
  const pathname = usePathname()

  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setIsLoading(false)
  }, [])

  const intersectionCallback = (entries: IntersectionObserverEntry[]) => {
    entries.forEach((entry) => {
      setIsTopOfPage(entry.isIntersecting)
    })
  }

  useEffect(() => {
    // Logic to update header based on the new pathname
    const newHeader = PATHS_WITH_SEARCH_HEADER.some((path) =>
      pathname.includes(path)
    )
      ? "SiteHeader"
      : "HomeHeader"

    setHeaderSelected(newHeader)
  }, [pathname])

  useEffect(() => {
    // disconnect the observer
    // observer for show the LINE bellow header
    if (!PAGES_HIDE_HEADER_BORDER.includes(pathname as PathName)) {
      OBSERVER && OBSERVER.disconnect()
      OBSERVER = null
      return
    }
    if (!OBSERVER) {
      OBSERVER = new IntersectionObserver(intersectionCallback, OPTIONS)
      anchorRef.current && OBSERVER.observe(anchorRef.current)
    }
  }, [pathname])

  const renderHeader = () => {
    if (isLoading) {
      return null
    }
    let headerClassName = "shadow-sm dark:border-b dark:border-neutral-700"
    if (PAGES_HIDE_HEADER_BORDER.includes(pathname as PathName)) {
      headerClassName = isTopOfPage
        ? ""
        : "shadow-sm dark:border-b dark:border-neutral-700"
    }
    switch (headerSelected) {
      case "HomeHeader":
        return <Header className={headerClassName} navType="MainNav2" />
      default:
        return <Header3 className={headerClassName} />
    }
  }

  return (
    <>
      {renderHeader()}
      <div ref={anchorRef} className="invisible absolute h-1"></div>
    </>
  )
}

export default SiteHeader
