import { forwardRef, ReactNode } from "react"
import Link from "next/link"
import { CalendarIcon } from "@heroicons/react/24/outline"

import Avatar from "@/shared/Avatar"
import ButtonPrimary from "@/shared/ButtonPrimary"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import LogoutButton from "./LogoutButton"

interface Props {
  imgUrl?: string | null
  className?: string
  name?: string
  onLogout: Function
}

export default function AvatarDropdown({
  imgUrl,
  name,
  className = "",
  onLogout
}: Props) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="link" size="icon">
          <Avatar imgUrl={imgUrl} sizeClass="w-8 h-8 sm:w-9 sm:h-9" />
        </Button>
      </PopoverTrigger>

      <PopoverContent
        align="end"
        className="mt-2 w-80 space-y-3 rounded-xl bg-white shadow-lg ring-1 ring-black ring-opacity-5"
      >
        <div className="grid gap-4">
          <LinkAndClose
            href={"/account"}
            className="-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-neutral-100 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50 dark:hover:bg-neutral-700"
          >
            <div className="flex items-center space-x-3">
              <Avatar imgUrl={imgUrl} sizeClass="w-12 h-12" />

              <div className="flex-grow">
                <h4 className="font-semibold">{name}</h4>
              </div>
            </div>
          </LinkAndClose>
          <div className="w-full border-b border-neutral-200 dark:border-neutral-700" />
          <div className="grid gap-5">
            <LinkAndClose
              href={"/account"}
              className="-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-neutral-100 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50 dark:hover:bg-neutral-700"
            >
              <div className="flex flex-shrink-0 items-center justify-center text-neutral-500 dark:text-neutral-300">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.1601 10.87C12.0601 10.86 11.9401 10.86 11.8301 10.87C9.45006 10.79 7.56006 8.84 7.56006 6.44C7.56006 3.99 9.54006 2 12.0001 2C14.4501 2 16.4401 3.99 16.4401 6.44C16.4301 8.84 14.5401 10.79 12.1601 10.87Z"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M7.15997 14.56C4.73997 16.18 4.73997 18.82 7.15997 20.43C9.90997 22.27 14.42 22.27 17.17 20.43C19.59 18.81 19.59 16.17 17.17 14.56C14.43 12.73 9.91997 12.73 7.15997 14.56Z"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium ">{"Il mio profilo"}</p>
              </div>
            </LinkAndClose>
            <LinkAndClose
              href={"/account/servizi"}
              className="-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-neutral-100 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50 dark:hover:bg-neutral-700"
            >
              <div className="flex flex-shrink-0 items-center justify-center text-neutral-500 dark:text-neutral-300">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M8 12.2H15"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M8 16.2H12.38"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M10 6H14C16 6 16 5 16 4C16 2 15 2 14 2H10C9 2 8 2 8 4C8 6 9 6 10 6Z"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M16 4.02002C19.33 4.20002 21 5.43002 21 10V16C21 20 20 22 15 22H9C4 22 3 20 3 16V10C3 5.44002 4.67 4.20002 8 4.02002"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium ">{"I miei Servizi"}</p>
              </div>
            </LinkAndClose>
            <LinkAndClose
              href={"/account/appuntamenti"}
              className="-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-neutral-100 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50 dark:hover:bg-neutral-700"
            >
              <div className="flex flex-shrink-0 items-center justify-center text-neutral-500 dark:text-neutral-300">
                <CalendarIcon className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium "> Appuntamenti</p>
              </div>
            </LinkAndClose>
          </div>
          <div className="w-full border-b border-neutral-200 dark:border-neutral-700" />

          <LogoutButton className="-m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-neutral-100 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50 dark:hover:bg-neutral-700" onLogout={onLogout}>
            <div className="flex flex-shrink-0 items-center justify-center text-neutral-500 dark:text-neutral-300">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.90002 7.55999C9.21002 3.95999 11.06 2.48999 15.11 2.48999H15.24C19.71 2.48999 21.5 4.27999 21.5 8.74999V15.27C21.5 19.74 19.71 21.53 15.24 21.53H15.11C11.09 21.53 9.24002 20.08 8.91002 16.54"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M15 12H3.62"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M5.85 8.6499L2.5 11.9999L5.85 15.3499"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium ">Esci</p>
            </div>
          </LogoutButton>
        </div>
      </PopoverContent>
    </Popover>
  )
}

interface LinkAndCloseProps {
  href: string
  className?: string
  children: ReactNode
}

// Using forwardRef to forward the ref to the underlying anchor tag for Next.js Link compatibility
const LinkAndClose = forwardRef<HTMLAnchorElement, LinkAndCloseProps>(
  ({ href, className, children }, ref) => {

    const handleClick = (e: any) => {
      e.preventDefault(); // Prevent Next.js client-side navigation
      window.location.href = href; // Trigger a full page reload
    };
    return (
      <PopoverClose asChild>
        <Link href={href} passHref className={className} ref={ref} onClick={handleClick}>
          {children}
        </Link>
      </PopoverClose>
    )
  }
)

LinkAndClose.displayName = "LinkAndClose"
