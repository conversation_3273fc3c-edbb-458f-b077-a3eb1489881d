"use client"

import React, { FC, useEffect, useRef } from "react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { Route } from "@/routers/types"
import { searchAtom, searchCityAtom, searchServiceAtom } from "@/store/search"
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline"
import { useAtom, useAtomValue } from "jotai"

import useOutsideAlerter from "@/hooks/useOutsideAlerter"
import Logo from "@/shared/Logo"

import HeroSearchForm2MobileFactory from "../(HeroSearchForm2Mobile)/HeroSearchForm2MobileFactory"
import HeroSearchFormSmall from "../(HeroSearchFormSmall)/HeroSearchFormSmall"
import AvatarDropdown from "./AvatarDropdown"
import LoginRegisterDropdown from "./LoginRegisterDropdown"
import MenuDropdown from "./MenuDropdown"
import { MobileDrawer } from "./MobileDrawer"

interface Header3Props {
  className?: string
}

let WIN_PREV_POSITION = 0
if (typeof window !== "undefined") {
  WIN_PREV_POSITION = (window as any).pageYOffset
}

const Header3: FC<Header3Props> = ({ className = "" }) => {
  const city = useAtomValue(searchCityAtom)
  const service = useAtomValue(searchServiceAtom)

  const headerInnerRef = useRef<HTMLDivElement>(null)
  const [showHeroSearch, setShowHeroSearch] = useAtom(searchAtom)

  useOutsideAlerter(headerInnerRef, () => {
    setShowHeroSearch(null)
  })

  let searchParams = useSearchParams()

  useEffect(() => {
    setShowHeroSearch(null)
  }, [searchParams, setShowHeroSearch])

  // HIDDEN WHEN SCROLL EVENT
  useEffect(() => {
    window.addEventListener("scroll", handleEvent)
    return () => {
      window.removeEventListener("scroll", handleEvent)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleEvent = () => {
    window.requestAnimationFrame(handleHideSearchForm)
  }

  const handleHideSearchForm = () => {
    if (!document.querySelector("#nc-Header-3-anchor")) {
      return
    }
    //
    let currentScrollPos = window.pageYOffset
    if (
      WIN_PREV_POSITION - currentScrollPos > 100 ||
      WIN_PREV_POSITION - currentScrollPos < -100
    ) {
      setShowHeroSearch(null)
    } else {
      return
    }
    WIN_PREV_POSITION = currentScrollPos
  }

  const renderHeroSearch = () => {
    return (
      <div
        className={`absolute inset-x-0 top-0 transition-all will-change-[transform,opacity] ${
          showHeroSearch
            ? "visible"
            : "pointer-events-none invisible -translate-x-0 -translate-y-[90px] scale-x-[0.395] scale-y-[0.6] opacity-0"
        }`}
      >
        <div className={`mx-auto w-full max-w-4xl pb-6`}>
          <HeroSearchFormSmall
            defaultFieldFocus={showHeroSearch || undefined}
          />
        </div>
      </div>
    )
  }

  const renderButtonOpenHeroSearch = () => {
    return (
      <div
        className={`relative flex w-full items-center justify-between rounded-full border border-neutral-200 shadow transition-all hover:shadow-md dark:border-neutral-6000 ${
          showHeroSearch
            ? "pointer-events-none invisible -translate-x-0 translate-y-20 scale-x-[2.55] scale-y-[1.8] opacity-0"
            : "visible"
        }`}
      >
        <div className="flex items-center text-sm font-medium">
          <span
            onClick={() => setShowHeroSearch("service")}
            className="block max-w-[10rem] cursor-pointer truncate py-3 pl-5 pr-4 capitalize"
          >
            {service || "Professionisti"}
          </span>
          {city ? (
            <>
              <span className="h-5 w-[1px] bg-neutral-300 dark:bg-neutral-700"></span>
              <span
                onClick={() => setShowHeroSearch("location")}
                className="block cursor-pointer px-4 py-3 capitalize"
              >
                {city}
              </span>
            </>
          ) : null}
        </div>

        <div
          className="ml-auto flex-shrink-0 cursor-pointer pr-2"
          onClick={() => setShowHeroSearch("service")}
        >
          <span className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-6000  text-white">
            <MagnifyingGlassIcon className="h-5 w-5" />
          </span>
        </div>
      </div>
    )
  }

  return (
    <>
      <div
        className={`nc-Header nc-Header-3 fixed inset-0 top-0 z-40 bg-black/30 transition-opacity will-change-[opacity] dark:bg-black/50 ${
          showHeroSearch ? "visible" : "pointer-events-none invisible opacity-0"
        }`}
      ></div>
      {showHeroSearch && <div id="nc-Header-3-anchor"></div>}
      <header
        ref={headerInnerRef}
        className={`sticky top-0 z-40 flex ${className}`}
      >
        <div
          className={`absolute inset-x-0 top-0 h-full bg-white transition-transform will-change-[transform,opacity] dark:bg-neutral-900
          ${showHeroSearch ? "scale-y-[2.4] duration-75" : ""}
          `}
        ></div>
        <div className="relative flex w-full px-4 lg:container">
          <div className="flex flex-1 items-center justify-between gap-x-4 py-4">
            {/* Logo (lg+) */}
            <div className="relative z-10 flex-1 items-center">
              <Logo className="hidden w-28 self-center py-2 md:flex lg:w-40" />
            </div>

            <div className="mx-auto flex flex-grow lg:flex-none">
              <div className="hidden flex-1 self-center lg:flex">
                {renderButtonOpenHeroSearch()}
              </div>
              <div className="mx-auto w-full max-w-lg flex-1 self-center lg:hidden">
                <HeroSearchForm2MobileFactory />
              </div>
              {renderHeroSearch()}
            </div>
            {/* NAV */}
            <div className="relative z-10 flex flex-1 items-center justify-end gap-x-4">
              <LoginRegisterDropdown />
              <MenuDropdown />
            </div>
          </div>
        </div>
      </header>
    </>
  )
}

export default Header3
