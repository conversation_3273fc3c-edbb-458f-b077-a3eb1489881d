import React, { <PERSON> } from "react"

import RentalCarSearchForm from "./(car-search-form)/RentalCarSearchForm"

export interface HeroSearchFormProps {
  className?: string
}

const HeroSearchForm: FC<HeroSearchFormProps> = ({ className = "" }) => {
  return (
    <div className={`nc-HeroSearchForm w-full py-5 lg:py-0 ${className}`}>
      <RentalCarSearchForm />
    </div>
  )
}

export default HeroSearchForm
