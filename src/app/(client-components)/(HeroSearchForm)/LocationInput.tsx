"use client"

import * as React from "react"
import { useCallback, useEffect, useRef } from "react"
import { useGetCitiesQuery } from "@/api/gql/generated"
import {
  isOnlineAtom,
  isSearchLoading,
  searchCityAtom,
  searchInputLocationErrorAtom,
  searchPathCityAtom,
} from "@/store/search"
import {
  BuildingOffice2Icon,
  VideoCameraIcon,
} from "@heroicons/react/24/outline"
import { useAtom, useAtomValue } from "jotai"
import { isEmpty } from "lodash"

import { cn } from "@/lib/utils"
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"

export type MainSearchProps = {
  autoFocus?: boolean
  placeHolder?: string
  desc?: string
}

const MainSearch: React.FC<MainSearchProps> = ({
  autoFocus = false,
  placeHolder = "Città",
  desc = "Dove ti trovi?",
}) => {
  const [showPopover, setShowPopover] = React.useState(autoFocus)
  const [value, setValue] = useAtom(searchCityAtom)
  const [, setPath] = useAtom(searchPathCityAtom)
  const [, setInputError] = useAtom(searchInputLocationErrorAtom)
  const [, setLoading] = useAtom(isSearchLoading)
  const isOnline = useAtomValue(isOnlineAtom)

  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleCleanup = useCallback(() => {
    setValue("")
    setPath(null)
    inputRef.current?.focus()
  }, [setValue, setPath])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowPopover(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  const { data, isLoading } = useGetCitiesQuery(
    {
      name: value,
    },
    {
      select: (data) =>
        data?.cities?.collection?.map((city) => ({
          label: city?.name,
          path: city?.urlKey,
        })),
    }
  )

  useEffect(() => {
    setLoading(isLoading)
  }, [isLoading, setLoading])

  const handleValueChange = (newValue: string) => {
    setValue(newValue)
    if (!showPopover) {
      setShowPopover(true)
    }
  }

  useEffect(() => {
    const isInvalidValue =
      !data?.some(
        (framework) => framework.label?.toLowerCase() === value?.toLowerCase()
      ) || value === ""
    setInputError(isInvalidValue)
  }, [data, value, setInputError, isOnline])

  const handleSelect = (currentValue: string) => {
    const pickPath = data?.find(
      (framework) =>
        framework.label?.toLowerCase() === currentValue.toLowerCase()
    )?.path
    setPath(pickPath || null)
    setValue(currentValue)
    setShowPopover(false)
  }

  return (
    <div className="relative flex flex-1" ref={containerRef}>
      <Command shouldFilter={false}>
        <div
          onClick={() => {
            setShowPopover(!isLoading)
          }}
          className={cn(
            `[ nc-hero-field-padding ] relative z-10 flex flex-1 flex-shrink-0 cursor-pointer items-center space-x-3 text-left focus:outline-none`,
            isLoading && "pointer-events-none"
          )}
        >
          <div className="text-neutral-300 dark:text-neutral-400">
            {!isOnline ? (
              <BuildingOffice2Icon className="h-7 w-7" />
            ) : (
              <VideoCameraIcon className="h-7 w-7" />
            )}
          </div>
          <div className="flex-grow">
            <CommandInput
              placeholder="Città"
              value={value ? value : ""}
              onValueChange={handleValueChange}
              ref={inputRef}
              className="capitalize"
              onFocus={() => {
                setShowPopover(true)
              }}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  handleCleanup()
                } else if (e.key === "Tab") {
                  setShowPopover(false)
                }
              }}
            />
            <span className="mt-0.5 block text-sm font-light text-neutral-400 ">
              <span className="line-clamp-1">
                {isOnline ? "A distanza" : !!value ? placeHolder : desc}
              </span>
            </span>
            {value && (
              <ClearDataButton
                onClick={(e) => {
                  e.stopPropagation()
                  handleCleanup()
                }}
              />
            )}
          </div>
        </div>
        {showPopover && (
          <div className="absolute left-0 top-full z-40 mt-5  w-full min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-4 ring-primary-300 ring-offset-2 transition-all dark:bg-neutral-800 sm:py-6">
            <div className="max-h-48 overflow-y-auto md:max-h-96">
              {!isLoading && isEmpty(data) ? (
                <div className="flex items-center justify-start gap-x-2 px-5 text-base">
                  <span className="text-base">Nessuna Città trovata.</span>
                  <ClearDataButton
                    className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCleanup()
                    }}
                  >
                    Cancella Filtri
                  </ClearDataButton>
                </div>
              ) : null}
              <CommandGroup>
                {data?.map((framework) => (
                  <CommandItem
                    key={framework.path}
                    value={framework.label}
                    onSelect={handleSelect}
                  >
                    {framework.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </div>
          </div>
        )}
      </Command>
    </div>
  )
}

export default MainSearch
