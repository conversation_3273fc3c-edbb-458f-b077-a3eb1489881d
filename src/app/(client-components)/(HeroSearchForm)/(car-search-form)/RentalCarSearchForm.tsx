"use client"

import { FC } from "react"
import { Route } from "next"
import { useRouter } from "next/navigation"
import {
  byType<PERSON>tom,
  isOnlineAtom,
  isSearchLoading,
  oldCity, professionOrService,
  searchCityAtom,
  searchCursorAtom,
  searchInputErrorAtom,
  searchInputLocationErrorAtom,
  searchPathServiceAtom,
} from "@/store/search"
import {
  BuildingOffice2Icon,
  VideoCameraIcon,
} from "@heroicons/react/24/outline"
import { useAtom, useAtomValue } from "jotai"

import MainSearch from "@/components/MainSearch"

import ButtonSubmit from "../ButtonSubmit"
import LocationInput from "../LocationInput"
var capitalize = require('capitalize')

export interface ServiceSearchFormProps { }

const ServiceSearchForm: FC<ServiceSearchFormProps> = ({ }) => {
  const [byType, setByType] = useAtom(byTypeAtom)
  const [oldLocation, setOldLocation] = useAtom(oldCity)
  const [location, setLocation] = useAtom(searchCityAtom)
  const [loading, setIsLoading] = useAtom(isSearchLoading)
  const servicePath = useAtomValue(searchPathServiceAtom)
  const typeSerOrProf = useAtomValue(professionOrService)
  const inputLocationError = useAtomValue(searchInputLocationErrorAtom)
  const inputServiceError = useAtomValue(searchInputErrorAtom)
  const router = useRouter()
  const [isOnline, setIsOnline] = useAtom(isOnlineAtom)
  const [, setCursor] = useAtom(searchCursorAtom)

  const handleClick = async (type: "byCity" | "online" | null) => {
    setByType(type)
    if (type === "byCity") {
      setLocation(oldLocation)
      setIsOnline(false)
    }
    if (type === "online") {
      setOldLocation(location)
      setLocation(null)
      setIsOnline(true)
    }
  }

  const buildQueryFilters = () => {
    const filters = []
    if (isOnline) filters.push("online=true")
    return filters.length > 0 ? `?${filters.join("&")}` : ""
  }

  const buildTargetPath = () => {
    const locationPath = location && !inputLocationError ? `/${capitalize.words(location)}` : ""
    const servicePathSuffix = servicePath ? `${servicePath}` : ""
      return `${typeSerOrProf==='service'?'/consulenza':'consulenti'}${servicePathSuffix}${locationPath.replace(/ /g, "-")}${buildQueryFilters()}`
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    router.push(buildTargetPath() as Route)
  }

  const renderRadioBtn = () => {
    return (
      <div className=" [ nc-hero-field-padding ] flex flex-row flex-wrap items-center border-b border-neutral-100 py-5 dark:border-neutral-700">
        <div
          className={`my-1 mr-2 flex cursor-pointer items-center rounded-full px-4 py-1.5 text-xs font-medium sm:mr-3 ${byType === "online"
            ? "bg-primary-6000 text-white shadow-lg shadow-black/10 dark:bg-primary-500"
            : "border border-neutral-300 dark:border-neutral-700"
            }`}
          onClick={async () => await handleClick("online")}
        >
          <VideoCameraIcon className="mr-1 h-4 w-4" />
          Online
        </div>
        <div
            className={`my-1 mr-2 flex cursor-pointer items-center rounded-full px-4 py-1.5 text-xs font-medium sm:mr-3 ${byType === "byCity"
                ? "bg-primary-6000 text-white shadow-lg shadow-black/10 dark:bg-primary-500"
                : "border border-neutral-300 dark:border-neutral-700"
            }`}
            onClick={async () => await handleClick("byCity")}
        >
          <BuildingOffice2Icon className="mr-1 h-4 w-4" />
          Per città
        </div>
      </div>
    )
  }

  return (
    <form
      className="relative w-full rounded-[40px] rounded-t-2xl bg-white shadow-xl dark:bg-neutral-800 dark:shadow-2xl xl:rounded-[49px] xl:rounded-t-3xl"
      onSubmit={handleSubmit}
    >
      {renderRadioBtn()}
      <div
        className={`items-left relative flex w-full flex-col md:flex-row md:items-center`}
      >
        <MainSearch />

        <div className="hidden h-8 self-center border-r border-slate-200 dark:border-slate-700 md:block"></div>
        {byType === "byCity" && <LocationInput />}
        <div className="hidden h-8 self-center border-r border-slate-200 dark:border-slate-700 md:block"></div>
        <div className="px-2 pb-3 md:pb-0 xl:px-4">
          <ButtonSubmit
            onPreSubmit={() => {
              setCursor(null)
            }}
            disabled={inputServiceError}
            loading={loading}
          />
        </div>
      </div>
    </form>
  )
}

export default ServiceSearchForm
