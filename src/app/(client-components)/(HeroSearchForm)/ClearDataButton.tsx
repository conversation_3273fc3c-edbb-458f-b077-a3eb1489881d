"use client"

import React, { <PERSON> } from "react"
import { cn } from "@/utils/cn"
import { XMarkIcon } from "@heroicons/react/24/outline"

export interface ClearDataButtonProps
  extends React.HTMLAttributes<HTMLButtonElement> {
  className?: string
}

const ClearDataButton: FC<ClearDataButtonProps> = ({
  onClick,
  children,
  className,
}) => {
  return (
    <button
      onClick={(e) => onClick && onClick(e)}
      className={cn(
        className,
        !children &&
          "absolute right-3 top-1/2 z-10 flex h-5 w-5 -translate-y-1/2 transform items-center justify-center rounded-full bg-neutral-200 text-sm dark:bg-neutral-800 lg:h-6 lg:w-6"
      )}
    >
      {children || <XMarkIcon className="h-3 w-3" />}
    </button>
  )
}

export default ClearDataButton
