import React, { FC, FormEventHandler } from "react"
import { cn } from "@/utils/cn"

interface Props {
  disabled?: boolean
  loading?: boolean
  onPreSubmit?: () => void
}

const ButtonSubmit: FC<Props> = ({ disabled, loading, onPreSubmit }) => {
  const handleClick: FormEventHandler<HTMLButtonElement> = () => {
    if (onPreSubmit) {
      onPreSubmit()
    }
  }

  return (
    <button
      type="submit"
      disabled={disabled && !loading}
      onClick={handleClick}
      className={cn(
        "flex h-14 w-full items-center justify-center gap-x-2 rounded-full bg-primary-6000 text-neutral-50 hover:bg-primary-700 focus:outline-none md:h-16 md:w-16",
        disabled ? "pointer-events-none bg-neutral-200" : ""
      )}
    >
      {!loading ? (
        <>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <span className="md:hidden" aria-label="search">
            Ricerca consulente
          </span>
        </>
      ) : (
        <svg
          className="h-5 w-5 animate-spin text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
          ></path>
        </svg>
      )}
    </button>
  )
}

export default ButtonSubmit
