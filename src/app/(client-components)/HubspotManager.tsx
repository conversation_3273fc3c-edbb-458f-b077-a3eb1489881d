"use client"

import { useEffect, useState } from "react"
import { useIsClient } from "@uidotdev/usehooks"

const useHubSpotChat = () => {
  const isClient = useIsClient()
  const HUBSPOT_CHATFLOW_ID = process.env.NEXT_PUBLIC_HUBSPOT_CHATFLOW_ID

  useEffect(() => {
    if (isClient) {
      const script = document.createElement("script")
      script.src = `//js.hs-scripts.com/${HUBSPOT_CHATFLOW_ID}.js`
      script.defer = true
      script.async = true
      script.type = 'text/javascript'

      document.body.appendChild(script)


      window.hsConversationsSettings = {
        loadImmediately: false,
      }

      window.hsConversationsOnReady = [
        () =>
          window?.HubSpotConversations?.on("widgetClosed", () => {
            window?.HubSpotConversations?.widget?.remove()
          }),
      ]

      return () => {
        document.body.removeChild(script)
      }
    }
  }, [HUBSPOT_CHATFLOW_ID, isClient])
}

export default useHubSpotChat
