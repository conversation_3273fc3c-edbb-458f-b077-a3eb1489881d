"use client"

import { FC, useState, useTransition } from "react"
import { useSearchParams } from "next/navigation"
import { login } from "@/actions/login"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useIsClient } from "@uidotdev/usehooks"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { InferOutput } from "valibot"

import { LoginSchema } from "@/lib/constants"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import Spinner from "@/components/Spinner"
import FormItem from "@/app/(Form)/FormItem"
import { useRouter } from "next/navigation"
import { useQueryClient } from "@tanstack/react-query"

export const LoginForm: FC = () => {
  const searchParams = useSearchParams()
  const callbackUrl = searchParams.get("callbackUrl")
  const router = useRouter()
  const queryClient = useQueryClient()

  const form = useForm<InferOutput<typeof LoginSchema>>({
    resolver: valibotResolver(LoginSchema),
    mode: "onSubmit",
  })

  const [isPending, startTransition] = useTransition()

  const isClient = useIsClient()

  const loginAction = async (values: InferOutput<typeof LoginSchema>) => {
    startTransition(() => {
      try {
        login(values, callbackUrl).then((data) => {
          if (data?.error) {
            form.reset()
            toast.error(data.error)
            if (!data.error.includes('Credenziali non valide')) {
              throw new Error("Error while logging in: " + data.error)
            }
          }
          else {
            // queryClient.invalidateQueries({ queryKey: ["getConsultantByToken"] })
            router.push("/account")
          }
        })
      } catch (err) {
        toast.error("Si è verificato un errore, riprova")
        throw new Error("An error occurred while logging in")
      }
    })
  }

  const fillDemoCredentials = () => {
    form.setValue('email', '<EMAIL>')
    form.setValue('password', 'demopassword123')
  }

  if (!isClient)
    return (
      <div className="flex h-96 items-center justify-center">
        <Spinner />
      </div>
    )

  return (
    <form
      className="grid grid-cols-1 gap-6"
      onSubmit={form.handleSubmit(loginAction)}
    >
      <FormItem
        label="Email"
        visibleDesc={!!form?.formState.errors?.email}
        descClassName="text-red-500"
        desc={
          form.formState.errors.email
            ? form.formState.errors.email.message
            : "Inserisci la tua mail"
        }
      >
        <Input
          type="email"
          autoComplete="email"
          isError={!!form?.formState.errors?.email}
          {...form.register("email")}
          className="mt-1"
          disabled={isPending}
        />
      </FormItem>
      <FormItem
        label="Password"
        visibleDesc={!!form?.formState.errors?.password}
        descClassName="text-red-500"
        desc={
          form.formState.errors
            ? form.formState.errors.password?.message
            : "Inserisci la tua password"
        }
      >
        <Input
          type="password"
          autoComplete="current-password"
          isError={!!form?.formState.errors?.password}
          {...form.register("password")}
          className="mt-1"
          disabled={isPending}
        />
      </FormItem>
      <ButtonPrimary type="submit" disabled={isPending}>
        Accedi
      </ButtonPrimary>

      {/* Demo button for development */}
      {process.env.NODE_ENV === 'development' && (
        <button
          type="button"
          onClick={fillDemoCredentials}
          className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          disabled={isPending}
        >
          Fill Demo Credentials
        </button>
      )}
    </form>
  )
}

export default LoginForm
