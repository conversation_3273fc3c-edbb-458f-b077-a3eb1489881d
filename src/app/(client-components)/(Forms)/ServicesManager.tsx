"use client"

import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react"
import { fetcher } from "@/api/gql/fetcher"
import {
  AssignOffersToConsultantDocument,
  AssignOffersToConsultantMutation,
  AssignOffersToConsultantMutationVariables,
  ConsultingService,
  GetConsultantByTokenDocument,
  GetConsultantByTokenQuery,
  GetConsultantByTokenQueryVariables,
  useGetConsultingServicesQuery,
} from "@/api/gql/generated"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { isEmpty } from "lodash"
import ReCAPTCHA from "react-google-recaptcha"
import { FieldArrayWithId, useFieldArray, useForm } from "react-hook-form"
import { toast } from "sonner"
import * as v from "valibot"

import { useCurrentUser } from "@/hooks/useCurrentUser"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import {
  CleanUiCommandInput,
  CommandCleanUI,
  CommandItemCleanUI,
} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"

export const FormSchema = v.object({
  offers: v.array(
    v.object({
      price: v.nullish(v.string()),
      displayPrice: v.nullish(v.number()),
      name: v.string(),
      urlKey: v.string(),
      serviceId: v.number()
    })
  ),
})

export type GroupedFields = {
  [category: string]: FieldArrayWithId<FormSchemaType, "offers", "id">[]
}
export type FormSchemaType = v.InferOutput<typeof FormSchema>

const ServicesManager: FC = () => {
  const { control, handleSubmit, register, getValues } =
    useForm<FormSchemaType>({
      resolver: valibotResolver(FormSchema),
      defaultValues: { offers: [] },
    })

  const queryClient = useQueryClient()

  const { fields, append, remove } = useFieldArray({
    control,
    name: "offers",
  })

  const [showPopover, setShowPopover] = useState<boolean>(false)
  const [value, setValue] = useState<string>("")
  const [, setLoading] = useState<boolean>(false)

  const currentUser = useCurrentUser()

  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleCleanup = useCallback(() => {
    setValue("")
    inputRef.current?.focus()
  }, [setValue])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowPopover(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  const { data, isLoading } = useGetConsultingServicesQuery(
    {},
    {
      select: (data) =>
        data?.consultingServices?.collection ?? [],
    }
  )

  const { data: prefilledData } = useQuery({
    queryKey: ["getConsultantByToken"],
    queryFn: fetcher<
      GetConsultantByTokenQuery,
      GetConsultantByTokenQueryVariables
    >(
      GetConsultantByTokenDocument,
      {},
      {
        Authorization: `Bearer ${currentUser?.token}`,
      }
    ),
  })

  useEffect(() => {
    if (prefilledData?.byTokenConsultant?.offers?.collection) {
      const offers = prefilledData.byTokenConsultant.offers.collection
      offers.forEach((offer) => {
        if (offer && offer.consultingService) {
          const { consultingService } = offer
          const { name, urlKey, price, _id } =
            consultingService
          const offerExists = getValues().offers.some(
            (offer) => offer.urlKey === urlKey
          )
          if (!offerExists)
            append({
              urlKey: urlKey ?? "",
              price: offer.price?.toString() ?? undefined,
              displayPrice: price ? price / 100 : undefined,
              name: name ?? "",
              serviceId: _id
            })
        }
      })
    }
  }, [prefilledData, append, getValues])

  useEffect(() => {
    setLoading(isLoading)
  }, [isLoading, setLoading])

  const handleValueChange = (newValue: string) => {
    setValue(newValue)
    if (!showPopover) {
      setShowPopover(true)
    }
  }

  const handleSelect = useCallback(
    (selectedService: ConsultingService) => {
      const offerExists = getValues().offers.some((offer) => {
        return offer.urlKey === selectedService.urlKey
      })
      if (
        !offerExists &&
        selectedService.id &&
        selectedService.name &&
        selectedService.price
      ) {
        append(
          {
            price: null,
            displayPrice: selectedService.price / 100,
            name: selectedService.name,
            urlKey: selectedService.urlKey ?? "",
            serviceId: selectedService._id
          },
          {}
        )
      }
      setShowPopover(false)
    },
    [append, getValues, setShowPopover]
  )

  const handleRemoveService = useCallback(
    (name: string) => {
      const index = fields.findIndex((field) => field.name === name)
      remove(index)
    },
    [fields, remove]
  )

  const captchaRef = useRef<ReCAPTCHA>(null)

  const { mutate } = useMutation<
    AssignOffersToConsultantMutation,
    unknown,
    AssignOffersToConsultantMutationVariables,
    unknown
  >({
    mutationKey: ["assignTagsToConsultant"],
    mutationFn: (variables?: AssignOffersToConsultantMutationVariables) =>
      fetcher<
        AssignOffersToConsultantMutation,
        AssignOffersToConsultantMutationVariables
      >(AssignOffersToConsultantDocument, variables, {
        Authorization: `Bearer ${currentUser?.token}`,
      })(),
    onSuccess: () => {
      toast.success("Servizi aggiornati")
      queryClient.invalidateQueries({ queryKey: ["getConsultantByToken"] })
    },
    onError: () => {
      toast.error("Errore nell'aggiornamento dei servizi")
    },
  })

  const onSubmit = async (data: FormSchemaType) => {
    const captcha = await captchaRef.current?.executeAsync()
    const res = await fetch("/subscribe", {
      method: "POST",
      body: JSON.stringify({ captcha }),
      headers: { "Content-type": "application/json" },
    })

    if (res.status === 200) {
      mutate({
        input: {
          offers: data.offers.map((offer) => {
            if (isEmpty(offer.price)) {
              return {
                serviceId: offer.serviceId,
              }
            } else {
              return {
                serviceId: offer.serviceId,
                price: Number(offer.price),
              }
            }
          })
        },
      })
    } else {
      toast.error("Errore nell'invio della richiesta")
    }
  }

  return (
    <div className="w-full space-y-6">
      <h3 className="block text-xl font-semibold">Servizi di consulenza</h3>
      <span className="mt-2 block text-base font-normal text-neutral-500 [text-wrap:balance] dark:text-neutral-400 sm:text-lg md:mt-3">
        Seleziona dalla ricerca il servizio che vuoi aggiungere al tuo listino
        di servizi, puoi pubblicare i tuoi servizi con il prezzo consigliato di
        Consulente Ideale oppure definire il prezzo che preferisci.
      </span>
      <div className="relative flex flex-1 flex-col " ref={containerRef}>
        <CommandCleanUI shouldFilter={false}>
          <div
            onClick={() => setShowPopover(true)}
            className={`relative z-10 flex flex-1 flex-shrink-0 cursor-pointer items-center space-x-3 text-left focus:outline-none`}
          >
            <div className="flex-grow">
              <CleanUiCommandInput
                placeholder="Ricerca servizio"
                value={value}
                onValueChange={handleValueChange}
                ref={inputRef}
                className="capitalize"
                onFocus={() => setShowPopover(true)}
                onKeyDown={(e) => {
                  if (e.key === "Escape") {
                    handleCleanup()
                  } else if (e.key === "Tab") {
                    setShowPopover(false)
                  }
                }}
              />
              {value && (
                <ClearDataButton
                  onClick={(e) => {
                    e.stopPropagation()
                    handleCleanup()
                  }}
                />
              )}
            </div>
          </div>
          {showPopover && (
            <div className="absolute left-0 top-full z-40 mt-5 w-full min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-4 ring-primary-300 ring-offset-2 transition-all dark:bg-neutral-800 sm:py-6">
              <div className="max-h-48 overflow-y-auto md:max-h-96">
                {!isLoading && isEmpty(data) ? (
                  <div className="flex items-center justify-start gap-x-2 px-5 text-base">
                    <span className="text-base">Nessun servizio trovato.</span>
                    <ClearDataButton
                      className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleCleanup()
                      }}
                    >
                      Cancella Filtri
                    </ClearDataButton>
                  </div>
                ) : null}
                {data?.filter((item) => item?.name?.toLowerCase().includes(value.toLowerCase()))?.map((service) => {
                  if (service) {
                    const offerExists = getValues().offers.some((offer) => {
                      return offer.urlKey === service.urlKey
                    })
                    if (!offerExists) {
                      return (
                        <CommandItemCleanUI
                          key={service._id}
                          value={service._id.toString() ?? ""}
                          onSelect={() => handleSelect(service)}
                        >
                          {service.name}
                        </CommandItemCleanUI>
                      )
                    }
                  }
                }
                )}
              </div>
            </div>
          )}
        </CommandCleanUI>
      </div>
      <div className=" border-b border-neutral-200 dark:border-neutral-700"></div>
      <span className="block text-lg font-semibold">Servizi aggiunti</span>
      <div className="flow-root">
        <div className="-my-3 divide-y divide-neutral-100 dark:divide-neutral-800">
          <form onSubmit={handleSubmit(onSubmit)}>
            {isEmpty(fields) && (
              <div className="flex max-w-lg flex-col">
                <span className="text-base font-medium text-neutral-6000 [text-wrap:balance] dark:text-neutral-400">
                  Nessun servizio aggiunto.
                </span>
              </div>
            )}
            {fields.map((field, index) => (
              <div
                key={field.name}
                className="flex items-center justify-between gap-x-3 py-3"
              >
                <div className="flex flex-col gap-y-2">
                  <span className="font-medium text-neutral-6000 dark:text-neutral-400">
                    {field.name}
                  </span>
                </div>
                <div className="flex items-center gap-x-2">
                  <div className="relative flex items-center gap-x-2 text-neutral-6000 dark:text-neutral-300">
                    {/* prvent input from getting focused when it gets first rendered */}

                    <Input
                      type="number"
                      step={0.01}
                      max={999999}
                      min={1}
                      {...register(`offers.${index}.price`)}
                      placeholder={field.displayPrice?.toString() ?? "Prezzo"}
                      className="[appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                    />
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-500">€</span>
                    </div>
                  </div>
                  <i
                    onClick={() => handleRemoveService(field.name)}
                    className="las la-times-circle cursor-pointer text-2xl text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100"
                  ></i>
                </div>
              </div>
            ))}
            <ReCAPTCHA
              ref={captchaRef}
              size="invisible"
              sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
            />
            <div className="pt-8">
              <ButtonPrimary>Salva modifiche</ButtonPrimary>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default ServicesManager
