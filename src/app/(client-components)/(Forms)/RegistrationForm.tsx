"use client"

import { FC, useEffect, useRef, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useCreateConsultantMutation } from "@/api/gql/generated"
import { getRegistrationErrorMessage } from "@/utils/errors"
import { Switch } from "@headlessui/react"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useMaskito } from "@maskito/react"
import { sendGTMEvent } from "@next/third-parties/google"
import { DateTime } from "luxon"
import Captcha from "react-google-recaptcha"
import { Controller, SubmitHandler, useForm } from "react-hook-form"
import { toast } from "sonner"
import { InferOutput } from "valibot"

import { maskitoOptions } from "@/lib/constants"
import { cn } from "@/lib/utils"
import useConvertImageToWebP from "@/hooks/useConvertImagetoWebp"
import useDynamicFormSchema from "@/hooks/useDynamicFormSchema"
import useCityIdByName from "@/hooks/useGetCityIdByName"
import Avatar from "@/shared/Avatar"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import LocationSelect from "@/components/LocationSelect"
import RecaptchaDisclaimer from "@/components/RecaptchaDisclaimer"
import FormItem from "@/app/(Form)/FormItem"
import Checkbox from "@/shared/Checkbox"
import Link from "next/link"

const RegistrationForm: FC = () => {
  const router = useRouter()
  const [previewSrc, setPreviewSrc] = useState("")
  const dynamicFormSchema = useDynamicFormSchema()
  const { convertToWebP, isLoading } = useConvertImageToWebP()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const form = useForm<InferOutput<typeof dynamicFormSchema>>({
    resolver: valibotResolver(dynamicFormSchema),
    mode: "onSubmit",
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      city: "",
      dateOfBirth: "",
      remote: false,
      image: null,
      telephone: "",
      acceptedConditions: false
    },
  })

  const { cityId, isLoading: isCityIdLoading } = useCityIdByName(
    form.watch("city")
  )

  const maskedDateRef = useMaskito({ options: maskitoOptions })

  const validateFile = (file: File) => {
    const validTypes = ["image/jpeg", "image/png", "image/webp"]
    if (!validTypes.includes(file.type)) {
      toast.error("Formato non valido.")
      return false
    }
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File troppo grande. Max 5MB.")
      return false
    }
    return true
  }

  const updateFormFile = async (file?: File) => {
    if (file && validateFile(file)) {
      try {
        const webPBlob = await convertToWebP({ imageFile: file })

        if (webPBlob.size > 3 * 1024 * 1024) {
          toast.error("Immagine troppo grande", {
            description: "Prova con un file più piccolo.",
          })
          return
        }

        const webPFile = new File([webPBlob], "converted-image.webp", {
          type: "image/webp",
        })

        const reader = new FileReader()
        reader.readAsDataURL(webPFile)
        const loadFile = new Promise((resolve) => {
          reader.onloadend = () => {
            if (reader.result && typeof reader.result === "string") {
              form.setValue("image", reader.result)
            }
            resolve(reader.result)
          }
        })
        toast.promise(loadFile, {
          loading: "Caricamento immagine",
          success: "Immagine caricata con successo",
          error: "Errore durante il caricamento",
        })
        setPreviewSrc(URL.createObjectURL(webPFile))
      } catch (error) {
        console.error("Error converting file:", error)
        toast.error("Immagine non valida. Prova con un altro file.")
      }
    }
  }

  const removeFile = () => {
    form.resetField("image")
    setPreviewSrc("")
  }

  const captchaRef = useRef<Captcha>(null)

  const { mutate } = useCreateConsultantMutation({
    onError: (error) => {
      if (error instanceof Error)
        toast.error(getRegistrationErrorMessage(error as Error))
    },
    onSuccess: async () => {
      sendGTMEvent({
        event: "sign_up",
      })
      if (typeof window !== "undefined" && window.fbq) {
        try {
          window.fbq("track", "CompleteRegistration")
        } catch (e) {
          console.error(e)
        }
      }
      setTimeout(() => {
        router.push("/auth/registrazione/successo")
      }, 3000)
    },
  })

  const onSubmit: SubmitHandler<InferOutput<typeof dynamicFormSchema>> = async (
    data
  ) => {
    if (isCityIdLoading || !cityId) return
    const captcha = await captchaRef.current?.executeAsync()

    const res = await fetch("/subscribe", {
      method: "POST",
      body: JSON.stringify({ captcha }),
      headers: { "Content-type": "application/json" },
    })

    if (res.status === 200) {
      mutate({
        input: {
          fullName: data.fullName,
          email: data.email,
          password: data.password,
          avatar: data.image,
          dateOfBirth: `${DateTime.fromFormat(data.dateOfBirth, "dd/MM/yyyy", {
            zone: "utc",
          }).toISODate()}T00:00:00+00:00`,
          isRemote: data.remote,
          city: cityId,
          telephone: data.telephone,
        },
      })
    } else {
      toast.error("Errore di CAPTCHA.")
    }
  }

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()

    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      const file = event.dataTransfer.files[0]
      await updateFormFile(file)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
  }

  useEffect(() => {
    return () => {
      if (previewSrc) {
        URL.revokeObjectURL(previewSrc)
      }
    }
  }, [previewSrc])

  const loading = isCityIdLoading || isLoading

  return (
    <>
      <h2 className="text-2xl font-semibold">Inserisci i tuoi dati</h2>
      <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      {/* FORM */}
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
        {/* ITEM */}
        <FormItem
          label="Nome completo*"
          visibleDesc={!!form.formState.errors.fullName}
          descClassName="text-red-500"
          desc={
            form.formState.errors.fullName
              ? form.formState.errors.fullName.message
              : "Inserisci il tuo nome"
          }
        >
          <Input
            type="text"
            placeholder="Mario Rossi"
            autoComplete="name"
            {...form.register("fullName")}
            isError={!!form?.formState?.errors?.fullName?.message}
          />
        </FormItem>
        <div className="mb-4 flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <FormItem
            className="w-full"
            label="Email*"
            visibleDesc={!!form.formState.errors.email}
            descClassName="text-red-500"
            desc={
              form.formState.errors.email
                ? form.formState.errors.email.message
                : "Inserisci la tua email"
            }
          >
            <Input
              placeholder="Email"
              autoComplete="email"
              {...form.register("email")}
              isError={!!form?.formState?.errors?.email?.message}
            />
          </FormItem>
          <FormItem
            className="w-full"
            label="Password*"
            visibleDesc={!!form.formState.errors.password}
            descClassName="text-red-500"
            desc={
              form.formState.errors.password
                ? form.formState.errors.password.message
                : "Inserisci la tua password"
            }
          >
            <Input
              type="password"
              autoComplete="new-password"
              placeholder="Password"
              {...form.register("password")}
              isError={!!form?.formState?.errors?.password?.message}
            />
          </FormItem>
        </div>
        <FormItem
          label="Città*"
          visibleDesc={!!form.formState.errors.city}
          descClassName="text-red-500"
          desc={
            form.formState.errors.city
              ? form.formState.errors.city.message
              : "Inserisci la tua città"
          }
        >
          <Controller
            control={form.control}
            name="city"
            render={({ field: { value, onBlur, onChange } }) => {
              return (
                <LocationSelect
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  isError={!!form.formState.errors.city}
                />
              )
            }}
          />
        </FormItem>
        <div className="mb-4 flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <FormItem
            className="w-full"
            label="Data di nascita*"
            visibleDesc={!!form.formState.errors.dateOfBirth}
            descClassName="text-red-500"
            desc={
              form.formState.errors.dateOfBirth
                ? form.formState.errors.dateOfBirth.message
                : "Inserisci la tua data di nascita"
            }
          >
            <Controller
              control={form.control}
              name="dateOfBirth"
              render={({ field }) => (
                <Input
                  {...field}
                  onInput={(e) => {
                    maskedDateRef(e.currentTarget)
                    form.setValue("dateOfBirth", e.currentTarget.value)
                  }}
                  ref={(e) => {
                    field.ref(e)
                  }}
                  placeholder="Data di nascita"
                  isError={!!form?.formState?.errors?.dateOfBirth?.message}
                />
              )}
            />
          </FormItem>
          <FormItem
            className="w-full"
            label="Telefono*"
            visibleDesc={!!form.formState.errors.telephone}
            descClassName="text-red-500"
            desc={
              form.formState.errors.telephone
                ? form.formState.errors.telephone.message
                : "Inserisci il tuo numero di telefono"
            }
          >
            <Input
              placeholder="Telefono"
              {...form.register("telephone")}
              isError={!!form?.formState?.errors?.telephone?.message}
            />
          </FormItem>
        </div>
        <FormItem label="Servizi aggiuntivi">
          <div className="flex flex-row items-center justify-between gap-2 space-y-2 rounded-lg border p-4">
            <div className="flex w-full flex-col space-y-2">
              <p className="text-sm text-neutral-900 dark:text-neutral-100">
                Lavoro da remoto
              </p>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                Offri servizi di consulenza anche da remoto?
              </p>
            </div>
            <Switch
              checked={form.watch("remote")}
              onChange={(value) => form.setValue("remote", value)}
              className={`${form.watch("remote") ? "bg-primary-500" : "bg-neutral-200"
                } relative inline-flex h-[22px] w-[42px] shrink-0 cursor-pointer rounded-full border-4 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2  focus-visible:ring-white focus-visible:ring-opacity-75`}
            >
              <span
                aria-hidden="true"
                className={`${form.watch("remote") ? "translate-x-5" : "translate-x-0"
                  } pointer-events-none inline-block h-[14px] w-[14px] transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out`}
              />
            </Switch>
          </div>
        </FormItem>
        <FormItem
          label="Carica una foto profilo"
          visibleDesc={!!form.formState.errors.image}
          descClassName="text-red-500"
          desc={
            form.formState.errors.image
              ? form.formState.errors.image.message
              : "Upload your file"
          }
        >
          <Controller
            name="image"
            control={form.control}
            render={({ field: { onChange } }) => (
              <div
                className={cn(
                  "relative flex flex-shrink-0 cursor-pointer items-start hover:opacity-80"
                )}
                onClick={() => fileInputRef.current?.click()}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <div className="mt-1 flex w-full cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
                  {previewSrc && (
                    <div className="flex items-center justify-center">
                      <Avatar
                        sizeClass="w-14 h-14"
                        customUrl={previewSrc}
                        hasClose
                        handleClose={removeFile}
                      />
                    </div>
                  )}
                  <div
                    className={cn(
                      "space-y-1 text-center",
                      previewSrc && "hidden"
                    )}
                  >
                    <svg
                      className="mx-auto h-12 w-12 text-neutral-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                    </svg>
                    <div className="flex text-sm text-neutral-6000 dark:text-neutral-300">
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={async (e) => {
                          onChange(e)
                          await updateFormFile(e?.target?.files?.[0])
                        }}
                        className="hidden"
                      />
                      <p>
                        {isLoading
                          ? "Caricamento..."
                          : "Clicca qui per selezionare un file."}
                      </p>
                    </div>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      PNG, JPG, WEBP fino a 3MB
                    </p>
                  </div>
                </div>
              </div>
            )}
          />
        </FormItem>
        <FormItem
          label="Termini e condizioni *"
          descClassName="text-red-500"
          desc={
            form.formState.errors.acceptedConditions
              ? form.formState.errors.acceptedConditions.message
              : "Accetta le condizioni"
          }
          visibleDesc={
            !!form.formState.errors.acceptedConditions
          }
        >
          <Checkbox
            {...form.register("acceptedConditions")}
            label="Accetta i termini e condizioni"
            LinkToAgreements={
              <Link
                target="_blank"
                passHref
                href={"/termini-e-condizioni"}
              >
                <span className="text-primary-500 dark:text-primary-400">
                  Termini e condizioni
                </span>
              </Link>
            }
          />
        </FormItem>
        <Captcha
          ref={captchaRef}
          size="invisible"
          sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
        />
        <div>
          <ButtonPrimary disabled={loading} type="submit">
            Registrati
          </ButtonPrimary>
        </div>
      </form>
      <RecaptchaDisclaimer />
    </>
  )
}

export default RegistrationForm
