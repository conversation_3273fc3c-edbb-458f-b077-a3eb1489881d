"use client"

import { useEffect, useRef } from "react"
import { fetcher } from "@/api/gql/fetcher"
import {
  AssignTagsToConsultantDocument,
  AssignTagsToConsultantMutation,
  AssignTagsToConsultantMutationVariables,
  GetConsultantByTokenDocument,
  GetConsultantByTokenQuery,
  GetConsultantByTokenQueryVariables,
  useGetTagsQuery,
} from "@/api/gql/generated"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import ReCAPTCHA from "react-google-recaptcha"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import * as v from "valibot"

import { useCurrentUser } from "@/hooks/useCurrentUser"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Checkbox from "@/shared/Checkbox"
import FormItem from "@/app/(Form)/FormItem"

export const FormSchema = v.object({
  checkboxes: v.record(v.string(), v.boolean()),
})

export type FormSchemaType = v.InferOutput<typeof FormSchema>

const ListedTags = () => {
  const form = useForm<FormSchemaType>({
    resolver: valibotResolver(FormSchema),
    mode: "onSubmit",
  })
  const currentUser = useCurrentUser()

  const queryClient = useQueryClient()

  const { data: tagsQueryData, isLoading, isError } = useGetTagsQuery()

  const {
    data: prefilledData,
    isLoading: prefilledLoading,
    isError: PrefilledLoading,
  } = useQuery({
    queryKey: ["getConsultantByToken"],
    queryFn: fetcher<
      GetConsultantByTokenQuery,
      GetConsultantByTokenQueryVariables
    >(
      GetConsultantByTokenDocument,
      {},
      {
        Authorization: `Bearer ${currentUser?.token}`,
      }
    ),
  })

  useEffect(() => {
    prefilledData?.byTokenConsultant?.tags?.collection?.forEach((tag) => {
      form.setValue(`checkboxes.${tag?._id}`, true)
    })
  }, [prefilledData, form, tagsQueryData?.tags?.collection])

  const {
    mutate,
    isSuccess: submitSuccess,
    isError: submitError,
  } = useMutation<
    AssignTagsToConsultantMutation,
    unknown,
    AssignTagsToConsultantMutationVariables,
    unknown
  >({
    mutationKey: ["assignTagsToConsultant"],
    mutationFn: (variables?: AssignTagsToConsultantMutationVariables) =>
      fetcher<
        AssignTagsToConsultantMutation,
        AssignTagsToConsultantMutationVariables
      >(AssignTagsToConsultantDocument, variables, {
        Authorization: `Bearer ${currentUser?.token}`,
      })(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["getConsultantByToken"],
      })
    },
  })

  const onSubmit = async (data: FormSchemaType) => {
    const captcha = await captchaRef.current?.executeAsync()
    const res = await fetch("/subscribe", {
      method: "POST",
      body: JSON.stringify({ captcha }),
      headers: { "Content-type": "application/json" },
    })

    if (res.status === 200) {
      mutate({
        input: {
          tagIds: Object.keys(data.checkboxes)
            .filter((key) => data.checkboxes[key])
            .map(Number),
        },
      })
    } else {
      toast.error("Errore nell'invio della richiesta")
    }
  }

  const captchaRef = useRef<ReCAPTCHA>(null)

  useEffect(() => {
    if (submitSuccess) {
      toast.success("Servizi aggiuntivi aggiornati con successo")
    }
  }, [form, submitSuccess])

  useEffect(() => {
    if (submitError) {
      toast.error("Errore nell'aggiornamento dei servizi aggiuntivi")
    }
  }, [form, submitError])

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <h3 className="block text-xl font-semibold">
        Configura le tue politiche
      </h3>
      <div className="mt-6 grid w-full grid-cols-1 gap-5 sm:grid-cols-3 lg:grid-cols-5">
        {!isLoading &&
          !prefilledLoading &&
          tagsQueryData?.tags?.collection?.map((tag) => (
            <FormItem
              key={tag?._id}
              visibleDesc={
                !!form?.formState.errors?.checkboxes?.[tag?._id || 0]
              }
              descClassName="text-red-500"
              desc={
                form.formState.errors
                  ? form.formState.errors.checkboxes?.[tag?._id || 0]?.message
                  : "Seleziona almeno un tag"
              }
            >
              <Checkbox
                {...form.register(`checkboxes.${tag?._id}`)}
                defaultChecked={
                  prefilledData?.byTokenConsultant?.tags?.collection?.find(
                    (checkbox) => checkbox?._id === tag?._id
                  )
                    ? true
                    : false
                }
                label={tag?.name}
              />
            </FormItem>
          ))}
      </div>
      <ReCAPTCHA
        ref={captchaRef}
        size="invisible"
        sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
      />
      <div className="pt-2">
        <ButtonPrimary>Salva le politiche</ButtonPrimary>
      </div>
    </form>
  )
}

export default ListedTags
