"use client"

import { FC, useCallback, useEffect, useRef, useState } from "react"
import { fetcher } from "@/api/gql/fetcher"
import {
    ConsultantProfession,
    GetConsultantByTokenDocument,
    GetConsultantByTokenQuery,
    GetConsultantByTokenQueryVariables,
    UpdateConsultantDocument,
    UpdateConsultantMutation,
    UpdateConsultantMutationVariables,
    useGetConsultantProfessionsAllQuery,
} from "@/api/gql/generated"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { isEmpty } from "lodash"
import ReCAPTCHA from "react-google-recaptcha"
import { useFieldArray, useForm } from "react-hook-form"
import { toast } from "sonner"
import * as v from "valibot"

import { useCurrentUser } from "@/hooks/useCurrentUser"
import ButtonPrimary from "@/shared/ButtonPrimary"
import {
    CleanUiCommandInput,
    CommandCleanUI,
    CommandItemCleanUI,
} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"

export const FormSchema = v.object({
    professions: v.array(
        v.object({
            urlKey: v.string(),
            id: v.string(),
            name: v.string(),
        })
    ),
})

export type FormSchemaType = v.InferOutput<typeof FormSchema>


const ProfessionsManager: FC = () => {
    const { control, handleSubmit, getValues } =
        useForm<FormSchemaType>({
            resolver: valibotResolver(FormSchema),
            defaultValues: { professions: [] },
        })

    const queryClient = useQueryClient()

    const { fields, append, remove } = useFieldArray({
        control,
        name: "professions",
    })

    const [showPopover, setShowPopover] = useState<boolean>(false)
    const [value, setValue] = useState<string>("")
    const [, setLoading] = useState<boolean>(false)

    const currentUser = useCurrentUser()

    const containerRef = useRef<HTMLDivElement>(null)
    const inputRef = useRef<HTMLInputElement>(null)

    const handleCleanup = useCallback(() => {
        setValue("")
        inputRef.current?.focus()
    }, [setValue])

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                containerRef.current &&
                !containerRef.current.contains(event.target as Node)
            ) {
                setShowPopover(false)
            }
        }

        document.addEventListener("mousedown", handleClickOutside)
        return () => {
            document.removeEventListener("mousedown", handleClickOutside)
        }
    }, [])

    useEffect(() => {
        if (showPopover && inputRef.current) {
            inputRef.current.focus()
        }
    }, [showPopover])

    const { data, isLoading } = useGetConsultantProfessionsAllQuery(
        {},
        {
            select: (data) =>
                data?.consultantProfessions?.collection ?? [],
        }
    )

    const { data: prefilledData } = useQuery({
        queryKey: ["getConsultantByToken"],
        queryFn: fetcher<
            GetConsultantByTokenQuery,
            GetConsultantByTokenQueryVariables
        >(
            GetConsultantByTokenDocument,
            {},
            {
                Authorization: `Bearer ${currentUser?.token}`,
            }
        ),
    })

    useEffect(() => {
        if (prefilledData?.byTokenConsultant?.professions?.collection) {
            const professions = prefilledData.byTokenConsultant.professions?.collection
            professions.forEach((profession) => {
                if (profession && profession.urlKey) {

                    const professionExists = getValues().professions.some(
                        (prof) => prof.urlKey === profession.urlKey
                    )
                    if (!professionExists)
                        append({
                            urlKey: profession.urlKey,
                            name: profession.name ?? "",
                            id: profession.id
                        })
                }
            })
        }
    }, [prefilledData, append, getValues])

    useEffect(() => {
        setLoading(isLoading)
    }, [isLoading, setLoading])

    const handleValueChange = (newValue: string) => {
        setValue(newValue)
        if (!showPopover) {
            setShowPopover(true)
        }
    }

    const handleSelect = useCallback(
        (selectedProfession: ConsultantProfession) => {
            const professionExists = getValues().professions.some((profession) => {
                return profession.urlKey === selectedProfession.urlKey
            })
            if (
                !professionExists &&
                selectedProfession.name &&
                selectedProfession.urlKey
            ) {
                append(
                    {
                        id: selectedProfession.id,
                        urlKey: selectedProfession.urlKey,
                        name: selectedProfession.name,
                    },
                    {}
                )
            }
            setShowPopover(false)
        },
        [append, getValues, setShowPopover]
    )

    const handleRemoveProfession = useCallback(
        (name: string) => {
            const index = fields.findIndex((field) => field.name === name)
            remove(index)
        },
        [fields, remove]
    )

    const captchaRef = useRef<ReCAPTCHA>(null)

    const { mutate } = useMutation<
        UpdateConsultantMutation,
        unknown,
        UpdateConsultantMutationVariables,
        unknown
    >({
        mutationKey: ["updateConsultant"],
        mutationFn: (variables?: UpdateConsultantMutationVariables) =>
            fetcher<
                UpdateConsultantMutation,
                UpdateConsultantMutationVariables
            >(UpdateConsultantDocument, variables, {
                Authorization: `Bearer ${currentUser?.token}`,
            })(),
        onSuccess: () => {
            toast.success("Professions aggiornati")
            queryClient.invalidateQueries({ queryKey: ["getConsultantByToken"] })
        },
        onError: () => {
            toast.error("Errore nell'aggiornamento dei servizi")
        },
    })

    const onSubmit = async (data: FormSchemaType) => {
        const captcha = await captchaRef.current?.executeAsync()
        const res = await fetch("/subscribe", {
            method: "POST",
            body: JSON.stringify({ captcha }),
            headers: { "Content-type": "application/json" },
        })

        if (res.status === 200) {
            mutate({
                input: {
                    id: prefilledData?.byTokenConsultant?.id ?? "",
                    professions: data.professions.map((profession) => profession.id),
                },
            })
        } else {
            toast.error("Errore nell'invio della richiesta")
        }
    }

    return (
        <div className="w-full space-y-6">
            <h3 className="block text-xl font-semibold">Definisci la Tua Categoria Professionale</h3>
            <span className="mt-2 block text-base font-normal text-neutral-500 [text-wrap:balance] dark:text-neutral-400 sm:text-lg md:mt-3">
                Seleziona il tuo settore per facilitare la ricerca dei clienti e la richiesta di preventivi.
            </span>
            <div className="relative flex flex-1 flex-col " ref={containerRef}>
                <CommandCleanUI shouldFilter={false}>
                    <div
                        onClick={() => setShowPopover(true)}
                        className={`relative z-10 flex flex-1 flex-shrink-0 cursor-pointer items-center space-x-3 text-left focus:outline-none`}
                    >
                        <div className="flex-grow">
                            <CleanUiCommandInput
                                placeholder="Categoria Professionale"
                                value={value}
                                onValueChange={handleValueChange}
                                ref={inputRef}
                                className="capitalize"
                                onFocus={() => setShowPopover(true)}
                                onKeyDown={(e) => {
                                    if (e.key === "Escape") {
                                        handleCleanup()
                                    } else if (e.key === "Tab") {
                                        setShowPopover(false)
                                    }
                                }}
                            />
                            {value && (
                                <ClearDataButton
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        handleCleanup()
                                    }}
                                />
                            )}
                        </div>
                    </div>
                    {showPopover && (
                        <div className="absolute left-0 top-full z-40 mt-5 w-full min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-4 ring-primary-300 ring-offset-2 transition-all dark:bg-neutral-800 sm:py-6">
                            <div className="max-h-48 overflow-y-auto md:max-h-96">
                                {!isLoading && isEmpty(data) ? (
                                    <div className="flex items-center justify-start gap-x-2 px-5 text-base">
                                        <span className="text-base">Nessun servizio trovato.</span>
                                        <ClearDataButton
                                            className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                handleCleanup()
                                            }}
                                        >
                                            Cancella Filtri
                                        </ClearDataButton>
                                    </div>
                                ) : null}

                                {data?.filter((profession) => profession?.name?.toLowerCase().includes(value.toLowerCase())).map((profession) => {
                                    if (profession) {
                                        const professionExists = getValues().professions.some((prof) => {
                                            return prof.urlKey === profession.urlKey
                                        })
                                        if (!professionExists) {
                                            return (
                                                <CommandItemCleanUI
                                                    key={profession?.urlKey}
                                                    value={profession.name ?? ""}
                                                    onSelect={() => handleSelect(profession)}
                                                >
                                                    {profession.name}
                                                </CommandItemCleanUI>
                                            )
                                        }
                                    }
                                }
                                )}

                            </div>
                        </div>
                    )}
                </CommandCleanUI>
            </div>
            <div className=" border-b border-neutral-200 dark:border-neutral-700"></div>
            <span className="block text-lg font-semibold">Categoria Professionale</span>
            <div className="flow-root">
                <div className="-my-3 divide-y divide-neutral-100 dark:divide-neutral-800">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        {isEmpty(fields) && (
                            <div className="flex max-w-lg flex-col">
                                <span className="text-base font-medium text-neutral-6000 [text-wrap:balance] dark:text-neutral-400">
                                    Nessun servizio aggiunto.
                                </span>
                            </div>
                        )}
                        {fields.map((field) => (
                            <div
                                key={field.name}
                                className="flex items-center justify-between gap-x-3 py-3"
                            >
                                <div className="flex flex-col gap-y-2">
                                    <span className="font-medium text-neutral-6000 dark:text-neutral-400">
                                        {field.name}
                                    </span>
                                </div>
                                <div className="flex items-center gap-x-2">
                                    <i
                                        onClick={() => handleRemoveProfession(field.name)}
                                        className="las la-times-circle cursor-pointer text-2xl text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100"
                                    ></i>
                                </div>
                            </div>
                        ))}
                        <ReCAPTCHA
                            ref={captchaRef}
                            size="invisible"
                            sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
                        />
                        <div className="pt-8">
                            <ButtonPrimary>Salva modifiche</ButtonPrimary>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    )
}

export default ProfessionsManager
