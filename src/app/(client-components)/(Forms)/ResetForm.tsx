"use client"

import { FC, useTransition } from "react"
import { reset } from "@/actions/reset"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useIsClient } from "@uidotdev/usehooks"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { InferOutput } from "valibot"

import { ResetPasswordSchema } from "@/lib/constants"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import Spinner from "@/components/Spinner"
import FormItem from "@/app/(Form)/FormItem"

export const ResetForm: FC = () => {
  const form = useForm<InferOutput<typeof ResetPasswordSchema>>({
    resolver: valibotResolver(ResetPasswordSchema),
    mode: "onSubmit",
  })

  const [isPending, startTransition] = useTransition()

  const isClient = useIsClient()

  const resetAction = async (values: InferOutput<typeof ResetPasswordSchema>) => {
    startTransition(() => {
      try {
        reset(values).then((data) => {
          if (data?.error) {
            form.reset()
            toast.error(data.error)
          }
          if (data?.success) {
            form.reset()
            toast.success(data.success)
          }
        })
      } catch (err) {
        toast.error("Si è verificato un errore, riprova")
      }
    })
  }

  if (!isClient)
    return (
      <div className="flex h-96 items-center justify-center">
        <Spinner />
      </div>
    )

  return (
    <form
      className="grid grid-cols-1 gap-6"
      onSubmit={form.handleSubmit(resetAction)}
    >
      <FormItem
        label="Email"
        visibleDesc={!!form?.formState.errors?.email}
        descClassName="text-red-500"
        desc={
          form.formState.errors.email
            ? form.formState.errors.email.message
            : "Inserisci la tua mail"
        }
      >
        <Input
          disabled={isPending}
          type="email"
          isError={!!form?.formState.errors?.email}
          {...form.register("email")}
          className="mt-1"
        />
      </FormItem>
      <ButtonPrimary type="submit" disabled={isPending}>
        Ricevi link
      </ButtonPrimary>
    </form>
  )
}

export default ResetForm
