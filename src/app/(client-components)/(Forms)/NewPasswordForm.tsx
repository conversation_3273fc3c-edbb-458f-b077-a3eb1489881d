"use client"

import { FC, useTransition } from "react"
import { redirect, useRouter, useSearchParams } from "next/navigation"
import { newPassword } from "@/actions/new-password"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { useIsClient } from "@uidotdev/usehooks"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { InferOutput } from "valibot"

import { NewPasswordSchema } from "@/lib/constants"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import Spinner from "@/components/Spinner"
import FormItem from "@/app/(Form)/FormItem"

export const NewPasswordForm: FC = () => {
  const searchParams = useSearchParams()
  const token = searchParams.get("token")

  const form = useForm<InferOutput<typeof NewPasswordSchema>>({
    resolver: valibotResolver(NewPasswordSchema),
    mode: "onSubmit",
  })

  const [isPending, startTransition] = useTransition()

  const isClient = useIsClient()

  const { push } = useRouter()

  const newPasswordAction = async (
    values: InferOutput<typeof NewPasswordSchema>
  ) => {
    startTransition(() => {
      try {
        newPassword(values, token).then((data) => {
          if (data?.error) {
            form.reset()
            toast.error(data.error)
          }
          if (data?.success) {
            form.reset()
            toast.success(data.success)
            toast.promise(
              new Promise((resolve) =>
                setTimeout(() => resolve(push("/auth/login")), 2000)
              ),
              {
                loading: "Stai per essere reindirizzato alla pagina di login",
              }
            )
          }
        })
      } catch (err) {
        toast.error("Si è verificato un errore, riprova")
      }
    })
  }

  if (!isClient)
    return (
      <div className="flex h-96 items-center justify-center">
        <Spinner />
      </div>
    )

  if (!token) {
    return redirect("/auth/reset")
  }

  return (
    <form
      className="grid grid-cols-1 gap-6"
      onSubmit={form.handleSubmit(newPasswordAction)}
    >
      <FormItem
        label="Password"
        visibleDesc={!!form?.formState.errors?.password}
        descClassName="text-red-500"
        desc={
          form.formState.errors
            ? form.formState.errors.password?.message
            : "Inserisci la tua password"
        }
      >
        <Input
          type="password"
          autoComplete="new-password"
          disabled={isPending}
          isError={!!form?.formState.errors?.password}
          {...form.register("password")}
          className="mt-1"
        />
      </FormItem>
      <FormItem
        label="Conferma password"
        visibleDesc={!!form?.formState.errors?.confirmPassword}
        descClassName="text-red-500"
        desc={
          form.formState.errors
            ? form.formState.errors.confirmPassword?.message
            : "Inserisci la tua password"
        }
      >
        <Input
          disabled={isPending}
          autoComplete="new-password"
          type="password"
          isError={!!form?.formState.errors?.confirmPassword}
          {...form.register("confirmPassword")}
          className="mt-1"
        />
      </FormItem>
      <ButtonPrimary type="submit" disabled={isPending}>
        Cambia password
      </ButtonPrimary>
    </form>
  )
}

export default NewPasswordForm
