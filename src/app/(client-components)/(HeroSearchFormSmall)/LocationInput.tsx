"use client"

import React, { FC, useCallback, useEffect, useRef, useState } from "react"
import { useGetCitiesQuery } from "@/api/gql/generated"
import {
  isOnlineAtom,
  isSearchLoading,
  searchCityAtom,
  searchInputLocationErrorAtom,
  searchPathCityAtom,
} from "@/store/search"
import { MapPinIcon } from "@heroicons/react/24/outline"
import { useAtom, useAtomValue } from "jotai"
import { isEmpty } from "lodash"

import useOutsideAlerter from "@/hooks/useOutsideAlerter"
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"

export interface LocationInputProps {
  placeHolder?: string
  desc?: string
  className?: string
  autoFocus?: boolean
}

const LocationInput: FC<LocationInputProps> = ({
  autoFocus = false,
  placeHolder = "Città",
  desc = "Dove ti trovi?",
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const isOnline = useAtomValue(isOnlineAtom)

  const [value, setValue] = useAtom(searchCityAtom)
  const [, setPath] = useAtom(searchPathCityAtom)
  const [, setInputError] = useAtom(searchInputLocationErrorAtom)
  const [, setLoading] = useAtom(isSearchLoading)
  const [showPopover, setShowPopover] = useState(autoFocus)

  const { data, isLoading } = useGetCitiesQuery(
    {
      name: value,
    },
    {
      select: (data) =>
        data?.cities?.collection?.map((city) => ({
          label: city?.name,
          path: city?.urlKey,
        })),
    }
  )

  useEffect(() => {
    setLoading(isLoading)
  }, [isLoading, setLoading])

  useEffect(() => {
    setShowPopover(autoFocus)
    if (autoFocus && !!inputRef.current) {
      setTimeout(() => {
        inputRef.current && inputRef.current.focus()
      }, 200)
    }
  }, [autoFocus])

  useOutsideAlerter(containerRef, () => {
    setShowPopover(false)
  })

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  const handleValueChange = (newValue: string) => {
    setValue(newValue)
    if (isOnline) {
      setShowPopover(false)
    } else if (!showPopover) {
      setShowPopover(true)
    }
  }

  const handleSelect = (currentValue: string) => {
    const pickPath = data?.find(
      (framework) =>
        framework.label?.toLowerCase() === currentValue.toLowerCase()
    )?.path
    setPath(pickPath || null)
    setValue(currentValue)
    setShowPopover(false)
  }

  useEffect(() => {
    const isInvalidValue =
      !data?.some(
        (framework) => framework.label?.toLowerCase() === value?.toLowerCase()
      ) || value === ""
    setInputError(isInvalidValue)
  }, [data, value, setInputError, isOnline])

  const handleCleanup = useCallback(() => {
    setValue("")
    setPath(null)
    inputRef.current?.focus()
  }, [setValue, setPath])

  return (
    <div className={`relative flex flex-1`} ref={containerRef}>
      <Command shouldFilter={false}>
        <div
          onClick={() => {
            if (!isOnline) {
              setShowPopover(true)
            }
          }}
          className={`[ nc-hero-field-padding--small ] relative z-10 flex flex-1 flex-shrink-0 cursor-pointer items-center space-x-3  text-left focus:outline-none`}
        >
          <div className="text-neutral-300 dark:text-neutral-400">
            <MapPinIcon className="h-7 w-7" />
          </div>
          <div className="flex-grow">
            <CommandInput
              placeholder="Città"
              value={value ? value : ""}
              onValueChange={handleValueChange}
              ref={inputRef}
              className="capitalize"
              onFocus={() => {
                if (!isOnline) {
                  setShowPopover(true)
                }
              }}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  handleCleanup()
                } else if (e.key === "Tab") {
                  setShowPopover(false)
                }
              }}
            />
            <span className="mt-0.5 block text-sm font-light text-neutral-400 ">
              <span className="line-clamp-1">
                {isOnline ? "A distanza" : !!value ? placeHolder : desc}
              </span>
            </span>
            {value && (
              <ClearDataButton
                onClick={(e) => {
                  e.stopPropagation()
                  handleCleanup()
                }}
              />
            )}
          </div>
        </div>

        {showPopover && !isOnline && (
          <div className="absolute left-0 top-full z-40 mt-5  w-full min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-4 ring-primary-300 ring-offset-2 transition-all dark:bg-neutral-800 sm:py-6">
            <div className="max-h-48 overflow-y-auto md:max-h-96">
              {" "}
              {!isLoading && isEmpty(data) ? (
                <div className="flex items-center justify-start gap-x-2 px-5 text-base">
                  <span className="text-base">Nessuna Città trovata.</span>
                  <ClearDataButton
                    className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCleanup()
                    }}
                  >
                    Cancella Filtri
                  </ClearDataButton>
                </div>
              ) : null}
              <CommandGroup>
                {data?.map((framework) => (
                  <CommandItem
                    key={framework.path}
                    value={framework.label}
                    onSelect={handleSelect}
                  >
                    {framework.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </div>
          </div>
        )}
      </Command>
    </div>
  )
}

export default LocationInput
