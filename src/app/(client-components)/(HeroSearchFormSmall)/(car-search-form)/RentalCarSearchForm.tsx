"use client"

import React, { FC } from "react"
import { Route } from "next"
import { useRouter } from "next/navigation"
import {
  byType<PERSON>tom,
  isOnlineAtom,
  isSearchLoading,
  oldCity,
  searchCityAtom,
  searchInputErrorAtom,
  searchInputLocationErrorAtom,
  searchPathServiceAtom,
} from "@/store/search"
import {
  BuildingOffice2Icon,
  VideoCameraIcon,
} from "@heroicons/react/24/outline"
import { useAtom, useAtomValue } from "jotai"

import ButtonSubmit from "@/app/(client-components)/(HeroSearchForm)/ButtonSubmit"

import { SearchFormFields } from "../../type"
import LocationInput from "../LocationInput"
import ServiceInput from "../ServiceInput"

export interface RentalCarSearchFormProps {
  defaultFieldFocus?: SearchFormFields
}

const RentalCarSearchForm: FC<RentalCarSearchFormProps> = ({
  defaultFieldFocus,
}) => {
  const [byType, setByType] = useAtom(byType<PERSON>tom)
  const [oldLocation, setOldLocation] = useAtom(oldCity)
  const [location, setLocation] = useAtom(searchCityAtom)
  const [loading, setIsLoading] = useAtom(isSearchLoading)
  const servicePath = useAtomValue(searchPathServiceAtom)
  const inputLocationError = useAtomValue(searchInputLocationErrorAtom)
  const inputServiceError = useAtomValue(searchInputErrorAtom)
  const [isOnline, setIsOnline] = useAtom(isOnlineAtom)
  const router = useRouter()

  const handleClick = async (type: "byCity" | "online" | null) => {
    setByType(type)
    if (type === "byCity") {
      setLocation(oldLocation)
      setIsOnline(false)
    }
    if (type === "online") {
      setOldLocation(location)
      setLocation(null)
      setIsOnline(true)
    }
  }

  const buildQueryFilters = () => {
    const filters = []
    if (isOnline) filters.push("online=true")
    return filters.length > 0 ? `?${filters.join("&")}` : ""
  }

  const buildTargetPath = () => {
    const locationPath = location && !inputLocationError ? `/${location}` : ""
    const servicePathSuffix = servicePath ? `/${servicePath}` : ""
    return `/ricerca${servicePathSuffix}${locationPath}${buildQueryFilters()}`
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    router.push(buildTargetPath() as Route)
  }

  const renderRadioBtn = () => {
    return (
      <div className="flex items-center justify-center space-x-3 pb-3">
        <div
          className={`flex cursor-pointer items-center rounded-full px-4 py-1.5 text-xs font-medium ${
            byType === "byCity"
              ? "bg-primary-6000 text-white shadow-lg shadow-black/10"
              : "border border-neutral-300 dark:border-neutral-700"
          }`}
          onClick={async () => await handleClick("byCity")}
        >
          <BuildingOffice2Icon className="mr-1 h-4 w-4" />
          Per città
        </div>
        <div
          className={`flex cursor-pointer items-center rounded-full px-4 py-1.5 text-xs font-medium ${
            byType === "online"
              ? "bg-primary-6000 text-white shadow-lg shadow-black/10"
              : "border border-neutral-300 dark:border-neutral-700"
          }`}
          onClick={async () => await handleClick("online")}
        >
          <VideoCameraIcon className="mr-1 h-4 w-4" />
          Online
        </div>
      </div>
    )
  }

  const renderForm = () => {
    return (
      <form className="relative w-full" onSubmit={handleSubmit}>
        {renderRadioBtn()}
        <div className="flex w-full flex-row rounded-full border border-neutral-200 bg-white dark:border-neutral-700 dark:bg-neutral-800">
          <ServiceInput autoFocus={defaultFieldFocus === "service"} />
          {byType !== "online" ? (
            <>
              <div className="hidden h-8 self-center border-r border-slate-200 dark:border-slate-700 md:block"></div>
              <LocationInput autoFocus={defaultFieldFocus === "location"} />{" "}
            </>
          ) : null}
          <div className="hidden h-8 self-center border-r border-slate-200 dark:border-slate-700 md:block"></div>
          <div className="flex items-center pl-2 pr-1">
            <ButtonSubmit disabled={inputServiceError} loading={loading} />
          </div>
        </div>
      </form>
    )
  }

  return renderForm()
}

export default RentalCarSearchForm
