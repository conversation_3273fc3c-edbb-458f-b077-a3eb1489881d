"use client"

import React, { FC, useCallback, useEffect, useRef, useState } from "react"
import { useGetConsultingServicesQuery } from "@/api/gql/generated"
import { PathName } from "@/routers/types"
import {
  isSearchLoading,
  searchInputErrorAtom,
  searchPathServiceAtom,
  searchServiceAtom,
} from "@/store/search"
import { groupByCategory } from "@/utils/groupServices"
import { AcademicCapIcon } from "@heroicons/react/24/outline"
import { useAtom } from "jotai"
import { isEmpty } from "lodash"

import useOutsideAlerter from "@/hooks/useOutsideAlerter"
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"

export interface ServiceInputProps {
  placeHolder?: string
  desc?: string
  className?: string
  divHideVerticalLineClass?: string
  autoFocus?: boolean
  hasButtonSubmit?: boolean
  buttonSubmitHref?: PathName
}

const ServiceInput: FC<ServiceInputProps> = ({
  autoFocus = false,
  placeHolder = "Servizio",
  desc = "Di cosa hai bisogno?",
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const [showPopover, setShowPopover] = useState(autoFocus)

  const [value, setValue] = useAtom(searchServiceAtom)
  const [, setPath] = useAtom(searchPathServiceAtom)
  const [, setInputError] = useAtom(searchInputErrorAtom)
  const [, setLoading] = useAtom(isSearchLoading)

  const { data, isLoading } = useGetConsultingServicesQuery(
    {},
    {
      select: (data) =>
        data?.consultingServices?.collection ?? [],
    }
  )

  useEffect(() => {
    setLoading(isLoading)
  }, [isLoading, setLoading])

  useEffect(() => {
    setShowPopover(autoFocus)
    if (autoFocus && !!inputRef.current) {
      setTimeout(() => {
        inputRef.current && inputRef.current.focus()
      }, 200)
    }
  }, [autoFocus])

  useOutsideAlerter(containerRef, () => {
    setShowPopover(false)
  })

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  useEffect(() => {
    const isInData = data?.some(
      (service) => service?.name?.toLowerCase() === value.toLowerCase()

    )

    setInputError(!isInData || value === "")
  }, [data, setInputError, value])

  const handleSelect = (currentValue: string) => {
    let pickPath, professionUrl, macroCategory

    for (const service of data || []) {


      pickPath = service?.urlKey
      break
    }

    const path = professionUrl ? `/${professionUrl}` : ""
    const categoryPath = macroCategory ? `/${macroCategory}` : ""
    const servicePath = pickPath ? `/${pickPath}` : ""
    const completePath = `${path}${categoryPath}${servicePath}`
    setPath(completePath || null)
    setValue(currentValue)
    setShowPopover(false)
  }

  const handleCleanup = useCallback(() => {
    setValue("")
    setPath(null)
    inputRef.current?.focus()
  }, [setValue, setPath])

  const handleValueChange = (newValue: string) => {
    setValue(newValue)
    if (!showPopover) {
      setShowPopover(true)
    }
  }

  return (
    <div className="relative flex flex-1" ref={containerRef}>
      <Command shouldFilter={false}>
        <div
          onClick={() => setShowPopover(true)}
          className={`[ nc-hero-field-padding--small ] relative z-10 flex flex-1 flex-shrink-0 cursor-pointer items-center space-x-3 text-left focus:outline-none`}
        >
          <div className="text-neutral-300 dark:text-neutral-400">
            <AcademicCapIcon className="h-7 w-7" />
          </div>
          <div className="flex-grow">
            <CommandInput
              placeholder="Ricerca servizio"
              value={value}
              onValueChange={handleValueChange}
              ref={inputRef}
              onFocus={() => setShowPopover(true)}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  handleCleanup()
                } else if (e.key === "Tab") {
                  setShowPopover(false)
                }
              }}
            />
            <span className="mt-0.5 block text-sm font-light text-neutral-400 ">
              <span className="line-clamp-1">
                {!!value ? placeHolder : desc}
              </span>
            </span>
            {value && (
              <ClearDataButton
                onClick={(e) => {
                  e.stopPropagation()
                  handleCleanup()
                }}
              />
            )}
          </div>
        </div>
        {showPopover && (
          <div className="absolute left-0 top-full z-40 mt-5  w-full min-w-[300px] rounded-3xl bg-white py-3 shadow-xl ring-4 ring-primary-300 ring-offset-2 transition-all dark:bg-neutral-800 sm:py-6">
            <div className="max-h-48 overflow-y-auto md:max-h-96">
              {!isLoading && isEmpty(data) ? (
                <div className="flex items-center justify-start gap-x-2 px-5 text-base">
                  <span className="text-base">
                    Nessun Professionista trovato.
                  </span>
                  <ClearDataButton
                    className="font-semibold text-primary-500 underline underline-offset-4 hover:text-primary-6000"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCleanup()
                    }}
                  >
                    Cancella Filtri
                  </ClearDataButton>
                </div>
              ) : null}
              {data?.filter((item) => item?.name?.toLowerCase().includes(value.toLowerCase()))?.map((service) => {
                if (service) {
                  return (
                    <CommandItem
                      key={service._id}
                      value={service._id.toString() ?? ""}
                      onSelect={() => handleSelect(service.name ?? "")}
                    >
                      {service.name}
                    </CommandItem>
                  )
                }
              }
              )}
            </div>
          </div>
        )}
      </Command>
    </div>
  )
}

export default ServiceInput
