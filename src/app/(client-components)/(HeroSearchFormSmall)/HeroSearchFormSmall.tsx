"use client"

import React, { <PERSON> } from "react"

import { SearchFormFields } from "../type"
import RentalCarSearchForm from "./(car-search-form)/RentalCarSearchForm"

export type SearchTab = "Stays" | "Experiences" | "Cars" | "Flights"

export interface HeroSearchFormSmallProps {
  className?: string
  defaultTab?: SearchTab
  onTabChange?: (tab: SearchTab) => void
  defaultFieldFocus?: SearchFormFields
}

const HeroSearchFormSmall: FC<HeroSearchFormSmallProps> = ({
  className = "",
  defaultFieldFocus,
}) => {
  return (
    <div
      className={`nc-HeroSearchFormSmall ${className}`}
      data-nc-id="HeroSearchFormSmall"
    >
      <div className="mt-6">
        <RentalCarSearchForm defaultFieldFocus={defaultFieldFocus} />
      </div>
    </div>
  )
}

export default HeroSearchFormSmall
