"use client"

import React, { Fragment, useState } from "react"
import { modalSearchAtom } from "@/store/search"
import { Dialog, Transition } from "@headlessui/react"
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline"
import { XMarkIcon } from "@heroicons/react/24/solid"
import { useAtom } from "jotai"

import { useTimeoutFn } from "@/hooks/useTimeoutFn"

import RentalCarSearchFormMobile from "../(HeroSearchFormSmall)/(car-search-form)/RentalCarSearchFormMobile"

const HeroSearchForm2Mobile = () => {
  const [showModal, setShowModal] = useAtom(modalSearchAtom)

  // FOR RESET ALL DATA WHEN CLICK CLEAR BUTTON
  const [showDialog, setShowDialog] = useState(false)
  let [, , resetIsShowingDialog] = useTimeoutFn(() => setShowDialog(true), 1)

  //
  function closeModal() {
    setShowModal(false)
  }

  function openModal() {
    setShowModal(true)
  }

  const renderButtonOpenModal = () => {
    return (
      <button
        onClick={openModal}
        className="relative flex w-full items-center rounded-full border border-neutral-200 px-4 py-2 pr-11 shadow-lg dark:border-neutral-6000"
      >
        <MagnifyingGlassIcon className="h-5 w-5 flex-shrink-0" />

        <div className="ml-3 flex-1 overflow-hidden text-left">
          <span className="block text-xs font-medium md:text-sm">
            Cerca il tuo consulente
          </span>
          <span className="mt-0.5 hidden text-xs font-light text-neutral-500 dark:text-neutral-400 md:block ">
            <span className="line-clamp-1">
              Consulenti da tutta Italia a tua disposizione
            </span>
          </span>
        </div>
      </button>
    )
  }

  return (
    <div className="HeroSearchForm2Mobile">
      {renderButtonOpenModal()}
      <Transition appear show={showModal} as={Fragment}>
        <Dialog
          as="div"
          className="HeroSearchFormMobile__Dialog z-max relative"
          onClose={closeModal}
        >
          <div className="fixed inset-0 bg-neutral-100 dark:bg-neutral-900">
            <div className="flex h-full">
              <Transition.Child
                as={Fragment}
                enter="ease-out transition-transform"
                enterFrom="opacity-0 translate-y-52"
                enterTo="opacity-100 translate-y-0"
                leave="ease-in transition-transform"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 translate-y-52"
              >
                <Dialog.Panel className="relative flex h-full flex-1 flex-col justify-between overflow-hidden ">
                  {showDialog && (
                    <>
                      <div className="absolute right-4 top-4">
                        <button className="" onClick={closeModal}>
                          <XMarkIcon className="h-5 w-5 text-black dark:text-white" />
                        </button>
                      </div>

                      <div className="flex flex-1 overflow-hidden px-1.5 pt-3 sm:px-4">
                        <div className="hiddenScrollbar flex-1 overflow-y-auto py-4">
                          <div className="animate-[myblur_0.4s_ease-in-out] transition-opacity">
                            <RentalCarSearchFormMobile />
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  )
}

export default HeroSearchForm2Mobile
