import React, { <PERSON> } from "react"
import Image from "next/image"
import Link from "next/link"
import facebookSvg from "@/images/Facebook.svg"
import googleSvg from "@/images/Google.svg"
import twitterSvg from "@/images/Twitter.svg"

import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"

export interface PageLoginProps {}

const PageLogin: FC<PageLoginProps> = ({}) => {
  return (
    <div className={`nc-PageLogin`}>
      <div className="container mb-24 lg:mb-32">
        <h2 className="my-20 flex items-center justify-center text-3xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-5xl md:leading-[115%]">
          Recupera password
        </h2>
        <div className="mx-auto max-w-md space-y-6">
          {/* FORM */}
          <form className="grid grid-cols-1 gap-6" action="#" method="post">
            <label className="block">
              <span className="text-neutral-800 dark:text-neutral-200">
                Email address
              </span>
              <Input type="email" className="mt-1" />
            </label>
            <label className="block">
              <span className="flex items-center justify-between text-neutral-800 dark:text-neutral-200">
                Password
                <Link href="/login" className="text-sm font-medium underline">
                  Forgot password?
                </Link>
              </span>
              <Input type="password" className="mt-1" />
            </label>
            <ButtonPrimary type="submit">Continue</ButtonPrimary>
          </form>

          {/* ==== */}
          <span className="block text-center text-neutral-700 dark:text-neutral-300">
            Vuoi entrare a far parte di Consulente Ideale? {` `}
            <Link
              href="/auth/registrazione"
              className="font-semibold underline"
            >
              Crea un account
            </Link>
          </span>
        </div>
      </div>
    </div>
  )
}

export default PageLogin
