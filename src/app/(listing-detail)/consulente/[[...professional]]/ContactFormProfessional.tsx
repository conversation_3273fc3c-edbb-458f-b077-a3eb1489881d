"use client"

import { useEffect, useRef } from "react"
import { useCreateContactRequestMutation } from "@/api/gql/generated"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { sendGTMEvent } from "@next/third-parties/google"
import Captcha from "react-google-recaptcha"
import { useForm, type SubmitHandler } from "react-hook-form"
import { toast } from "sonner"
import { email, minLength, object, string, type InferOutput, pipe } from "valibot"

import ButtonPrimary from "@/shared/ButtonPrimary"
import Input from "@/shared/Input"
import Textarea from "@/shared/Textarea"
import RecaptchaDisclaimer from "@/components/RecaptchaDisclaimer"
import FormItem from "@/app/(Form)/FormItem"

export const FormSchema = object({
  fullName: pipe(string("Per favore inserisci un nome."),
    minLength(1, "Per favore inserisci un nome.")
  ),
  mail: pipe(string("Per favore inserisci una mail corretta."),
    minLength(1, "Per favore inserisci una mail."),
    email("Per favore inserisci una mail corretta.")
  ),
  telephone: pipe(string("Per favore inserisci un numero di telefono."),
    minLength(1, "Per favore inserisci un numero di telefono."),
  ),
  message: pipe(string("Per favore inserisci un messaggio."),
    minLength(1, "Per favore inserisci un messaggio."),
  ),
})

export type FormSchemaType = InferOutput<typeof FormSchema>
export type ContactFormProfessionalProps = {
  consultant: number
  service?: number
}

const ContactFormProfessional: React.FC<ContactFormProfessionalProps> = ({
  consultant,
  service = 0,
}) => {
  const captchaRef = useRef<Captcha>(null)

  const form = useForm<FormSchemaType>({
    resolver: valibotResolver(FormSchema),
    mode: "onSubmit",
  })

  const { mutate } = useCreateContactRequestMutation({
    onSuccess: () => {
      form.reset()
      toast.success("Il tuo messaggio è stato inviato.", {
        description: "Verrai ricontattato al più presto.",
      })
      sendGTMEvent({
        event: "generate_lead_preventivo",
      })
      if (typeof window !== "undefined" && window.fbq) {
        try {
          window.fbq("track", "Contact")
        } catch (e) {
          console.error(e)
        }
      }
    },
    onError: () => {
      toast.error("Errore nell'invio del messaggio, prova di nuovo.")
    },
  })

  const onSubmit: SubmitHandler<FormSchemaType> = async (data) => {
    const captcha = await captchaRef.current?.executeAsync()
    const res = await fetch("/subscribe", {
      method: "POST",
      body: JSON.stringify({ captcha }),
      headers: { "Content-type": "application/json" },
    })

    if (res.status === 200) {
      mutate({
        input: {
          customerFullName: data.fullName,
          customerEmail: data.mail,
          customerTelephone: data.telephone.toString(),
          message: data.message,
          consultant,
          service,
        },
      })
    } else {
      toast.error("Errore di CAPTCHA.")
    }
  }

  return (
    <>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid grid-cols-1 gap-3"
      >
        <FormItem
          label="Nome completo"
          visibleDesc={!!form.formState.errors.fullName}
          descClassName="text-red-500"
          desc={
            form.formState.errors.fullName
              ? form.formState.errors.fullName.message
              : "Inserisci il tuo nome"
          }
        >
          <Input
            placeholder="Nome completo"
            {...form.register("fullName")}
            isError={!!form?.formState?.errors?.fullName?.message}
          />
        </FormItem>
        <FormItem
          label="Email"
          visibleDesc={!!form.formState.errors.mail}
          descClassName="text-red-500"
          desc={
            form.formState.errors.mail
              ? form.formState.errors.mail.message
              : "Inserisci la tua email"
          }
        >
          <Input
            placeholder="Email"
            {...form.register("mail")}
            isError={!!form?.formState?.errors?.mail?.message}
          />
        </FormItem>
        <FormItem
          label="Telefono"
          visibleDesc={!!form.formState.errors.telephone}
          descClassName="text-red-500"
          desc={
            form.formState.errors.telephone
              ? form.formState.errors.telephone.message
              : "Inserisci il tuo numero di telefono"
          }
        >
          <Input
            placeholder="Telefono"
            type="tel"
            {...form.register("telephone")}
            isError={!!form?.formState?.errors?.telephone?.message}
          />
        </FormItem>
        <FormItem
          label="Messaggio"
          descClassName="text-red-500"
          visibleDesc={!!form.formState.errors.message}
          desc={
            form.formState.errors.message
              ? form.formState.errors.message.message
              : "Inserisci la tua email"
          }
        >
          <Textarea
            className="mt-1"
            rows={6}
            placeholder="Scrivi il tuo messaggio"
            isError={!!form?.formState?.errors?.message?.message}
            {...form.register("message")}
          />
        </FormItem>
        <Captcha
          ref={captchaRef}
          size="invisible"
          sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
        />
        <div>
          <ButtonPrimary type="submit">Invia Messaggio</ButtonPrimary>
        </div>
      </form>
      <RecaptchaDisclaimer />
    </>
  )
}

export default ContactFormProfessional
