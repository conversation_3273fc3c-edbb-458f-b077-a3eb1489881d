"use client"

import React, { ButtonHTMLAttributes } from "react"
import { useRouter } from "next/navigation"
import { ChevronLeftIcon } from "@heroicons/react/24/outline"

export interface ButtonCircleProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  size?: string
  iconSize?: string
}

const ButtonCircle: React.FC<ButtonCircleProps> = ({
  className = " ",
  size = " w-9 h-9 ",
  iconSize = "h-5 w-5",
  ...args
}) => {
  const { back } = useRouter()
  return (
    <button
      className="inline-flex h-11 w-11 cursor-pointer items-center justify-center rounded-full border border-neutral-200 bg-white text-neutral-600 hover:bg-neutral-100 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:hover:bg-neutral-800"
      {...args}
      onClick={back}
    >
      <ChevronLeftIcon className={`text-neutral-9000 ${iconSize}`} />
    </button>
  )
}

export default ButtonCircle
