"use client"

import { Fragment, useState } from "react"
import { Dialog, Transition } from "@headlessui/react"

import ButtonClose from "@/shared/ButtonClose"
import ButtonSecondary from "@/shared/ButtonSecondary"
import CommentListing from "@/components/CommentListing"

export type ModalReviewsProps = {
  points: number
  reviewsCount: number
  reviews: {
    __typename?: "ConsultantReview" | undefined
    createdAt?: string | null | undefined
    rating?: number | null | undefined
    description?: string | null | undefined
    authorName?: string | null | undefined
  }[]
}
const ModalReviews: React.FC<ModalReviewsProps> = ({
  reviews,
  reviewsCount,
  points,
}) => {
  const [isOpenModalReviews, setIsOpenModalReviews] = useState(false)
  const openModalReviews = () => setIsOpenModalReviews(true)
  const closeModalReviews = () => setIsOpenModalReviews(false)

  const firstFiveReviews = Array.from(reviews).slice(0, 5)

  const renderModalReviews = () => {
    return (
      <Transition appear show={isOpenModalReviews} as={Fragment}>
        <Dialog
          as="div"
          className="fixed inset-0 z-50 overflow-y-auto"
          onClose={closeModalReviews}
        >
          <div className="min-h-screen px-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-40" />
            </Transition.Child>

            {/* This element is to trick the browser into centering the modal contents. */}
            <span
              className="inline-block h-screen align-middle"
              aria-hidden="true"
            >
              &#8203;
            </span>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <div className="inline-block h-screen w-full max-w-4xl py-8">
                <div className="inline-flex h-full w-full transform flex-col overflow-hidden rounded-2xl bg-white pb-2 text-left align-middle shadow-xl transition-all dark:border dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-100">
                  <div className="relative flex-shrink-0 border-b border-neutral-200 px-6 py-4 text-center dark:border-neutral-800">
                    <h3
                      className="text-lg font-medium leading-6 text-gray-900"
                      id="headlessui-dialog-title-70"
                    >
                      Recensioni
                    </h3>
                    <span className="absolute left-3 top-3">
                      <ButtonClose onClick={closeModalReviews} />
                    </span>
                  </div>
                  <div className="divide-y divide-neutral-200 overflow-auto px-8 text-neutral-700 dark:text-neutral-300">
                    {reviews.map((review) => (
                      <CommentListing
                        data={review}
                        className="py-8"
                        key={review?.createdAt}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition>
    )
  }

  const renderSection6 = () => {
    return (
      <div className="flex w-full flex-col space-y-3 border-b border-neutral-200 px-0 pb-10 dark:border-neutral-700 sm:space-y-6 sm:rounded-2xl sm:border-l sm:border-r sm:border-t sm:p-4 xl:p-8">
        {/* HEADING */}
        <h2 className="text-2xl font-semibold">Recensioni</h2>
        <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
        {/* comment */}
        <div className="divide-y divide-neutral-100 dark:divide-neutral-800">
          {firstFiveReviews.map((review) => (
            <CommentListing
              data={review as any}
              className="py-8"
              key={review?.createdAt}
            />
          ))}
          {reviewsCount > 5 && (
            <>
              <div className="w-14 border-b border-neutral-200"></div>
              <div>
                <ButtonSecondary className="mt-4" onClick={openModalReviews}>
                  Vedi tutte le recensioni
                </ButtonSecondary>
              </div>
              {renderModalReviews()}
            </>
          )}
        </div>
      </div>
    )
  }
  return (
    <div className="flex w-full flex-col space-y-6">{renderSection6()}</div>
  )
}

export default ModalReviews
