import React, { <PERSON> } from "react"
import { notFound, redirect } from "next/navigation"
import {
  GetConsultantQ<PERSON>y,
  Offer,
  useGetConsultantQuery,
} from "@/api/gql/generated"
import { serverFetch } from "@/api/gql/queryUtils"
import { MapPinIcon } from "@heroicons/react/24/outline"
import { isEmpty } from "lodash"
import sanitizeHtml from "sanitize-html"

import Avatar from "@/shared/Avatar"
import Badge from "@/shared/Badge"
import { ComboboxForm } from "@/components/ConsultantServiceSearch"
import ServicesTable from "@/components/ServicesTable"
import StartRating from "@/components/StartRating"

import CompleteFormDialog from "../../(components)/CompleteFormDialog"
import BackButton from "./BackButton"
import ContactFormProfessional from "./ContactFormProfessional"
import ModalReviews from "./ModalReviews"

export interface ListingStayDetailPageProps {
  params: {
    professional: string
  }
}

/**
 * @returns {Promise<import("next").Metadata>}
 */

export async function generateMetadata({ params: { professional } }: { params: { professional: any } }) {

  if (!professional) redirect("/")

  let query: GetConsultantQuery["byUrlKeyConsultant"]
  try {
    query = await serverFetch(useGetConsultantQuery, {
      variables: {
        urlKey: professional[0],
      },
      cache: "no-cache",
      select: (data) => {
        return data.byUrlKeyConsultant
      },
    })
  } catch (error) {
    notFound()
  }

  return {
    title: `${query?.fullName} - Consulente Ideale - Prenota online il tuo consulente ideale`,
    description: query?.bio,
    metadataBase: new URL(process.env.NEXT_PUBLIC_WEBSITE_URL ?? ""),

    openGraph: {
      url: `/professionista/${query?.urlKey}`,
      title: `${query?.fullName} - Consulente Ideale - Prenota online il tuo consulente ideale`,
      description: query?.bio,
      siteName: "Consulente Ideale",
      images: [`${process.env.NEXT_PUBLIC_BACKEND_MEDIA_URL}/avatars/${query?.avatar}`],
    }
  };
}

const ListingStayDetailPage: FC<ListingStayDetailPageProps> = async ({
  params: { professional },
}) => {
  if (!professional) redirect("/")

  let query: GetConsultantQuery["byUrlKeyConsultant"]
  try {
    query = await serverFetch(useGetConsultantQuery, {
      variables: {
        urlKey: professional[0],
      },
      cache: "no-cache",
      select: (data) => {
        return data.byUrlKeyConsultant
      },
    })
  } catch (error) {
    notFound()
  }
  const services = query?.offers?.collection?.map((item) => item)

  const professions = query?.professions?.collection?.map((item) => item);

  const reviewsMap = query?.reviews?.collection?.map((item) => item)

  const accReviews =
    reviewsMap?.reduce((acc, item) => {
      return acc + (item?.rating ?? 0)
    }, 0) ?? 0

  const medianReviews =
    (reviewsMap?.length ?? 0) > 0
      ? accReviews / Math.round(reviewsMap?.length ?? 0)
      : 0

  const renderSection1 = () => {
    return (
      <div className="listingSection__wrap !space-y-6">
        {/* <CompleteFormDialog /> */}
        <BackButton />
        {/* 1 */}
        <div className="flex flex-wrap items-center gap-2">
          {!isEmpty(professions)
            ? professions?.map((profession, index) => {
              if (profession) {
                return (
                  <Badge key={index} name={profession?.name} />
                )
              }
            })
            : null}
        </div>
        {/* 2 */}
        <div className="flex items-center">
          <Avatar
            hasChecked
            sizeClass="h-14 w-14"
            radius="rounded-full"
            imgUrl={query?.avatar}
          />
          <h1 className="ml-2.5 text-lg font-semibold sm:text-xl lg:text-2xl">
            {query?.fullName}
          </h1>
        </div>
        {/* 3 */}
        <div className="flex items-center space-x-4">
          {accReviews > 0 ? (
            <>
              <StartRating
                point={medianReviews}
                reviewCount={reviewsMap?.length}
              />
              <span>·</span>
            </>
          ) : null}
          {query?.city?.name ? (
            <div className="flex items-center space-x-2">
              <MapPinIcon className="h-5 w-5" />
              <span>{`${query?.city?.name}`}</span>
            </div>
          ) : null}
        </div>
        {query?.bio ? (
          <>
            <div className="w-full border-b border-neutral-100 dark:border-neutral-700" />
            <div className="space-y-4 text-neutral-6000 dark:text-neutral-300">
              <div
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: sanitizeHtml(query.bio) }}
              />
            </div>
          </>
        ) : null}
        {!isEmpty(query?.tags?.collection) && (
          <>
            <div className="w-full border-b border-neutral-100 dark:border-neutral-700" />
            <div className="flex flex-wrap gap-2">
              {query?.tags?.collection?.map((tag, index) => (
                <div
                  key={index}
                  className="flex w-fit items-center justify-center rounded-full border border-neutral-300 px-4 py-2 text-sm transition-all focus:outline-none dark:border-neutral-700"
                >
                  <span>{tag?.name}</span>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    )
  }

  const servicesSection = () => {
    return (
      <div className="listingSection__wrap">
        {/* HEADING */}
        <div>
          <h2 className="text-2xl font-semibold">Servizi</h2>
          <span className="mt-2 block text-neutral-500 dark:text-neutral-400">
            Qui sotto troverai un elenco dettagliato dei servizi offerti dal
            nostro specialista
          </span>
        </div>
        <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
        {/* CONTENT */}
        <div className="flow-root">
          <div className=" text-sm text-neutral-6000 dark:text-neutral-300 sm:text-base">
            <ServicesTable services={services as Offer[]} />
          </div>
        </div>
      </div>
    )
  }

  const ContactMeSection = () => {
    return (
      <div className="listingSection__wrap">
        {/* HEADING */}
        <div>
          <h2 className="text-xl font-semibold">
            Ottieni un preventivo gratuito
          </h2>
          <span className="mt-2 block text-neutral-500 dark:text-neutral-400">
            Scrivi qui sotto il tuo messaggio e sarai ricontattato al più presto
            dal nostro specialista.
          </span>
        </div>
        <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
        {/* CONTENT */}
        <ContactFormProfessional consultant={query?._id!} />
      </div>
    )
  }

  const renderSidebar = () => {
    return (
      <div className="listingSectionSidebar__wrap shadow-xl">
        {/* PRICE */}
        <div className="flex justify-between">
          <span className="text-lg font-semibold">
            Prenota una consulenza gratuita
          </span>
        </div>

        {/* FORM */}
        <ComboboxForm consultant={professional[0]} />
      </div>
    )
  }

  return (
    <div className="nc-ListingStayDetailPage mt-4">
      {/* MAIN */}
      <main className=" relative z-10 flex flex-col lg:flex-row">
        {/* CONTENT */}
        <div className="mt-5 w-full space-y-8 lg:w-3/5 lg:space-y-10 lg:pr-10 xl:w-2/3">
          {renderSection1()}
          {!isEmpty(services) && services ? servicesSection() : null}
          {ContactMeSection()}
          {!isEmpty(reviewsMap) ? (
            <ModalReviews
              points={medianReviews}
              reviewsCount={accReviews}
              reviews={reviewsMap as any}
            />
          ) : null}
        </div>

        {/* SIDEBAR */}
        <div className="mt-14 hidden flex-grow lg:mt-0 lg:block">
          <div className="sticky top-24">{renderSidebar()}</div>
        </div>
      </main>
    </div>
  )
}

export default ListingStayDetailPage
