"use client"

import React, { FC, Fragment, useEffect, useMemo, useState } from "react"
import { Popover, Transition } from "@headlessui/react"
import { CalendarIcon } from "@heroicons/react/24/outline"
import it from "date-fns/locale/it"
import { DateTime } from "luxon"
import DatePicker, { registerLocale } from "react-datepicker"

import DatePickerCustomDay from "@/components/DatePickerCustomDay"
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"

export interface StayDatesRangeInputProps {
  className?: string
  handleChangeDate?: (date: Date | null) => void
}

registerLocale("it", it)

const StayDatesRangeInput: FC<StayDatesRangeInputProps> = ({
  className = "flex-1",
  handleChangeDate,
}) => {
  const [startDate, setStartDate] = useState<Date | null>(null)

  const minDate = useMemo(
    () =>
      DateTime.now().hour > 16
        ? DateTime.now().plus({ days: 1 }).toJSDate()
        : DateTime.now().toJSDate(),
    []
  )

  const onChangeDate = (
    date: Date | null,
    close: {
      (
        focusableElement?:
          | HTMLElement
          | React.MouseEvent<HTMLElement, MouseEvent>
          | React.MutableRefObject<HTMLElement | null>
          | undefined
      ): void
    }
  ) => {
    setStartDate(date)
    handleChangeDate && handleChangeDate(date)
    close()
  }

  const renderInput = () => {
    return (
      <>
        <div className="text-neutral-300 dark:text-neutral-400">
          <CalendarIcon className="h-5 w-5 lg:h-7 lg:w-7" />
        </div>
        <div className="flex-grow text-left">
          <span className="block font-semibold xl:text-lg">
            {startDate?.toLocaleDateString("it-IT", {
              month: "long",
              day: "2-digit",
              year: "numeric",
            }) || "Scegli una data"}
          </span>
          <span className="mt-1 block text-sm font-light leading-none text-neutral-400">
            {"Che giorno vuoi essere ricontattato?"}
          </span>
        </div>
      </>
    )
  }

  return (
    <Popover className={`StayDatesRangeInput relative z-10 flex ${className}`}>
      {({ open, close }) => (
        <>
          <Popover.Button
            className={`relative flex w-full flex-1 items-center space-x-3 p-3 focus:outline-none ${
              open ? "shadow-lg" : ""
            }`}
          >
            {renderInput()}
            {startDate && open && (
              <ClearDataButton onClick={() => onChangeDate(null, close)} />
            )}
          </Popover.Button>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-200"
            enterFrom="opacity-0 translate-y-1"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-150"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 translate-y-1"
          >
            <Popover.Panel className="absolute top-full z-10 mt-3 w-full sm:w-fit">
              <div className="w-full overflow-hidden rounded-3xl bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-neutral-800">
                <DatePicker
                  selected={startDate}
                  onChange={(date) => (date ? onChangeDate(date, close) : null)}
                  startDate={undefined}
                  selectsRange={false}
                  filterDate={(date) =>
                    date.getDay() !== 0 && date.getDay() !== 6
                  }
                  minDate={minDate}
                  showPopperArrow={false}
                  inline
                  locale="it"
                  renderCustomHeader={(p) => (
                    <DatePickerCustomHeaderTwoMonth {...p} />
                  )}
                  renderDayContents={(day, date) => (
                    <DatePickerCustomDay dayOfMonth={day} date={date} />
                  )}
                />
              </div>
            </Popover.Panel>
          </Transition>
        </>
      )}
    </Popover>
  )
}

export default StayDatesRangeInput
