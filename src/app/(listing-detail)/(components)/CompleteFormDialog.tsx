"use client"

import {
  BaseSyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  type FC,
} from "react"
import Link from "next/link"
import { useParams } from "next/navigation"
import {
  useCreateOrderMutation,
  useGetConsultantQuery,
} from "@/api/gql/generated"
import { formStateAtom, formStepAtom } from "@/store/checkout"
import { modalCheckoutAtom } from "@/store/modal"
import { convertDateToItalianWithTime } from "@/utils/localesDate"
import {
  AcademicCapIcon,
  BanknotesIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline"
import { valibotResolver } from "@hookform/resolvers/valibot"
import { sendGTMEvent } from "@next/third-parties/google"
import { useAtom } from "jotai"
import { isEmpty, isUndefined } from "lodash"
import { DateTime } from "luxon"
import Captcha from "react-google-recaptcha"
import { Form, useForm } from "react-hook-form"
import { toast } from "sonner"
import {
  boolean,
  email,
  minLength,
  object,
  regex,
  string,
  value,
  type InferOutput,
  pipe
} from "valibot"

import { cn } from "@/lib/utils"
import Avatar from "@/shared/Avatar"
import ButtonPrimary from "@/shared/ButtonPrimary"
import Checkbox from "@/shared/Checkbox"
import Input from "@/shared/Input"
import List from "@/shared/List"
import RecaptchaDisclaimer from "@/components/RecaptchaDisclaimer"
import StartRating from "@/components/StartRating"
import FormItem from "@/app/(Form)/FormItem"
import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@radix-ui/react-dialog"

interface Props {
  onClose?: () => void
}

const FormSchema = object({
  name: pipe(string("Per favore inserisci un nome."),
    minLength(1, "Per favore inserisci un nome.")
  ),
  surname: pipe(string("Per favore inserisci un cognome."),
    minLength(1, "Per favore inserisci un cognome.")
  ),
  mail: pipe(string("Per favore inserisci una mail corretta."),
    minLength(1, "Per favore inserisci una mail."),
    email("Per favore inserisci una mail corretta.")
  ),
  phone: pipe(string("Per favore inserisci un numero di telefono"),
    regex(
      /^(\+\d{1,3}[- ]?)?\d{10}$/,
      "Per favore inserisci un numero di telefono valido."
    )
  ),
  acceptedConditions: pipe(boolean("Per favore accetta i termini e le condizioni."),
    value(true, "Per favore accetta i termini e le condizioni.")
  ),
})

const lists = [
  {
    id: 1,
    content: (
      <>
        <span className="font-semibold">
          Riceverai una mail di conferma della tua prenotazione
        </span>{" "}
        e verrai ricontattato dal consulente nella fascia oraria da te indicata.
      </>
    ),
    icon: EnvelopeIcon,
    iconBackground: "bg-gray-400",
  },
  {
    id: 2,
    content: (
      <>
        <span className="font-semibold">Riceverai un promemoria entro 24h</span>{" "}
        con i dati della tua prenotazione.
      </>
    ),
    icon: CalendarDaysIcon,
    iconBackground: "bg-blue-500",
  },
  {
    id: 3,
    content: (
      <>
        Alla fine dell&apos;appuntamento, avrai{" "}
        <span className="font-semibold">
          conferma del preventivo richiesto.
        </span>
      </>
    ),
    icon: BanknotesIcon,
    iconBackground: "bg-green-500",
  },
]

export type FormSchemaType = InferOutput<typeof FormSchema>

const CompleteFormDialog: FC<Props> = ({ onClose }) => {
  const captchaRef = useRef<Captcha>(null)
  const [isShowModal, setShowModal] = useAtom(modalCheckoutAtom)
  const [formAtom, setFormAtom] = useAtom(formStateAtom)
  const [triggerOrderCreation, setTriggerOrderCreation] = useState(false)

  const [step, setStep] = useAtom(formStepAtom)

  const form = useForm<FormSchemaType>({
    resolver: valibotResolver(FormSchema),
    mode: "onSubmit",
  })

  const params = useParams()

  const { data } = useGetConsultantQuery(
    {
      urlKey: params?.professional[0],
    },
    {
      select: (data) => ({
        name: data?.byUrlKeyConsultant?.fullName,
        id: data.byUrlKeyConsultant?._id,
        reviews: data?.byUrlKeyConsultant?.reviews?.collection,
        avatar: data?.byUrlKeyConsultant?.avatar,
        offers: data?.byUrlKeyConsultant?.offers?.collection,
        services: data?.byUrlKeyConsultant?.offers?.collection?.flatMap(
          (item) => item?.consultingService ?? []
        ),
      }),
    }
  )

  const { mutate } = useCreateOrderMutation({
    onError: (error) => {
      toast.error("Errore nell'invio della richiesta, prova di nuovo.")
      console.error(error)
    },
    onSuccess: (data) => {
      sendGTMEvent({
        event: "generate_lead",
        purchase_currency: "€",
        purchase_value: servicePrice,
        purchase_transaction_id: data?.createOrder?.order?.id,
        purchase_items: [
          {
            item_id: data?.createOrder?.order?.service?.id,
            item_name: serviceName,
          },
        ],
      })
      if (typeof window !== "undefined" && window.fbq) {
        try {
          window.fbq("track", "Lead", {
            value: servicePrice,
            currency: "EUR",
            contents: [
              {
                id: data?.createOrder?.order?.service?.id,
                quantity: 1,
              },
            ],
          })
        } catch (e) {
          console.error(e)
        }
      }
      setStep("completed")
    },
  })

  const numberOfReviews = data?.reviews?.length ?? 0
  const accReviews =
    data?.reviews?.reduce((acc, item) => {
      return acc + (item?.rating ?? 0)
    }, 0) ?? 0

  const handleClose = () => {
    setShowModal(false)
    onClose?.()
    setTimeout(() => {
      setStep("form")
    }, 300)
  }

  const handleSubmit = async (e?: BaseSyntheticEvent<object, any, any> | undefined) => {
    e?.preventDefault();
    const captcha = await captchaRef.current?.executeAsync()
    const res = await fetch("/subscribe", {
      method: "POST",
      body: JSON.stringify({ captcha }),
      headers: { "Content-type": "application/json" },
    })

    if (res.status === 200) {
      const isValid = await form.trigger()
      if (isValid) {
        setFormAtom({
          ...formAtom,
          name: form.getValues("name"),
          surname: form.getValues("surname"),
          email: form.getValues("mail"),
          telephone: form.getValues("phone"),
          acceptedConditions: form.getValues("acceptedConditions"),
        })
        setTriggerOrderCreation(true)
      }
    } else {
      toast.error("Errore di CAPTCHA.")
    }
  }

  const handleCreateOrder = useCallback(() => {
    if (
      !formAtom.name ||
      !formAtom.surname ||
      !formAtom.email ||
      !formAtom.telephone ||
      !formAtom.acceptedConditions ||
      !formAtom.day ||
      !formAtom.time ||
      isEmpty(data) ||
      isUndefined(data?.id)
    ) {
      return
    }

    const date = DateTime.fromJSDate(formAtom.day)
      .set({
        hour: Number.parseInt(formAtom.time.isoTime.slice(1, 3)),
        minute: 0,
        second: 0,
      })
      .toISO({
        includeOffset: true,
        suppressMilliseconds: true,
      })

    const input = {
      customerName: formAtom.name,
      customerSurname: formAtom.surname,
      customerEmail: formAtom.email,
      customerTelephone: formAtom.telephone,
      scheduledAt: `${date ?? ""}`,
      scheduledAtNote: `${formAtom.time.time}`,
      consultant: data.id,
      service: formAtom.serviceId ?? 0,
    }

    mutate({
      input,
    })
  }, [data, formAtom, mutate])

  useEffect(() => {
    if (triggerOrderCreation) {
      handleCreateOrder()
      setTriggerOrderCreation(false)
    }
  }, [triggerOrderCreation, handleCreateOrder])

  const serviceName = useMemo(() => {
    return data?.services?.find((item) => item.urlKey === formAtom?.service)
      ?.name
  }, [data?.services, formAtom?.service])

  const servicePrice = useMemo(() => {
    return (
      data?.offers?.find(
        (item) => item?.consultingService?.name === serviceName
      )?.price ??
      data?.services?.find((item) => item.urlKey === formAtom?.service)?.price
    )
  }, [data, formAtom?.service, serviceName])

  // useEffect run only when complete modal is open

  const isTrackingSent = useRef(false)
  const previousServiceId = useRef<number | null>(null)

  useEffect(() => {
    if (step === "form" && isShowModal && !isTrackingSent.current) {
      sendGTMEvent({
        event: "begin_checkout",
        purchase_currency: "€",
        purchase_value: servicePrice,
        purchase_items: [
          {
            item_id: formAtom?.serviceId,
            item_name: serviceName,
          },
        ],
      })
      if (typeof window !== "undefined" && window.fbq) {
        try {
          window.fbq("track", "InitiateCheckout", {
            value: servicePrice,
            currency: "EUR",
            content_type: "product",
            content_ids: [formAtom?.serviceId],
          })
        } catch (e) {
          console.error(e)
        }
      }
      isTrackingSent.current = true
      previousServiceId.current = formAtom?.serviceId
    }
  }, [formAtom?.serviceId, isShowModal, serviceName, servicePrice, step])

  useEffect(() => {
    if (
      step !== "form" ||
      !isShowModal ||
      previousServiceId.current !== formAtom?.serviceId
    ) {
      isTrackingSent.current = false
    }
  }, [step, isShowModal, formAtom?.serviceId])

  return (
    <>
      <Dialog modal={true} open={isShowModal} onOpenChange={(props) => setShowModal(props.valueOf())}>
        <DialogContent
          className={cn(
            "absolute top-0 z-[100] lg:w-[200%] lg:-left-[150%] max-lg:w-full h-full overflow-y-auto rounded-2xl bg-white p-6 pr-10 -ml-2  text-left align-middle shadow-xl md:h-auto md:max-w-5xl",
            step === "completed" && "md:max-w-2xl"
          )}
          style={{ WebkitOverflowScrolling: "touch" }}
        >
          <div className="absolute right-0 top-0 mr-4 mt-4 z-[90]">
            <button
              type="button"
              className="flex h-10 w-10 items-center justify-center rounded-full bg-white hover:bg-neutral-100 focus:outline-none focus:ring-0"
              onClick={handleClose}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          {step === "form" && (
            <>
              <DialogTitle
                className="text-lg font-medium leading-6 text-gray-900"
              >
                Completa con i tuoi dati
              </DialogTitle>
              <DialogDescription className="mt-3 text-sm text-neutral-500 [text-wrap:balance] dark:text-neutral-400">
                Al momento della tua richiesta,{" "}
                <span className="font-semibold">
                  non ci sono costi e ti garantiamo un ricontatto senza
                  impegno.
                </span>{" "}
                Se in seguito trovi valore nel nostro servizio e
                desideri approfondire con una consulenza personalizzata
                a pagamento, potrai facilmente organizzarla con il
                nostro esperto. Lasciaci i tuoi dettagli qui sotto per
                iniziare.
              </DialogDescription>
              <Form
                {...form}
                onSubmit={(props) => handleSubmit(props.event)}
                className="mt-8 grid grid-cols-1 gap-x-6 gap-y-3 md:grid-cols-2"
              >
                <FormItem
                  label="Nome *"
                  visibleDesc={!!form.formState.errors.name}
                  descClassName="text-red-500"
                  desc={
                    form.formState.errors.name
                      ? form.formState.errors.name.message
                      : "Inserisci il tuo nome"
                  }
                >
                  <Input
                    placeholder="Nome"
                    {...form.register("name")}
                    isError={!!form?.formState?.errors?.name?.message}
                  />
                </FormItem>
                <FormItem
                  label="Cognome *"
                  desc="Inserisci il tuo cognome"
                  visibleDesc={!!form.formState.errors.surname}
                  descClassName="text-red-500"
                >
                  <Input
                    placeholder="Cognome"
                    isError={
                      !!form?.formState?.errors?.surname?.message
                    }
                    {...form.register("surname")}
                  />
                </FormItem>
                <FormItem
                  label="Email *"
                  visibleDesc={!!form.formState.errors.mail}
                  descClassName="text-red-500"
                  desc={
                    form.formState.errors.mail
                      ? form.formState.errors.mail.message
                      : "Inserisci la tua email"
                  }
                >
                  <Input
                    placeholder="Email"
                    {...form.register("mail")}
                    isError={!!form?.formState?.errors?.mail?.message}
                  />
                </FormItem>
                <FormItem
                  label="Telefono *"
                  desc="Inserisci il tuo numero di telefono"
                  descClassName="text-red-500"
                  visibleDesc={!!form.formState.errors.phone}
                >
                  <Input
                    placeholder="Telefono"
                    {...form.register("phone")}
                    isError={!!form?.formState?.errors?.phone?.message}
                  />
                </FormItem>
                <FormItem
                  label="Termini e condizioni *"
                  descClassName="text-red-500"
                  desc={
                    form.formState.errors.acceptedConditions
                      ? form.formState.errors.acceptedConditions.message
                      : "Accetta le condizioni"
                  }
                  visibleDesc={
                    !!form.formState.errors.acceptedConditions
                  }
                >
                  <Checkbox
                    {...form.register("acceptedConditions")}
                    label="Accetta i termini e condizioni"
                    LinkToAgreements={
                      <Link
                        target="_blank"
                        passHref
                        href={"/termini-e-condizioni"}
                      >
                        <span className="text-primary-500 dark:text-primary-400">
                          Termini e condizioni
                        </span>
                      </Link>
                    }
                  />
                </FormItem>
                <Captcha
                  ref={captchaRef}
                  size="invisible"
                  sitekey={process.env.NEXT_PUBLIC_RECAPTCHA!}
                />
                <ButtonPrimary onClick={handleSubmit} className="flex w-fit" >
                  Conferma Appuntamento
                </ButtonPrimary>
              </Form>
              <RecaptchaDisclaimer />
            </>
          )}
          {step === "completed" && (
            <div className="flex w-full flex-col space-y-6">
              <DialogTitle
                className="text-xl font-semibold lg:text-2xl"
              >
                Appuntamento confermato
              </DialogTitle>
              <div className="border-b border-neutral-200 dark:border-neutral-700" />
              {/* ------------------------ */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold leading-6 text-gray-900">
                  Il tuo consulente
                </h3>
                <div className="flex flex-col sm:flex-row sm:items-start">
                  <div className="w-fit flex-shrink-0">
                    <div className="rounded-2xl">
                      <Avatar
                        hasChecked
                        sizeClass="h-12 w-12"
                        imgUrl={data?.avatar}
                      />
                    </div>
                  </div>
                  <div className="space-y-3  sm:px-5 sm:pb-5">
                    <div>
                      <span className="mt-1 block text-base font-medium sm:text-lg">
                        {data?.name}
                      </span>
                    </div>
                    {numberOfReviews > 0 && (
                      <>
                        <div className="w-10 border-b border-neutral-200 dark:border-neutral-700" />
                        <StartRating
                          point={accReviews / numberOfReviews}
                          reviewCount={numberOfReviews}
                        />
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="space-y-6">
                <h3 className="text-lg font-semibold leading-6 text-gray-900">
                  Cosa succede ora?
                </h3>
                <div className="flex flex-col sm:flex-row sm:items-start">
                  <List listItems={lists} />
                </div>
              </div>
              <div className="mt-6 flex flex-col divide-y divide-neutral-200 rounded-3xl border border-neutral-200 dark:divide-neutral-700 dark:border-neutral-700 sm:flex-row sm:divide-x sm:divide-y-0">
                <div className="flex flex-1 space-x-4 p-5">
                  <svg
                    className="h-8 w-8 text-neutral-300 dark:text-neutral-6000"
                    viewBox="0 0 28 28"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <path
                      d="M9.33333 8.16667V3.5M18.6667 8.16667V3.5M8.16667 12.8333H19.8333M5.83333 24.5H22.1667C23.4553 24.5 24.5 23.4553 24.5 22.1667V8.16667C24.5 6.878 23.4553 5.83333 22.1667 5.83333H5.83333C4.54467 5.83333 3.5 6.878 3.5 8.16667V22.1667C3.5 23.4553 4.54467 24.5 5.83333 24.5Z"
                      stroke="#D1D5DB"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>

                  <div className="flex flex-col">
                    <span className="text-sm text-neutral-400">
                      Data e ora
                    </span>

                    <span className="mt-1.5 text-lg font-semibold capitalize">
                      {convertDateToItalianWithTime(
                        formAtom?.day,
                        formAtom?.time?.time
                      )}
                    </span>
                  </div>
                </div>
                <div className="flex flex-1 space-x-4 p-5">
                  <AcademicCapIcon className="h-8 w-8 text-neutral-300 dark:text-neutral-6000" />
                  <div className="flex flex-col">
                    <span className="text-sm text-neutral-400">
                      Servizio
                    </span>
                    <span className="mt-1.5 text-lg font-semibold">
                      {serviceName}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>

      </Dialog >
    </>
  )
}

export default CompleteFormDialog
