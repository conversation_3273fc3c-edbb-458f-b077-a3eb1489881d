"use client"

import { FC } from "react"
import { modalCheckoutAtom } from "@/store/modal"
import { useAtom } from "jotai"

import ButtonPrimary from "@/shared/ButtonPrimary"

type ConfirmButtonProps = {
  children: React.ReactNode
  className?: string
  type?: "submit" | "button"
  disabled?: boolean
}

const ConfirmButton: FC<ConfirmButtonProps> = ({
  children,
  className,
  type = "submit",
  disabled = false,
}) => {
  const [, setShowModal] = useAtom(modalCheckoutAtom)

  return (
    <ButtonPrimary
      type={type}
      className={className}
      onClick={() => setShowModal(true)}
      disabled={disabled}
    >
      {children}
    </ButtonPrimary>
  )
}

export default ConfirmButton
