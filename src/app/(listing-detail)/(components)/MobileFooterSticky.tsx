"use client"

import React from "react"

import ButtonPrimary from "@/shared/ButtonPrimary"

import ModalReservation from "./ModalReservation"

const MobileFooterSticky = () => {
  return (
    <div className="fixed inset-x-0 bottom-0 z-40 block border-t border-neutral-200 bg-white py-2 dark:border-neutral-6000 dark:bg-neutral-800 sm:py-3 lg:hidden">
      <div className="container flex items-center justify-between">
        <ModalReservation
          renderChildren={({ openModal }) => (
            <ButtonPrimary
              sizeClass="w-full py-3 !rounded-2xl"
              onClick={openModal}
            >
              Prenota una consulenza gratuita
            </ButtonPrimary>
          )}
        />
      </div>
    </div>
  )
}

export default MobileFooterSticky
