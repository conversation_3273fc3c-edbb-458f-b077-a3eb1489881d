import React, { ReactNode } from "react"

import CompleteFormDialog from "./(components)/CompleteFormDialog"
import MobileFooterSticky from "./(components)/MobileFooterSticky"

const DetailtLayout = ({ children }: { children: ReactNode }) => {
  return (
    <div className="ListingDetailPage">
      <div className="ListingDetailPage__content container mb-12">
        {children}
      </div>
      {/* STICKY FOOTER MOBILE */}
      <MobileFooterSticky />
    </div>
  )
}

export default DetailtLayout
