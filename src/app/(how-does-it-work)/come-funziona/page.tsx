import React, { FC } from "react"
import HIW4img from "@/images/HIW4.png"
import HIW5img from "@/images/HIW5.png"
import HIW6img from "@/images/HIW6.png"
import { ArrowRightIcon } from "@radix-ui/react-icons"

import ButtonPrimary from "@/shared/ButtonPrimary"
import Heading from "@/shared/Heading"
import BgGlassmorphism from "@/components/BgGlassmorphism"
import HeroWithMasonry from "@/components/HeroWithMasonry"
import SectionBecomeAProfessional from "@/components/SectionBecomeAProfessional"
import SectionClientSay from "@/components/SectionClientSay"
import SectionHowItWork from "@/components/SectionHowItWork"
import SectionOurFeatures from "@/components/SectionOurFeatures"
import FAQ from "@/app/(server-components)/FAQ"
import SectionHero3 from "@/app/(server-components)/SectionHero3"
import ClientImage1 from "@/images/9.png"
import ClientImage2 from "@/images/3.png"
import ClientImage3 from "@/images/8.png"

export interface PageAboutProps { }

const PageAbout: FC<PageAboutProps> = ({ }) => {
  return (
    <div className="pb-24 pt-14 sm:pt-20 lg:pb-32">
      <HeroWithMasonry />

      <div className={`nc-PageAbout container relative overflow-hidden`}>
        {/* ======== BG GLASS ======== */}
        <BgGlassmorphism />

        <div className="space-y-8 lg:space-y-16">
          {/* <SectionBecomeAProfessional
          title="Professionisti qualificati"
          desc="Trova il professionista più adatto alle tue esigenze e prenota un appuntamento online in pochi clic."
          btnText="Trova un professionista"
          btnHref="/"
        /> */}
          <SectionHowItWork
            className="lg:py-16"
            data={[
              {
                id: 1,
                img: HIW4img,
                title: "Utilizza il motore di ricerca",
                desc: "Cerca il professionista più adatto alle tue esigenze.",
              },
              {
                id: 2,
                img: HIW5img,
                title: "Prenota un appuntamento",
                desc: "Seleziona una data, un orario e il tipo di consulenza che desideri prenotare.",
              },
              {
                id: 3,
                img: HIW6img,
                title: "Ricevi la consulenza",
                desc: "Il professionista ti contatterà per discutere delle tue necessità.",
              },
            ]}
          />
          <div className="relative py-16">
            <SectionClientSay
              data={[
                {
                  id: 1,
                  clientName: "Claudio",
                  clientAddress: "Bologna",
                  content:
                    "Ci siamo affidati ad un avvocato con gli attributi come si dice da noi. Ci ha fatto vincere un contenzioso con i nostri vicini di casa che hanno ristrutturato casa ma che hanno arrecato gravissimi danni alle tubature collegate alla nostra abitazione. \nChe dire, grazie",
                  clientImage: ClientImage1
                },
                {
                  id: 2,
                  clientName: "Marco",
                  clientAddress: "Palermo",
                  content:
                    "Ottimo direi.\nMi sono interfacciato con un commercialista davvero bravo e disponibile.\nMi sta seguendo tutta la contabilità e mi informa trimestralmente su tutte le attività svolte.\nSoddisfatto!",
                  clientImage: ClientImage2
                },
                {
                  id: 3,
                  clientName: "Carolina",
                  clientAddress: "Genova",
                  content:
                    "Bene.\nSono riuscita a fare tutto online con il consulente senza neppure spostarmi.\nMolto comoda come cosa.",
                  clientImage: ClientImage3
                },
              ]}
            />
          </div>
          <div className="relative scroll-my-32 pt-16" id="faq">
            <Heading isCenter desc="Domande frequenti">
              FAQ
            </Heading>
            <FAQ
              type="multiple"
              faqItems={[
                {
                  questionName: "Quanto costa Consulente Ideale?",
                  acceptedAnswerText:
                    "Consulente Ideale è assolutamente gratis per i clienti; l’unica cosa che devi fare è pagare il prezzo del servizio al consulente senza alcun rincaro o aumento di prezzo.",
                },
                {
                  questionName: "Quanto si risparmia con Consulente Ideale?",
                  acceptedAnswerText:
                    "Con Consulente Ideale hai la possibilità di risparmiare fino al 40% sui servizi acquistati rispetto alla norma. Questo è possibile grazie ai numerosi sconti, offerte speciali e pacchetti speciali di risparmio proposti dai diversi consulenti su alcuni servizi offerti.",
                },
                {
                  questionName: "Sono indicati i prezzi di tutti i servizi?",
                  acceptedAnswerText:
                    "Assolutamente sì, per tutti i servizi offerti dai diversi consulenti è espressamente indicato il prezzo di vendita che ovviamente varia a seconda del consulente scelto.",
                },
                {
                  questionName: "Quali metodi di pagamento posso utilizzare?",
                  acceptedAnswerText:
                    "Puoi utilizzare diversi metodi di pagamento, quali: bonifico bancario, carta di credito, PayPal etc.",
                },
              ]}
            />
          </div>
          <div className="relative py-16">
            <SectionHero3
              title="Trova il professionista"
              subtitle="Comincia a utilizzare il nostro servizio per trovare il professionista più adatto alle tue esigenze"
              btnText="Cerca"
              href="/"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PageAbout
