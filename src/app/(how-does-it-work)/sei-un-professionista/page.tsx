import React, { <PERSON> } from "react"
import DiventaConsulenteCtaImg from "@/images/diventa-consulente-cta.png"
import DiventaConsulente from "@/images/diventa-consulente-2.png"
import HIW7 from "@/images/HIW7.png"
import HIW8 from "@/images/HIW8.png"
import HIW9 from "@/images/HIW9.png"

import Heading from "@/shared/Heading"
import BackgroundSection from "@/components/BackgroundSection"
import HeroWithSinglePhoto from "@/components/HeroWithSinglePhoto"
import SectionBecomeAProfessional from "@/components/SectionBecomeAProfessional"
import SectionClientSay from "@/components/SectionClientSay"
import SectionHowItWork from "@/components/SectionHowItWork"
import SectionOurFeatures from "@/components/SectionOurFeatures"
import FAQ from "@/app/(server-components)/FAQ"
import SectionHero3 from "@/app/(server-components)/SectionHero3"
import ClientImage1 from "@/images/16.png"
import ClientImage2 from "@/images/14.png"
import ClientImage3 from "@/images/10.png"

export interface PageAboutProps { }

const PageAbout: FC<PageAboutProps> = ({ }) => {
  return (
    <div className="space-y-8 lg:space-y-16">
      <HeroWithSinglePhoto />
      <div className="container space-y-8 lg:space-y-16">
        <SectionOurFeatures
          title="Perché registrarsi"
          benefits={[
            {
              badgeName: "Visibilità",
              title: "La tua attività al centro",
              description:
                "Alla tua visibilità ci pensiamo noi. Quando un cliente cerca un servizio in rete, Consulente Ideale è tra i primissimi risultati di ricerca, ed è per questo che raggiungiamo quotidianamente così tante imprese e persone fisiche.",
            },
            {
              badgeName: "Convenienza",
              badgeColor: "green",
              title: "Paghi solo quello che incassi",
              description:
                "Nessun costo per rispondere alla richiesta del servizio da parte del cliente. Consulente ideale richiede soltanto una piccolissima commissione sull'incasso.",
            },
            {
              badgeName: "Garanzia",
              badgeColor: "red",
              title: "Risultati garantiti",
              description:
                "Ogni mese, oltre 10.000 clienti si affidano a Consulente Ideale. Grazie a questi numeri e all’ampia rete di contatti che abbiamo costruito, possiamo garantire ai nostri clienti una maggiore visibilità, aumentando concretamente le opportunità di essere trovati e scelti.",
            },
          ]}
          rightImg={DiventaConsulente}
        />
        <SectionHowItWork
          desc="Tre semplici passi per entrare nella nostra community"
          data={[
            {
              id: 1,
              img: HIW7,
              title: "Compila il form di registrazione",
              desc: "Compila il form con tutti i tuoi dati e le informazioni necessarie per creare il tuo profilo.",
            },
            {
              id: 2,
              img: HIW8,
              title: "Aggiungi i tuoi servizi e le tue competenze",
              desc: "Completa il tuo profilo con le competenze che vuoi mettere a disposizione dei clienti.",
            },
            {
              id: 3,
              img: HIW9,
              title: "Accetta le richieste di consulenza",
              desc: "Visualizza e accetta le richieste di consulenza dei clienti in base alle tue disponibilità.",
            },
          ]}
        />
        <div className="relative py-16">
          <BackgroundSection className="bg-neutral-100 dark:bg-black dark:bg-opacity-20 " />
          <SectionClientSay
            desc="Leggi le recensioni dei professionisti iscritti a Consulente Ideale"
            data={[
              {
                id: 1,
                clientName: "Federico",
                clientAddress: "Roma",
                content:
                  "Ho aperto da poco la partita Iva come libero professionista e devo dire che il portale mi sta aiutando molto. Ho già evaso diverse richieste interessanti. Decisamente soddisfatto!",
                clientImage: ClientImage1
              },
              {
                id: 2,
                clientName: "Alessandro",
                clientAddress: "Firenze",
                content:
                  "Esperienza molto positiva.\nIn un anno circa abbiamo ricevuto un riscontro importante ricevendo svariate richieste.\nQualcuno ha peraltro provato a non pagare la causa portata avanti e si sono occupati loro di recuperarmi il credito con il cliente.\nOttimo servizio grazie!",
                clientImage: ClientImage2
              },
              {
                id: 3,
                clientName: "Francesca",
                clientAddress: "Milano",
                content:
                  "Network ottimo!\nAl momento dopo 4 mesi abbiamo ricevuto una decina di richieste anche molto proficue devo dire, pertanto sicuramente rinnoveremo.\nMolto utili i corsi online di aggiornamento gratuiti.",
                clientImage: ClientImage3
              },
            ]}
          />
        </div>
        <div className="relative py-16">
          <SectionHero3
            title="Diventa un consulente ideale"
            subtitle="Entra a far parte della nostra community di professionisti"
            btnText="Registrati ora"
            href="/auth/registrazione"
            imageSrc={DiventaConsulenteCtaImg}
          />
        </div>
        <div className="relative scroll-my-32 pt-16" id="faq">
          <Heading isCenter desc="Domande frequenti">
            FAQ
          </Heading>
          <FAQ
            type="multiple"
            collapsible
            faqItems={[
              {
                questionName: "Quanto costa Consulente Ideale?",
                acceptedAnswerText:
                  "Consulente Ideale prevede un costo di iscrizione pari a 150,00 euro annuali e successivamente, su ogni servizio venduto, percepisce il 15% di commissione sull'incasso. Ciò vuol dire che paghi solo se effettivamente hai ricevuto il pagamento da parte del cliente.",
              },
              {
                questionName:
                  "Devo indicare i costi di tutti i servizi che offro?",
                acceptedAnswerText:
                  "Sì, devono essere indicati, fatta eccezione per alcuni servizi per i quali è richiesta una consulenza informativa per valutare il caso e determinare il prezzo. In tal senso al momento della registrazione vi verrà trasmesso un file all’interno del quale sono indicati tutti i servizi per i quali dovete inserire il prezzo di vendita e quelli che invece necessitano di una consulenza informativa pre-acquisto.",
              },
              {
                questionName: "Posso rifiutare un ordine?",
                acceptedAnswerText:
                  "Assolutamente sì, hai la facoltà di annullare gli ordini ricevuti anche senza motivata causa; tuttavia, nel caso in cui il cliente ha già effettuato il pagamento, sei tenuto al rimborso dell'intera somma ricevuta.",
              },
              {
                questionName: "Come faccio a pagare?",
                acceptedAnswerText:
                  "Facilissimo, per quanto riguarda il canone annuale di iscrizione di 150,00 euro puoi pagare direttamente dalla tua area personale riservata, cliccando sulla voce “Iscrizione”. Una volta cliccato sulla voce non ti resta altro che procedere con il pagamento, che può essere effettuato attraverso: carta di credito o di debito, bonifico bancario, PayPal e altri metodi simili. Per quanto riguarda invece la piccolissima commissione del 15%, viene direttamente detratta al momento del pagamento del cliente.",
              },
            ]}
          />
        </div>
      </div>
    </div>
  )
}

export default PageAbout
