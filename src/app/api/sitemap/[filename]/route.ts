import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";
export const revalidate = 0;
export const fetchCache = 'force-no-store';
export const preferredRegion = 'auto';

export async function GET(request: Request, { params }: { params: { filename: string } }) {
    const backend = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backend) {
        console.error('Missing NEXT_PUBLIC_BACKEND_URL environment variable');
        return NextResponse.json({ error: "Missing NEXT_PUBLIC_BACKEND_URL" }, { status: 500 });
    }

    // Extract the actual filename from the request URL or params
    // This handles both the rewrite case and direct access to the API route
    let filename = params.filename;

    // Get the full URL path to help with debugging
    const url = new URL(request.url);
    console.log(`Request URL path: ${url.pathname}`);
    console.log(`Extracted filename parameter: ${filename}`);

    // Ensure we have the correct filename for the backend request
    // Check if the filename has been properly passed or needs reconstruction
    if (filename.includes('sitemap-') && !filename.startsWith('sitemap-')) {
        // Extract just the sitemap part if we got something like 'sitemap/sitemap-1.xml'
        const match = filename.match(/sitemap-(.*)\.xml/);
        if (match && match[1]) {
            filename = `sitemap-${match[1]}.xml`;
        }
    } else if (!filename.includes('sitemap')) {
        // If we just got a number or text without 'sitemap' prefix
        filename = `sitemap-${filename}`;
        if (!filename.endsWith('.xml')) {
            filename = `${filename}.xml`;
        }
    } else if (!filename.endsWith('.xml')) {
        filename = `${filename}.xml`;
    }

    console.log(`Normalized filename: ${filename}`);

    // Ensure correct backend URL formatting
    const backendUrl = `${backend.replace(/\/$/, "")}/${filename}`;

    console.log(`Fetching sitemap from: ${backendUrl}`);

    try {
        const res = await fetch(backendUrl, {
            cache: 'no-store',
            next: { revalidate: 0 },
            headers: {
                'Accept': 'application/xml',
                'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!res.ok) {
            console.error(`Backend returned ${res.status} for ${filename}`);
            return NextResponse.json({
                error: `Backend returned ${res.status} for ${filename}`,
                url: backendUrl
            }, { status: 502 });
        }

        const xml = await res.text();
        console.log(`Successfully fetched sitemap: ${filename}`);

        return new NextResponse(xml, {
            status: 200,
            headers: {
                "Content-Type": "application/xml; charset=utf-8",
                "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
                "Surrogate-Control": "no-store"
            },
        });
    } catch (error) {
        console.error(`Error fetching sitemap ${filename}:`, error);
        return NextResponse.json({ 
            error: `Failed to fetch sitemap: ${error instanceof Error ? error.message : 'Unknown error'}`,
            url: backendUrl
        }, { status: 500 });
    }
}
