import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function POST(req: NextRequest) {
    if (req.method !== 'POST') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
    }
    const { amount, customerId, paymentMethodId, appointmentId } = await req.json();
    try {
        // Create a payment intent
        const paymentIntent = await stripeClient.paymentIntents.create({
            amount: amount * 100, // amount in cents
            currency: 'eur',
            customer: customerId, // Use customer ID from your saved account info
            payment_method: paymentMethodId, // Example payment method id, replace it with saved one
            off_session: true,
            confirm: true,
            metadata: {
                appointmentId
            }
        });
        return NextResponse.json({ success: true, paymentIntent }, { status: 200 });
    } catch (error: any) {
        return NextResponse.json({ success: false, error: error.message }, { status: 500 });
    }

};
