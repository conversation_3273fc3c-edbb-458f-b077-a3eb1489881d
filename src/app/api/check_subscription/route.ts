import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function POST(req: NextRequest) {
    if (req.method !== 'POST') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
    }
    try {
        const { subscriptionId } = await req.json();
        const subscription = await stripeClient.subscriptions.retrieve(subscriptionId);

        return NextResponse.json({ isValid: true, subscription }, { status: 200 });
    } catch (error) {
        console.error(error);
        return NextResponse.json({ error: 'An error occurred while checking the subscription validity.' }, { status: 500 });
    }
};
