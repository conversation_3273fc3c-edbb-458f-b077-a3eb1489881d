import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function GET(req: NextRequest) {
    if (req.method !== 'GET') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
    }
    try {
        const customerId = req.nextUrl.searchParams.get("customerId");

        const session = await stripeClient.billingPortal.sessions.create({
            customer: customerId as string,
            return_url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/account/subscription`,
        });

        return NextResponse.json({ url: session.url })
    } catch (error) {
        console.error(error);
        return NextResponse.json({ error: 'An error occurred while creating the billing portal.' }, { status: 500 });
    }

};
