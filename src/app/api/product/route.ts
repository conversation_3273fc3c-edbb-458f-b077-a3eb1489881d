import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function POST(req: NextRequest) {
    const { productType } = await req.json();
    const productId = productType === "basic" ? process.env.STRIPE_PRODUCT_ID! : process.env.STRIPE_PARTNER_PRODUCT_ID!

    try {
        const product = await stripeClient.products.retrieve(productId);

        const price = await stripeClient.prices.retrieve(product.default_price as string);

        return NextResponse.json({ product, price }, { headers: { 'Cache-Control': 'no-cache' } });
    } catch (error) {
        console.error(error);
        return NextResponse.json({ error: 'An error occurred while fetching the product.' }, { status: 500 });
    }
};

export const revalidate = 0;
