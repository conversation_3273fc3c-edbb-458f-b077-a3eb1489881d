import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey);

export async function POST(req: NextRequest) {
    try {
        const { priceId } = await req.json(); // Replace with your new price ID
        let hasMore = true;
        let startingAfter: string | null = null;

        while (hasMore) {
            const subscriptions: stripe.ApiList<stripe.Subscription> = await stripeClient.subscriptions.list({
                status: 'active',
                limit: 100,
                price: "price_1PEYbOIMru0Tu1TIcpiZ1k7q",
                starting_after: startingAfter || undefined
            });

            for (const subscription of subscriptions.data) {
                const subscriptionItemId = subscription.items.data[0].id; // Assuming one item per subscription
                console.log(subscriptionItemId)
                await stripeClient.subscriptionItems.update(subscriptionItemId, {
                    price: priceId,
                    proration_behavior: 'none', // Adjust prorations as needed
                });
            }

            hasMore = subscriptions.has_more;
            if (hasMore) {
                startingAfter = subscriptions.data[subscriptions.data.length - 1].id;
            }
        }

        return NextResponse.json({ success: true, message: 'Subscriptions updated successfully.' });
    } catch (error: any) {
        console.error('Error updating subscriptions:', error.message);
        return NextResponse.json({ success: false, error: error.message }, { status: 500 });
    }
}
