import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function POST(req: NextRequest) {
    if (req.method !== 'POST') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
    }
    try {
        const { priceId, isPartnerPlanIncluded, email } = await req.json();
        if (priceId) {

            const session = await stripeClient.checkout.sessions.create({
                payment_method_types: ['card'],
                line_items: [{ price: priceId, quantity: 1 }],
                mode: 'subscription',
                success_url:
                    isPartnerPlanIncluded ?
                        `${process.env.NEXT_PUBLIC_WEBSITE_URL}/partner-plan?sessionId={CHECKOUT_SESSION_ID}` :
                        `${process.env.NEXT_PUBLIC_WEBSITE_URL}/account/subscription?sessionId={CHECKOUT_SESSION_ID}`,
                cancel_url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/account/subscription`,
                customer_email: email
            });
            return NextResponse.json({ id: session.id }, { status: 200 });
        }
        else {
            return NextResponse.json({ error: 'Could not create the checkout sesion: No priceId provided' }, { status: 500 });
        }

    } catch (error) {
        console.error(error);
        return NextResponse.json({ error: 'An error occurred while creating the checkout session.' }, { status: 500 });
    }

};
