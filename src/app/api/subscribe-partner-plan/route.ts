import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function POST(req: NextRequest) {
    const { customerId, priceId, paymentMethodId } = await req.json();

    try {
        const subscription = await stripeClient.subscriptions.create({
            customer: customerId,
            items: [{ price: priceId }], // Price ID of the subscription plan
            default_payment_method: paymentMethodId, // Use the existing payment method
            expand: ['latest_invoice.payment_intent'], // Expand to include payment intent
        });

        return NextResponse.json(subscription, { headers: { 'Cache-Control': 'no-cache' } });
    } catch (error) {
        console.error(error);
        return NextResponse.json({ error: 'An error occurred while fetching the product.' }, { status: 500 });
    }
};

export const revalidate = 0;
