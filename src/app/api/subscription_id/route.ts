import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey);

export async function GET(req: NextRequest) {
    if (req.method !== 'GET') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
    }

    const sessionId = req.nextUrl.searchParams.get('sessionId');

    try {
        const session = await stripeClient.checkout.sessions.retrieve(sessionId as string);
        return NextResponse.json({ subscriptionId: session.subscription }, { status: 200 });
    } catch (error) {
        return NextResponse.json({ error: 'An error occurred while retrieving the subscription id.' }, { status: 500 });
    }
}
