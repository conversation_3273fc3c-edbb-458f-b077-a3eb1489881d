import { NextRequest, NextResponse } from 'next/server';
import stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
const stripeClient = new stripe(stripeSecretKey, {
    apiVersion: '2024-04-10',
});

export async function POST(req: NextRequest) {
    if (req.method !== 'POST') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
    }
    const { email } = await req.json();
    try {
        // Find customers by email
        const customers = await stripeClient.customers.list({
            email: email,
        });

        if (customers.data.length === 0) {
            return NextResponse.json({ success: false, error: 'Customer not found' }, { status: 404 });
        }

        const customer = customers.data[0]; // Assuming you want the first customer match

        // Retrieve payment methods for the found customer
        const paymentMethods = await stripeClient.paymentMethods.list({
            customer: customer.id,
            // type: 'card', // Specify the type of payment method
        });

        // Return customer ID and payment method IDs
        return NextResponse.json(
            {
                success: true,
                customerId: customer.id,
                paymentMethodIds: paymentMethods.data.map(pm => pm.id), // Return an array of payment method IDs
            },
            { status: 200 }
        );
    } catch (error: any) {
        return NextResponse.json({ success: false, error: error.message }, { status: 500 });
    }

};
