import { cache } from "react"
import {
  QueryClient,
  Query<PERSON><PERSON>,
  UseInfiniteQueryOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query"

type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K]
}

interface QueryWithKey<TData = any, TVariables = any> {
  (
    variables?: TVariables,
    options?: Omit<UseQueryOptions<TData, unknown, TData>, "queryKey"> & {
      queryKey?: UseQueryOptions<TData, unknown, TData>["queryKey"]
    }
  ): UseQueryResult<TData, unknown>
  getKey: (variables?: any) => QueryKey
  fetcher: (variables?: any, options?: any) => () => Promise<TData>
}

type QueryParams<TQuery extends QueryWithKey> = Parameters<TQuery>[0]

const hasVariablesTypeGuard = <TQuery extends QueryWithKey>(
  variables?: ServerFetchOptions<TQuery>
): variables is {
  variables: QueryParams<TQuery>[0]
  next?: NextFetchRequestConfig
} => !!Object.keys(variables || {}).length

export type ServerFetchOptions<
  TQuery extends QueryWithKey,
  TSelectedData = any,
> = {
  variables?: Parameters<TQuery>[0]
  next?: NextFetchRequestConfig
  cache?: RequestCache
  select?: (
    data: Awaited<ReturnType<ReturnType<TQuery["fetcher"]>>>
  ) => TSelectedData
}

type FetcherReturnValue<TQuery extends QueryWithKey> = Awaited<
  ReturnType<ReturnType<TQuery["fetcher"]>>
>

const cachedServerPreFetch = cache(
  async <TQuery extends QueryWithKey>(
    useQuery: TQuery,
    stringifiedQueryOptions?: string
  ) => {
    const queryOptions = stringifiedQueryOptions
      ? JSON.parse(stringifiedQueryOptions)
      : undefined
    const hasVariables = hasVariablesTypeGuard<TQuery>(queryOptions)
    let variables: QueryParams<TQuery> | undefined
    if (hasVariables) {
      variables = queryOptions.variables
    }
    const queryClient = new QueryClient()
    await queryClient.prefetchQuery({
      queryKey: useQuery.getKey(variables),
      queryFn: useQuery.fetcher(variables, {
        next: queryOptions?.next,
        cache: queryOptions?.cache,
      }),
    })
    return queryClient
  }
)

export const serverPreFetch = async <TQuery extends QueryWithKey>(
  useQuery: TQuery,
  queryOptions?: ServerFetchOptions<TQuery>
) => {
  let stringifiedQueryOptions: string | undefined
  if (queryOptions) stringifiedQueryOptions = JSON.stringify(queryOptions)
  return cachedServerPreFetch(useQuery, stringifiedQueryOptions)
}

const cachedServerFetch = cache(
  async <TQuery extends QueryWithKey<any, any>, TSelectedData>(
    useQuery: TQuery,
    stringifiedQueryOptions?: string,
    select?: (
      data: Awaited<ReturnType<ReturnType<TQuery["fetcher"]>>>
    ) => TSelectedData
  ) => {
    const queryOptions = stringifiedQueryOptions
      ? JSON.parse(stringifiedQueryOptions)
      : undefined

    const hasVariables = hasVariablesTypeGuard<TQuery>(queryOptions)
    let variables: QueryParams<TQuery> | undefined
    if (hasVariables) {
      variables = queryOptions.variables
    }

    const queryClient = new QueryClient()
    const data = await queryClient.fetchQuery<
      Awaited<ReturnType<ReturnType<TQuery["fetcher"]>>>,
      QueryParams<TQuery>
    >({
      queryKey: useQuery.getKey(variables),
      queryFn: useQuery.fetcher(variables, {
        next: queryOptions?.next,
        cache: queryOptions?.cache,
      }),
    })

    if (select) {
      return select(data)
    } else {
      return data as unknown as TSelectedData
    }
  }
)

export const serverFetch = async <
  TQuery extends QueryWithKey<any, any>,
  TSelectedData,
>(
  useQuery: TQuery,
  queryOptions?: ServerFetchOptions<TQuery, TSelectedData>
) => {
  let stringifiedQueryOptions: string | undefined
  let selectFunction = queryOptions?.select

  if (queryOptions) {
    const { select, ...otherOptions } = queryOptions
    stringifiedQueryOptions = JSON.stringify(otherOptions)
  }

  return cachedServerFetch<TQuery, TSelectedData>(
    useQuery,
    stringifiedQueryOptions,
    selectFunction
  )
}

interface InfiniteQueryWithKey<TData = any, TVariables = any> {
  (
    variables?: TVariables,
    options?: Omit<
      UseInfiniteQueryOptions<TData, unknown, TData, TData, QueryKey>,
      "queryKey"
    > & {
      queryKey?: UseInfiniteQueryOptions<
        TData,
        unknown,
        TData,
        TData,
        QueryKey
      >["queryKey"]
    }
  ): any // Replace with the correct return type
  getKey: (variables?: TVariables) => QueryKey
  fetcher: (variables?: TVariables, options?: any) => Promise<TData>
}

export const serverPreFetchInfinite = async <
  TQuery extends InfiniteQueryWithKey,
>(
  wrappedUseQuery: TQuery,
  queryOptions?: any
) => {
  const queryClient = new QueryClient()
  await queryClient.prefetchInfiniteQuery({
    queryKey: wrappedUseQuery.getKey(queryOptions?.variables),
    queryFn: () => wrappedUseQuery.fetcher(queryOptions?.variables),
    initialPageParam: 0,
  })
  return queryClient
}
