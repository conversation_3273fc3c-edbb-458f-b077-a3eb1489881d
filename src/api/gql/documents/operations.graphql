# Write your query or mutation here
query getConsultingServices($serviceUrlKey: String) {
  consultingServices(offers_consultingService_urlKey: $serviceUrlKey, itemsPerPage: 10000) {
    collection {
      name
      urlKey
      price
      id
      _id
      description
      consultingServiceCategories{
        collection{
          id
          _id
          name
          urlKey
          description
          isFeatured
          image
        }
        paginationInfo {
          itemsPerPage
          lastPage
          totalCount
          hasNextPage
        }
      }
    }
  }
}

query getConsultingServicesForLinks($serviceUrlKey: String) {
  consultingServices(offers_consultingService_urlKey: $serviceUrlKey, itemsPerPage: 10000) {
    collection {
      name
      urlKey
      _id
    }
  }
}



query getConsultingCategories {
  consultingServiceCategories(itemsPerPage: 10000) {
    collection {
      name
      urlKey
      id
      description
    }
  }
}

query getCities($name: String) {
  cities(name: $name, itemsPerPage: 999999) {
    collection {
      name
      urlKey
      id
    }
  }
}

query getFilteredConsultants(
  $city: String,
  $cityId: Int,
  $cityName: String,
  $service: Int,
  $serviceUrlKey: String,
  $serviceCategoryUrlKey: String,
  $professionsUrlKey: String,
  $page: Int,
  $itemsPerPage: Int,
  $isOfferFeatured: Boolean,
  $isRemote: Boolean,
  $isFeatured: Boolean,
  $tags: [Int]) {
  listConsultants(
    city_id: $cityId
    city: $city
    city_name: $cityName
    offers_consultingService_id: $service
    offers_consultingService_urlKey: $serviceUrlKey
    offers_consultingService_consultingServiceCategories_urlKey: $serviceCategoryUrlKey
    professions_urlKey: $professionsUrlKey
    page: $page
    itemsPerPage: $itemsPerPage
    isRemote: $isRemote
    isFeatured: $isFeatured
    order: { position : "ASC" }
    tags_id_list: $tags,
  ) {
    collection {
      _id
      fullName
      avatar
      urlKey
      reviews {
        collection {
          createdAt
          rating
          createdAt
          description
        }
      }
      city {
        name
      }
      tags {
        collection {
          name
          _id
        }
      }
      offers(isFeatured: $isOfferFeatured, itemsPerPage: 250) {
        collection {
          price
          consultingService {
            name
            price
            urlKey
            _id
            consultingServiceCategories{
              collection{
                id
                _id
                name
                urlKey
                description
                isFeatured
                image
              }
            }
          }
        }
      }
      professions {
        collection {
          id
          _id
          name
          urlKey
        }
      }
    }
    paginationInfo {
      totalCount
      lastPage
      itemsPerPage
    }
  }
}

query getConsultant($urlKey: String!) {
  byUrlKeyConsultant(urlKey: $urlKey) {
    fullName
    avatar
    urlKey
    bio
    _id
    reviews {
      collection {
        rating
        createdAt
        description
        authorName
      }
    }
    city {
      name
    }
    tags {
      collection {
        name
      }
    }
    offers(itemsPerPage: 10000) {
      collection {
        price
        id
        consultingService {
          _id
          name
          urlKey
          price
          consultingServiceCategories{
            collection{
              id
              _id
              name
              urlKey
              description
              image
              isFeatured
            }
          }
        }
      }
    }
    professions {
      collection {
        id
        _id
        name
        urlKey
      }
    }
  }
}

query getConsultantByToken {
  byTokenConsultant {
    fullName
    email
    avatar
    urlKey
    _id
    id
    bio
    isRemote
    dateOfBirth
    fiscalCode
    taxId
    telephone
    city {
      name
      id
    }
    reviews {
      collection {
        rating
        createdAt
        description
        authorName
      }
    }
    tags {
      collection {
        name
        _id
      }
    }
    offers(itemsPerPage: 10000) {
      collection {
        price
        id
        consultingService {
          _id
          id
          name
          urlKey
          price
          consultingServiceCategories{
            collection{
              id
              _id
              name
              urlKey
              description
              isFeatured
              image
            }
          }
        }
      }
    }
    stripeSubscriptionId
    stripePartnerPlanSubscriptionId
    professions{
      collection{
        name
        urlKey
        id
        isPublished
        description
        image
        isFeatured
      }
    }
  }
}

mutation createOrder($input: createOrderInput!) {
  createOrder(input: $input) {
    order {
      id
      consultant {
        id
      }
      price
      service {
        id
      }
    }
    clientMutationId
  }
}

query getTopTenConsultants($page: Int, $itemsPerPage: Int) {
  listConsultants(
    page: $page
    itemsPerPage: $itemsPerPage
    order: { isFeatured: "DESC",position: "ASC"}
  ) {
    collection {
      fullName
      avatar
      urlKey
      city {
        name
      }
      reviews {
        collection {
          rating
        }
      }
    }
  }
}

query getConsultantProfessions {
  consultantProfessions {
    collection {
      id
      _id
      isPublished
      isFeatured
      name
      urlKey
      image
      description
    }
  }
}

query getConsultantProfessionsAll {
  consultantProfessions(itemsPerPage: 1000) {
    collection {
      id
      _id
      isPublished
      name
      urlKey
    }
  }
}

query getFeaturedConsultingServiceCategories {
  consultingServiceCategories(isFeatured: true) {
    collection {
      id
      name
      image
      isFeatured
      urlKey
    }
    paginationInfo {
      totalCount
    }
  }
}

query getFeaturedConsultantProfessions {
  consultantProfessions(isFeatured: true) {
    collection {
      id
      name
      isFeatured
      urlKey
      image
    }
    paginationInfo {
      totalCount
    }
  }
}

query getFeaturedConsultingServices {
  consultingServices(isFeatured: true) {
    collection {
      id
      name
      isFeatured
      urlKey
      image
    }
    paginationInfo {
      totalCount
    }
  }
}

mutation createContactRequest($input: createContactRequestInput!) {
  createContactRequest(input: $input) {
    contactRequest {
      id
    }
  }
}

mutation createToAdminContactRequest(
  $input: createToAdminContactRequestInput!
) {
  createToAdminContactRequest(input: $input) {
    clientMutationId
  }
}

mutation createConsultant($input: createConsultantInput!) {
  createConsultant(input: $input) {
    consultant {
      fullName
    }
  }
}

mutation loginConsultant($input: loginConsultantAuthInput!) {
  loginConsultantAuth(input: $input) {
    consultantAuth {
      token
      id
    }
  }
}

mutation changePasswordByTokenConsultant(
  $input: changePasswordByTokenConsultantInput!
) {
  changePasswordByTokenConsultant(input: $input) {
    consultant {
      id
    }
  }
}

query getTags {
  tags {
    collection {
      name
      _id
    }
  }
}

mutation assignTagsToConsultant($input: assignTagsToConsultantInput!) {
  assignTagsToConsultant(input: $input) {
    consultant {
      id
    }
  }
}

mutation assignOffersToConsultant($input: assignOffersToConsultantInput!) {
  assignOffersToConsultant(input: $input) {
    consultant {
      id
    }
  }
}

mutation updateConsultant($input: updateConsultantInput!) {
  updateConsultant(input: $input) {
    consultant {
      id
    }
  }
}

query getAppointments {
  myAppointments {
    collection {
      id
      scheduledAt
      customer {
        telephone
        email
        fullName
      }
      customerOrder {
        price
        service {
          name
        }
      }
      paymentStatus
    }
  }
}

query getAgreements {
  agreements {
    collection {
      id
      content
    }
  }
}

query getTitleFromPath($id: ID!) {
  url(id: $id) {
    title
    bodyContent
  }
}

mutation createConsultantReview($appointmentHash: String!, $description: String!, $rating:Int!) {
	createConsultantReview(input:
    {
      appointmentHash: $appointmentHash,
      description: $description, 
      rating: $rating
    }
  ) {
		consultantReview {
			id
		}
	}
}

mutation validateByAppointmentHashConsultantReview($appointmentHash: String!){
  validateByAppointmentHashConsultantReview(input: {appointmentHash : $appointmentHash}) {
    consultantReview {
      id
    }
  }
}

query settingByCodeSetting {
  settingByCodeSetting(code:"consultantCommissionAmount") {
    code
    value
  }
}

query contents(
  $page: Int,
  $itemsPerPage: Int,
  $consultantProfession_name: String,
  $consultantProfession_name_list: [String],
  $consultantProfession_id: Int,
  $consultantProfession_id_list: [Int],
  $consultantService_id: Int,
  $consultantService_id_list: [Int],
  $city_name: String,
  $city_name_list: [String],
  $city: String,
  $city_list: [String],
  $city_id: Int,
  $city_id_list: [Int]
  $consultantService_urlKey: String
  $consultantService_urlKey_list: [String]
  $consultantProfession_urlKey: String
  $consultantProfession_urlKey_list: [String]
){
  contents(
   	page: $page
    itemsPerPage: $itemsPerPage
    consultantProfession_name:  $consultantProfession_name
    consultantProfession_name_list: $consultantProfession_name_list
    consultantProfession_id: $consultantProfession_id
    consultantProfession_id_list: $consultantProfession_id_list
    consultantService_id: $consultantService_id
    consultantService_id_list: $consultantService_id_list
    city_name: $city_name
    city_name_list: $city_name_list
    city: $city
    city_list: $city_list
    city_id: $city_id
    city_id_list: $city_id_list
    consultantService_urlKey:$consultantService_urlKey
    consultantService_urlKey_list:$consultantService_urlKey_list
    consultantProfession_urlKey:$consultantProfession_urlKey
    consultantProfession_urlKey_list:$consultantProfession_urlKey_list
  ){
    collection {
      id
      _id
      createdAt
      updatedAt
      lastUpdateByAIAt
      title
      body
      city {
        id
        name
        urlKey
      }
      consultantProfession {
        id
        _id
        name
        urlKey
        isPublished
        description
        image
        isFeatured
      }
      consultantService {
        id
        _id
        name
        price
        urlKey
        isFeatured
        description
        image
      }
      faqHTML
      metadataTitle 
      metadataDescription
    }
    paginationInfo {
      itemsPerPage
      lastPage
      totalCount
      hasNextPage
    }
  }
}
query ratingsForServices($serviceId: Int!) {
  statsByServiceIdServiceReviewStatistic(serviceId: $serviceId) {
    averageRating
    reviewCount
    bestRating
    worstRating
    service {
      name
    }
  }
}

query GetConsultingServiceIdByUrlKey($urlKey: String!) {
  consultingServices(
    offers_consultingService_urlKey: $urlKey,
    itemsPerPage: 1
  ) {
    collection {
      _id
    }
  }
}

