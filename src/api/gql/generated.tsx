import {
  InfiniteData,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
  useMutation,
  UseMutationOptions,
  useQuery,
  UseQueryOptions,
  useSuspenseInfiniteQuery,
  UseSuspenseInfiniteQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryOptions,
} from "@tanstack/react-query"

import { fetcher } from "./fetcher"

export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K]
}
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>
}
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>
}
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never }
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never
    }

type FetchOptions = {
  cache?: RequestCache
  next?: NextFetchRequestConfig
}

type RequestInit = {
  headers: (HeadersInit & FetchOptions) | FetchOptions
}
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string }
  String: { input: string; output: string }
  Boolean: { input: boolean; output: boolean }
  Int: { input: number; output: number }
  Float: { input: number; output: number }
  Iterable: { input: any; output: any }
}

/** Fetches the agreement details */
export type Agreement = Node & {
  __typename?: "Agreement"
  _id: Scalars["Int"]["output"]
  content: Scalars["String"]["output"]
  createdAt: Scalars["String"]["output"]
  id: Scalars["ID"]["output"]
  updatedAt: Scalars["String"]["output"]
}

/** Page connection for Agreement. */
export type AgreementPageConnection = {
  __typename?: "AgreementPageConnection"
  collection?: Maybe<Array<Maybe<Agreement>>>
  paginationInfo: AgreementPaginationInfo
}

/** Information about the pagination. */
export type AgreementPaginationInfo = {
  __typename?: "AgreementPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Appointment = Node & {
  __typename?: "Appointment"
  _id: Scalars["Int"]["output"]
  consultant?: Maybe<Consultant>
  createdAt: Scalars["String"]["output"]
  customer?: Maybe<Customer>
  customerOrder?: Maybe<Order>
  id: Scalars["ID"]["output"]
  modifiedAt: Scalars["String"]["output"]
  note?: Maybe<Scalars["String"]["output"]>
  paymentStatus?: Maybe<AppointmentPaymentStatus>
  scheduledAt?: Maybe<Scalars["String"]["output"]>
  secretHash?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for Appointment. */
export type AppointmentPageConnection = {
  __typename?: "AppointmentPageConnection"
  collection?: Maybe<Array<Maybe<Appointment>>>
  paginationInfo: AppointmentPaginationInfo
}

/** Information about the pagination. */
export type AppointmentPaginationInfo = {
  __typename?: "AppointmentPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export enum AppointmentPaymentStatus {
  Paid = "PAID",
  Unpaid = "UNPAID",
}

export type City = Node & {
  __typename?: "City"
  id: Scalars["ID"]["output"]
  name: Scalars["String"]["output"]
  urlKey?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for City. */
export type CityPageConnection = {
  __typename?: "CityPageConnection"
  collection?: Maybe<Array<Maybe<City>>>
  paginationInfo: CityPaginationInfo
}

/** Information about the pagination. */
export type CityPaginationInfo = {
  __typename?: "CityPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Consultant = Node & {
  __typename?: "Consultant"
  _id: Scalars["Int"]["output"]
  active?: Maybe<Scalars["Boolean"]["output"]>
  appointments?: Maybe<AppointmentPageConnection>
  avatar?: Maybe<Scalars["String"]["output"]>
  bio?: Maybe<Scalars["String"]["output"]>
  city?: Maybe<City>
  contactRequests?: Maybe<ContactRequestPageConnection>
  createdAt: Scalars["String"]["output"]
  dateOfBirth?: Maybe<Scalars["String"]["output"]>
  email: Scalars["String"]["output"]
  fiscalCode?: Maybe<Scalars["String"]["output"]>
  fullName: Scalars["String"]["output"]
  id: Scalars["ID"]["output"]
  industries?: Maybe<Scalars["String"]["output"]>
  isFeatured?: Maybe<Scalars["Boolean"]["output"]>
  isRemote?: Maybe<Scalars["Boolean"]["output"]>
  knowledgeAndCertifications?: Maybe<Scalars["String"]["output"]>
  modifiedAt: Scalars["String"]["output"]
  offers?: Maybe<OfferPageConnection>
  orders?: Maybe<OrderPageConnection>
  password?: Maybe<Scalars["String"]["output"]>
  position: Scalars["Int"]["output"]
  professions?: Maybe<ConsultantProfessionPageConnection>
  reviews?: Maybe<ConsultantReviewPageConnection>
  roles: Scalars["Iterable"]["output"]
  specializations?: Maybe<Scalars["String"]["output"]>
  stripePartnerPlanSubscriptionId?: Maybe<Scalars["String"]["output"]>
  stripeSubscriptionId?: Maybe<Scalars["String"]["output"]>
  tags?: Maybe<TagPageConnection>
  taxId?: Maybe<Scalars["String"]["output"]>
  telephone?: Maybe<Scalars["String"]["output"]>
  urlKey?: Maybe<Scalars["String"]["output"]>
  userIdentifier: Scalars["String"]["output"]
}

export type ConsultantAppointmentsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantContactRequestsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantOffersArgs = {
  consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantOrdersArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantProfessionsArgs = {
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantReviewsArgs = {
  isPublished?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantTagsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultantAuth = Node & {
  __typename?: "ConsultantAuth"
  id: Scalars["ID"]["output"]
  token: Scalars["String"]["output"]
}

export type ConsultantFilter_Order = {
  createdAt?: InputMaybe<Scalars["String"]["input"]>
  isFeatured?: InputMaybe<Scalars["String"]["input"]>
  position?: InputMaybe<Scalars["String"]["input"]>
}

/** Page connection for Consultant. */
export type ConsultantPageConnection = {
  __typename?: "ConsultantPageConnection"
  collection?: Maybe<Array<Maybe<Consultant>>>
  paginationInfo: ConsultantPaginationInfo
}

/** Information about the pagination. */
export type ConsultantPaginationInfo = {
  __typename?: "ConsultantPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type ConsultantProfession = Node & {
  __typename?: "ConsultantProfession"
  _id: Scalars["Int"]["output"]
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  image?: Maybe<Scalars["String"]["output"]>
  isFeatured: Scalars["Boolean"]["output"]
  isPublished?: Maybe<Scalars["Boolean"]["output"]>
  name: Scalars["String"]["output"]
  urlKey?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for ConsultantProfession. */
export type ConsultantProfessionPageConnection = {
  __typename?: "ConsultantProfessionPageConnection"
  collection?: Maybe<Array<Maybe<ConsultantProfession>>>
  paginationInfo: ConsultantProfessionPaginationInfo
}

/** Information about the pagination. */
export type ConsultantProfessionPaginationInfo = {
  __typename?: "ConsultantProfessionPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type ConsultantReview = Node & {
  __typename?: "ConsultantReview"
  authorName?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<Consultant>
  createdAt?: Maybe<Scalars["String"]["output"]>
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  rating?: Maybe<Scalars["Int"]["output"]>
}

/** Page connection for ConsultantReview. */
export type ConsultantReviewPageConnection = {
  __typename?: "ConsultantReviewPageConnection"
  collection?: Maybe<Array<Maybe<ConsultantReview>>>
  paginationInfo: ConsultantReviewPaginationInfo
}

/** Information about the pagination. */
export type ConsultantReviewPaginationInfo = {
  __typename?: "ConsultantReviewPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type ConsultingService = Node & {
  __typename?: "ConsultingService"
  _id: Scalars["Int"]["output"]
  consultingServiceCategories?: Maybe<ConsultingServiceCategoryPageConnection>
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  image?: Maybe<Scalars["String"]["output"]>
  isFeatured?: Maybe<Scalars["Boolean"]["output"]>
  name?: Maybe<Scalars["String"]["output"]>
  offers?: Maybe<OfferPageConnection>
  price?: Maybe<Scalars["Float"]["output"]>
  reviewStatistic?: Maybe<ServiceReviewStatistic>
  reviews?: Maybe<ServiceReviewPageConnection>
  urlKey?: Maybe<Scalars["String"]["output"]>
}

export type ConsultingServiceConsultingServiceCategoriesArgs = {
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
  urlKey?: InputMaybe<Scalars["String"]["input"]>
  urlKey_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
}

export type ConsultingServiceOffersArgs = {
  consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultingServiceReviewsArgs = {
  isPublished?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ConsultingServiceCategory = Node & {
  __typename?: "ConsultingServiceCategory"
  _id: Scalars["Int"]["output"]
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  image?: Maybe<Scalars["String"]["output"]>
  isFeatured?: Maybe<Scalars["Boolean"]["output"]>
  name: Scalars["String"]["output"]
  urlKey?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for ConsultingServiceCategory. */
export type ConsultingServiceCategoryPageConnection = {
  __typename?: "ConsultingServiceCategoryPageConnection"
  collection?: Maybe<Array<Maybe<ConsultingServiceCategory>>>
  paginationInfo: ConsultingServiceCategoryPaginationInfo
}

/** Information about the pagination. */
export type ConsultingServiceCategoryPaginationInfo = {
  __typename?: "ConsultingServiceCategoryPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

/** Page connection for ConsultingService. */
export type ConsultingServicePageConnection = {
  __typename?: "ConsultingServicePageConnection"
  collection?: Maybe<Array<Maybe<ConsultingService>>>
  paginationInfo: ConsultingServicePaginationInfo
}

/** Information about the pagination. */
export type ConsultingServicePaginationInfo = {
  __typename?: "ConsultingServicePaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type ContactRequest = Node & {
  __typename?: "ContactRequest"
  _id: Scalars["Int"]["output"]
  consultant?: Maybe<Consultant>
  createdAt: Scalars["String"]["output"]
  customerEmail: Scalars["String"]["output"]
  customerFullName: Scalars["String"]["output"]
  customerTelephone?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  message: Scalars["String"]["output"]
}

/** Page connection for ContactRequest. */
export type ContactRequestPageConnection = {
  __typename?: "ContactRequestPageConnection"
  collection?: Maybe<Array<Maybe<ContactRequest>>>
  paginationInfo: ContactRequestPaginationInfo
}

/** Information about the pagination. */
export type ContactRequestPaginationInfo = {
  __typename?: "ContactRequestPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Content = Node & {
  __typename?: "Content"
  _id: Scalars["Int"]["output"]
  body?: Maybe<Scalars["String"]["output"]>
  city?: Maybe<City>
  consultantProfession?: Maybe<ConsultantProfession>
  consultantService?: Maybe<ConsultingService>
  consultingServiceCategory?: Maybe<ConsultingServiceCategory>
  createdAt: Scalars["String"]["output"]
  faqHTML?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  lastUpdateByAIAt?: Maybe<Scalars["String"]["output"]>
  metadataDescription?: Maybe<Scalars["String"]["output"]>
  metadataTitle?: Maybe<Scalars["String"]["output"]>
  title: Scalars["String"]["output"]
  updatedAt?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for Content. */
export type ContentPageConnection = {
  __typename?: "ContentPageConnection"
  collection?: Maybe<Array<Maybe<Content>>>
  paginationInfo: ContentPaginationInfo
}

/** Information about the pagination. */
export type ContentPaginationInfo = {
  __typename?: "ContentPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Customer = Node & {
  __typename?: "Customer"
  _id: Scalars["Int"]["output"]
  appointments?: Maybe<AppointmentPageConnection>
  createdAt: Scalars["String"]["output"]
  email: Scalars["String"]["output"]
  fullName: Scalars["String"]["output"]
  id: Scalars["ID"]["output"]
  modifiedAt: Scalars["String"]["output"]
  orders?: Maybe<OrderPageConnection>
  telephone: Scalars["String"]["output"]
}

export type CustomerAppointmentsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type CustomerOrdersArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type Mutation = {
  __typename?: "Mutation"
  /** AssignOffersTos a Consultant. */
  assignOffersToConsultant?: Maybe<AssignOffersToConsultantPayload>
  /** AssignTagsTos a Consultant. */
  assignTagsToConsultant?: Maybe<AssignTagsToConsultantPayload>
  /** ChangePasswordByTokens a Consultant. */
  changePasswordByTokenConsultant?: Maybe<ChangePasswordByTokenConsultantPayload>
  /** Creates a Consultant. */
  createConsultant?: Maybe<CreateConsultantPayload>
  /** Creates a ConsultantReview. */
  createConsultantReview?: Maybe<CreateConsultantReviewPayload>
  /** Creates a ContactRequest. */
  createContactRequest?: Maybe<CreateContactRequestPayload>
  /** Creates a Order. */
  createOrder?: Maybe<CreateOrderPayload>
  /** CreateToAdmins a ContactRequest. */
  createToAdminContactRequest?: Maybe<CreateToAdminContactRequestPayload>
  /** Creates a Url. */
  createUrl?: Maybe<CreateUrlPayload>
  /** Deletes a Url. */
  deleteUrl?: Maybe<DeleteUrlPayload>
  /** Logins a ConsultantAuth. */
  loginConsultantAuth?: Maybe<LoginConsultantAuthPayload>
  /** RequestChangePasswordByEmails a Consultant. */
  requestChangePasswordByEmailConsultant?: Maybe<RequestChangePasswordByEmailConsultantPayload>
  /** Updates a Consultant. */
  updateConsultant?: Maybe<UpdateConsultantPayload>
  /** Updates a Url. */
  updateUrl?: Maybe<UpdateUrlPayload>
  /** ValidateByAppointmentHashs a ConsultantReview. */
  validateByAppointmentHashConsultantReview?: Maybe<ValidateByAppointmentHashConsultantReviewPayload>
}

export type MutationAssignOffersToConsultantArgs = {
  input: AssignOffersToConsultantInput
}

export type MutationAssignTagsToConsultantArgs = {
  input: AssignTagsToConsultantInput
}

export type MutationChangePasswordByTokenConsultantArgs = {
  input: ChangePasswordByTokenConsultantInput
}

export type MutationCreateConsultantArgs = {
  input: CreateConsultantInput
}

export type MutationCreateConsultantReviewArgs = {
  input: CreateConsultantReviewInput
}

export type MutationCreateContactRequestArgs = {
  input: CreateContactRequestInput
}

export type MutationCreateOrderArgs = {
  input: CreateOrderInput
}

export type MutationCreateToAdminContactRequestArgs = {
  input: CreateToAdminContactRequestInput
}

export type MutationCreateUrlArgs = {
  input: CreateUrlInput
}

export type MutationDeleteUrlArgs = {
  input: DeleteUrlInput
}

export type MutationLoginConsultantAuthArgs = {
  input: LoginConsultantAuthInput
}

export type MutationRequestChangePasswordByEmailConsultantArgs = {
  input: RequestChangePasswordByEmailConsultantInput
}

export type MutationUpdateConsultantArgs = {
  input: UpdateConsultantInput
}

export type MutationUpdateUrlArgs = {
  input: UpdateUrlInput
}

export type MutationValidateByAppointmentHashConsultantReviewArgs = {
  input: ValidateByAppointmentHashConsultantReviewInput
}

/** A node, according to the Relay specification. */
export type Node = {
  /** The id of this node. */
  id: Scalars["ID"]["output"]
}

export type Offer = Node & {
  __typename?: "Offer"
  consultant?: Maybe<Consultant>
  consultingService?: Maybe<ConsultingService>
  id: Scalars["ID"]["output"]
  isFeatured?: Maybe<Scalars["Boolean"]["output"]>
  price?: Maybe<Scalars["Float"]["output"]>
}

/** Page connection for Offer. */
export type OfferPageConnection = {
  __typename?: "OfferPageConnection"
  collection?: Maybe<Array<Maybe<Offer>>>
  paginationInfo: OfferPaginationInfo
}

/** Information about the pagination. */
export type OfferPaginationInfo = {
  __typename?: "OfferPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Order = Node & {
  __typename?: "Order"
  consultant?: Maybe<Consultant>
  id: Scalars["ID"]["output"]
  price?: Maybe<Scalars["Float"]["output"]>
  service?: Maybe<ConsultingService>
}

/** Page connection for Order. */
export type OrderPageConnection = {
  __typename?: "OrderPageConnection"
  collection?: Maybe<Array<Maybe<Order>>>
  paginationInfo: OrderPaginationInfo
}

/** Information about the pagination. */
export type OrderPaginationInfo = {
  __typename?: "OrderPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Query = {
  __typename?: "Query"
  /** Fetches the agreement details */
  agreements?: Maybe<AgreementPageConnection>
  appointment?: Maybe<Appointment>
  appointments?: Maybe<AppointmentPageConnection>
  byTokenConsultant?: Maybe<Consultant>
  byUrlKeyConsultant?: Maybe<Consultant>
  cities?: Maybe<CityPageConnection>
  city?: Maybe<City>
  consultant?: Maybe<Consultant>
  consultantProfession?: Maybe<ConsultantProfession>
  consultantProfessions?: Maybe<ConsultantProfessionPageConnection>
  consultantReview?: Maybe<ConsultantReview>
  consultantReviews?: Maybe<ConsultantReviewPageConnection>
  consultants?: Maybe<ConsultantPageConnection>
  consultingService?: Maybe<ConsultingService>
  consultingServiceCategories?: Maybe<ConsultingServiceCategoryPageConnection>
  consultingServiceCategory?: Maybe<ConsultingServiceCategory>
  consultingServices?: Maybe<ConsultingServicePageConnection>
  content?: Maybe<Content>
  contents?: Maybe<ContentPageConnection>
  customer?: Maybe<Customer>
  itemConsultant?: Maybe<Consultant>
  listConsultants?: Maybe<ConsultantPageConnection>
  myAppointments?: Maybe<AppointmentPageConnection>
  node?: Maybe<Node>
  offer?: Maybe<Offer>
  offers?: Maybe<OfferPageConnection>
  serviceReview?: Maybe<ServiceReview>
  serviceReviewStatistic?: Maybe<ServiceReviewStatistic>
  serviceReviewStatistics?: Maybe<ServiceReviewStatisticPageConnection>
  serviceReviews?: Maybe<ServiceReviewPageConnection>
  setting?: Maybe<Setting>
  /** Get a Setting by its code. */
  settingByCodeSetting?: Maybe<Setting>
  settings?: Maybe<SettingPageConnection>
  statsByServiceIdServiceReviewStatistic?: Maybe<ServiceReviewStatistic>
  tag?: Maybe<Tag>
  tags?: Maybe<TagPageConnection>
  url?: Maybe<Url>
  urls?: Maybe<UrlPageConnection>
}

export type QueryAgreementsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryAppointmentArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryAppointmentsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryByUrlKeyConsultantArgs = {
  urlKey: Scalars["String"]["input"]
}

export type QueryCitiesArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryCityArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryConsultantArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryConsultantProfessionArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryConsultantProfessionsArgs = {
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryConsultantReviewArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryConsultantReviewsArgs = {
  isPublished?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryConsultantsArgs = {
  city?: InputMaybe<Scalars["String"]["input"]>
  city_id?: InputMaybe<Scalars["Int"]["input"]>
  city_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  city_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  city_name?: InputMaybe<Scalars["String"]["input"]>
  city_name_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  isRemote?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_consultingServiceCategories_id?: InputMaybe<
    Scalars["Int"]["input"]
  >
  offers_consultingService_consultingServiceCategories_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  offers_consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  offers_consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  offers_consultingService_id?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  offers_consultingService_name?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  order?: InputMaybe<Array<InputMaybe<ConsultantFilter_Order>>>
  page?: InputMaybe<Scalars["Int"]["input"]>
  professions_urlKey?: InputMaybe<Scalars["String"]["input"]>
  professions_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  tags_id?: InputMaybe<Scalars["Int"]["input"]>
  tags_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  urlKey?: InputMaybe<Scalars["String"]["input"]>
  urlKey_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
}

export type QueryConsultingServiceArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryConsultingServiceCategoriesArgs = {
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
  urlKey?: InputMaybe<Scalars["String"]["input"]>
  urlKey_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
}

export type QueryConsultingServiceCategoryArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryConsultingServicesArgs = {
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  offers_consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  offers_consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryContentArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryContentsArgs = {
  city?: InputMaybe<Scalars["String"]["input"]>
  city_id?: InputMaybe<Scalars["Int"]["input"]>
  city_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  city_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  city_name?: InputMaybe<Scalars["String"]["input"]>
  city_name_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  consultantProfession_id?: InputMaybe<Scalars["Int"]["input"]>
  consultantProfession_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  consultantProfession_name?: InputMaybe<Scalars["String"]["input"]>
  consultantProfession_name_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  consultantProfession_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultantProfession_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  consultantService_id?: InputMaybe<Scalars["Int"]["input"]>
  consultantService_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  consultantService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultantService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryCustomerArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryItemConsultantArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryListConsultantsArgs = {
  city?: InputMaybe<Scalars["String"]["input"]>
  city_id?: InputMaybe<Scalars["Int"]["input"]>
  city_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  city_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  city_name?: InputMaybe<Scalars["String"]["input"]>
  city_name_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  isRemote?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_consultingServiceCategories_id?: InputMaybe<
    Scalars["Int"]["input"]
  >
  offers_consultingService_consultingServiceCategories_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  offers_consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  offers_consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  offers_consultingService_id?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  offers_consultingService_name?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  order?: InputMaybe<Array<InputMaybe<ConsultantFilter_Order>>>
  page?: InputMaybe<Scalars["Int"]["input"]>
  professions_urlKey?: InputMaybe<Scalars["String"]["input"]>
  professions_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  tags_id?: InputMaybe<Scalars["Int"]["input"]>
  tags_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  urlKey?: InputMaybe<Scalars["String"]["input"]>
  urlKey_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
}

export type QueryMyAppointmentsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryNodeArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryOfferArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryOffersArgs = {
  consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryServiceReviewArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryServiceReviewStatisticArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryServiceReviewStatisticsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryServiceReviewsArgs = {
  isPublished?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QuerySettingArgs = {
  id: Scalars["ID"]["input"]
}

export type QuerySettingByCodeSettingArgs = {
  code: Scalars["String"]["input"]
}

export type QuerySettingsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryStatsByServiceIdServiceReviewStatisticArgs = {
  serviceId: Scalars["Int"]["input"]
}

export type QueryTagArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryTagsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type QueryUrlArgs = {
  id: Scalars["ID"]["input"]
}

export type QueryUrlsArgs = {
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
}

export type ServiceReview = Node & {
  __typename?: "ServiceReview"
  _id: Scalars["Int"]["output"]
  authorName?: Maybe<Scalars["String"]["output"]>
  createdAt?: Maybe<Scalars["String"]["output"]>
  customer?: Maybe<Customer>
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  isPublished: Scalars["Boolean"]["output"]
  modifiedAt?: Maybe<Scalars["String"]["output"]>
  rating?: Maybe<Scalars["Int"]["output"]>
  service?: Maybe<ConsultingService>
}

/** Page connection for ServiceReview. */
export type ServiceReviewPageConnection = {
  __typename?: "ServiceReviewPageConnection"
  collection?: Maybe<Array<Maybe<ServiceReview>>>
  paginationInfo: ServiceReviewPaginationInfo
}

/** Information about the pagination. */
export type ServiceReviewPaginationInfo = {
  __typename?: "ServiceReviewPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type ServiceReviewStatistic = Node & {
  __typename?: "ServiceReviewStatistic"
  _id: Scalars["Int"]["output"]
  averageRating?: Maybe<Scalars["String"]["output"]>
  bestRating?: Maybe<Scalars["Int"]["output"]>
  id: Scalars["ID"]["output"]
  reviewCount?: Maybe<Scalars["Int"]["output"]>
  service: ConsultingService
  worstRating?: Maybe<Scalars["Int"]["output"]>
}

/** Page connection for ServiceReviewStatistic. */
export type ServiceReviewStatisticPageConnection = {
  __typename?: "ServiceReviewStatisticPageConnection"
  collection?: Maybe<Array<Maybe<ServiceReviewStatistic>>>
  paginationInfo: ServiceReviewStatisticPaginationInfo
}

/** Information about the pagination. */
export type ServiceReviewStatisticPaginationInfo = {
  __typename?: "ServiceReviewStatisticPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Setting = Node & {
  __typename?: "Setting"
  code: Scalars["String"]["output"]
  id: Scalars["ID"]["output"]
  value?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for Setting. */
export type SettingPageConnection = {
  __typename?: "SettingPageConnection"
  collection?: Maybe<Array<Maybe<Setting>>>
  paginationInfo: SettingPaginationInfo
}

/** Information about the pagination. */
export type SettingPaginationInfo = {
  __typename?: "SettingPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Tag = Node & {
  __typename?: "Tag"
  _id: Scalars["Int"]["output"]
  consultants?: Maybe<ConsultantPageConnection>
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  name: Scalars["String"]["output"]
}

export type TagConsultantsArgs = {
  city?: InputMaybe<Scalars["String"]["input"]>
  city_id?: InputMaybe<Scalars["Int"]["input"]>
  city_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  city_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  city_name?: InputMaybe<Scalars["String"]["input"]>
  city_name_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  isRemote?: InputMaybe<Scalars["Boolean"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_consultingServiceCategories_id?: InputMaybe<
    Scalars["Int"]["input"]
  >
  offers_consultingService_consultingServiceCategories_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  offers_consultingService_consultingServiceCategories_urlKey?: InputMaybe<
    Scalars["String"]["input"]
  >
  offers_consultingService_consultingServiceCategories_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  offers_consultingService_id?: InputMaybe<Scalars["Int"]["input"]>
  offers_consultingService_id_list?: InputMaybe<
    Array<InputMaybe<Scalars["Int"]["input"]>>
  >
  offers_consultingService_name?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  offers_consultingService_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  order?: InputMaybe<Array<InputMaybe<ConsultantFilter_Order>>>
  page?: InputMaybe<Scalars["Int"]["input"]>
  professions_urlKey?: InputMaybe<Scalars["String"]["input"]>
  professions_urlKey_list?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>>
  >
  tags_id?: InputMaybe<Scalars["Int"]["input"]>
  tags_id_list?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>
  urlKey?: InputMaybe<Scalars["String"]["input"]>
  urlKey_list?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
}

/** Page connection for Tag. */
export type TagPageConnection = {
  __typename?: "TagPageConnection"
  collection?: Maybe<Array<Maybe<Tag>>>
  paginationInfo: TagPaginationInfo
}

/** Information about the pagination. */
export type TagPaginationInfo = {
  __typename?: "TagPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

export type Url = Node & {
  __typename?: "Url"
  City?: Maybe<City>
  ConsultantProfession?: Maybe<ConsultantProfession>
  ConsultingService?: Maybe<ConsultingService>
  ConsultingServiceCategory?: Maybe<ConsultingServiceCategory>
  _id: Scalars["Int"]["output"]
  bodyContent?: Maybe<Scalars["String"]["output"]>
  city?: Maybe<City>
  consultant?: Maybe<Consultant>
  consultantProfession?: Maybe<ConsultantProfession>
  consultingService?: Maybe<ConsultingService>
  consultingServiceCategory?: Maybe<ConsultingServiceCategory>
  id: Scalars["ID"]["output"]
  path?: Maybe<Scalars["String"]["output"]>
  title?: Maybe<Scalars["String"]["output"]>
}

/** Page connection for Url. */
export type UrlPageConnection = {
  __typename?: "UrlPageConnection"
  collection?: Maybe<Array<Maybe<Url>>>
  paginationInfo: UrlPaginationInfo
}

/** Information about the pagination. */
export type UrlPaginationInfo = {
  __typename?: "UrlPaginationInfo"
  hasNextPage: Scalars["Boolean"]["output"]
  itemsPerPage: Scalars["Int"]["output"]
  lastPage: Scalars["Int"]["output"]
  totalCount: Scalars["Int"]["output"]
}

/** AssignOffersTos a Consultant. */
export type AssignOffersToConsultantInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  offers?: InputMaybe<Array<InputMaybe<Scalars["Iterable"]["input"]>>>
}

/** AssignOffersTos a Consultant. */
export type AssignOffersToConsultantPayload = {
  __typename?: "assignOffersToConsultantPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<Consultant>
}

/** AssignTagsTos a Consultant. */
export type AssignTagsToConsultantInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  tagIds: Scalars["Iterable"]["input"]
}

/** AssignTagsTos a Consultant. */
export type AssignTagsToConsultantPayload = {
  __typename?: "assignTagsToConsultantPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<Consultant>
}

/** ChangePasswordByTokens a Consultant. */
export type ChangePasswordByTokenConsultantInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  password: Scalars["String"]["input"]
  token: Scalars["String"]["input"]
}

/** ChangePasswordByTokens a Consultant. */
export type ChangePasswordByTokenConsultantPayload = {
  __typename?: "changePasswordByTokenConsultantPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<Consultant>
}

/** Creates a Consultant. */
export type CreateConsultantInput = {
  avatar?: InputMaybe<Scalars["String"]["input"]>
  city?: InputMaybe<Scalars["String"]["input"]>
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  dateOfBirth?: InputMaybe<Scalars["String"]["input"]>
  email: Scalars["String"]["input"]
  fullName: Scalars["String"]["input"]
  isRemote?: InputMaybe<Scalars["Boolean"]["input"]>
  password?: InputMaybe<Scalars["String"]["input"]>
  professions?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  stripePartnerPlanSubscriptionId?: InputMaybe<Scalars["String"]["input"]>
  stripeSubscriptionId?: InputMaybe<Scalars["String"]["input"]>
  telephone?: InputMaybe<Scalars["String"]["input"]>
}

/** Creates a Consultant. */
export type CreateConsultantPayload = {
  __typename?: "createConsultantPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<CreateConsultantPayloadData>
}

/** Creates a Consultant. */
export type CreateConsultantPayloadData = Node & {
  __typename?: "createConsultantPayloadData"
  _id: Scalars["Int"]["output"]
  avatar?: Maybe<Scalars["String"]["output"]>
  bio?: Maybe<Scalars["String"]["output"]>
  city?: Maybe<City>
  dateOfBirth?: Maybe<Scalars["String"]["output"]>
  email: Scalars["String"]["output"]
  fiscalCode?: Maybe<Scalars["String"]["output"]>
  fullName: Scalars["String"]["output"]
  id: Scalars["ID"]["output"]
  industries?: Maybe<Scalars["String"]["output"]>
  isRemote?: Maybe<Scalars["Boolean"]["output"]>
  knowledgeAndCertifications?: Maybe<Scalars["String"]["output"]>
  offers?: Maybe<OfferPageConnection>
  password?: Maybe<Scalars["String"]["output"]>
  professions?: Maybe<ConsultantProfessionPageConnection>
  specializations?: Maybe<Scalars["String"]["output"]>
  stripePartnerPlanSubscriptionId?: Maybe<Scalars["String"]["output"]>
  stripeSubscriptionId?: Maybe<Scalars["String"]["output"]>
  tags?: Maybe<TagPageConnection>
  taxId?: Maybe<Scalars["String"]["output"]>
  telephone?: Maybe<Scalars["String"]["output"]>
}

/** Creates a ConsultantReview. */
export type CreateConsultantReviewInput = {
  /** Service */
  appointmentHash: Scalars["String"]["input"]
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  description: Scalars["String"]["input"]
  /** Review */
  rating: Scalars["Int"]["input"]
}

/** Creates a ConsultantReview. */
export type CreateConsultantReviewPayload = {
  __typename?: "createConsultantReviewPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultantReview?: Maybe<ConsultantReview>
}

/** Creates a ContactRequest. */
export type CreateContactRequestInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  /** Consultant */
  consultant: Scalars["Int"]["input"]
  customerEmail: Scalars["String"]["input"]
  customerFullName: Scalars["String"]["input"]
  customerTelephone: Scalars["String"]["input"]
  message: Scalars["String"]["input"]
  /** Service */
  service: Scalars["Int"]["input"]
}

/** Creates a ContactRequest. */
export type CreateContactRequestPayload = {
  __typename?: "createContactRequestPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  contactRequest?: Maybe<ContactRequest>
}

/** Creates a Order. */
export type CreateOrderInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  /** Consultant */
  consultant: Scalars["Int"]["input"]
  customerEmail: Scalars["String"]["input"]
  customerName: Scalars["String"]["input"]
  customerSurname: Scalars["String"]["input"]
  customerTelephone: Scalars["String"]["input"]
  /** Date */
  scheduledAt: Scalars["String"]["input"]
  /** Date */
  scheduledAtNote: Scalars["String"]["input"]
  /** Service */
  service: Scalars["Int"]["input"]
}

/** Creates a Order. */
export type CreateOrderPayload = {
  __typename?: "createOrderPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  order?: Maybe<Order>
}

/** CreateToAdmins a ContactRequest. */
export type CreateToAdminContactRequestInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  email?: InputMaybe<Scalars["String"]["input"]>
  message?: InputMaybe<Scalars["String"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  telephone?: InputMaybe<Scalars["String"]["input"]>
}

/** CreateToAdmins a ContactRequest. */
export type CreateToAdminContactRequestPayload = {
  __typename?: "createToAdminContactRequestPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  contactRequest?: Maybe<ContactRequest>
}

/** Creates a Url. */
export type CreateUrlInput = {
  City?: InputMaybe<Scalars["String"]["input"]>
  ConsultantProfession?: InputMaybe<Scalars["String"]["input"]>
  ConsultingService?: InputMaybe<Scalars["String"]["input"]>
  ConsultingServiceCategory?: InputMaybe<Scalars["String"]["input"]>
  bodyContent?: InputMaybe<Scalars["String"]["input"]>
  city?: InputMaybe<Scalars["String"]["input"]>
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  consultant?: InputMaybe<Scalars["String"]["input"]>
  consultantProfession?: InputMaybe<Scalars["String"]["input"]>
  consultingService?: InputMaybe<Scalars["String"]["input"]>
  consultingServiceCategory?: InputMaybe<Scalars["String"]["input"]>
  path?: InputMaybe<Scalars["String"]["input"]>
  title?: InputMaybe<Scalars["String"]["input"]>
}

/** Creates a Url. */
export type CreateUrlPayload = {
  __typename?: "createUrlPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  url?: Maybe<Url>
}

/** Deletes a Url. */
export type DeleteUrlInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  id: Scalars["ID"]["input"]
}

/** Deletes a Url. */
export type DeleteUrlPayload = {
  __typename?: "deleteUrlPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  url?: Maybe<Url>
}

/** Logins a ConsultantAuth. */
export type LoginConsultantAuthInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  email: Scalars["String"]["input"]
  password: Scalars["String"]["input"]
}

/** Logins a ConsultantAuth. */
export type LoginConsultantAuthPayload = {
  __typename?: "loginConsultantAuthPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultantAuth?: Maybe<ConsultantAuth>
}

/** RequestChangePasswordByEmails a Consultant. */
export type RequestChangePasswordByEmailConsultantInput = {
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  email: Scalars["String"]["input"]
}

/** RequestChangePasswordByEmails a Consultant. */
export type RequestChangePasswordByEmailConsultantPayload = {
  __typename?: "requestChangePasswordByEmailConsultantPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<Consultant>
}

/** Updates a Consultant. */
export type UpdateConsultantInput = {
  avatar?: InputMaybe<Scalars["String"]["input"]>
  bio?: InputMaybe<Scalars["String"]["input"]>
  city?: InputMaybe<Scalars["String"]["input"]>
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  dateOfBirth?: InputMaybe<Scalars["String"]["input"]>
  fiscalCode?: InputMaybe<Scalars["String"]["input"]>
  fullName?: InputMaybe<Scalars["String"]["input"]>
  id: Scalars["ID"]["input"]
  industries?: InputMaybe<Scalars["String"]["input"]>
  isRemote?: InputMaybe<Scalars["Boolean"]["input"]>
  knowledgeAndCertifications?: InputMaybe<Scalars["String"]["input"]>
  password?: InputMaybe<Scalars["String"]["input"]>
  professions?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  specializations?: InputMaybe<Scalars["String"]["input"]>
  stripePartnerPlanSubscriptionId?: InputMaybe<Scalars["String"]["input"]>
  stripeSubscriptionId?: InputMaybe<Scalars["String"]["input"]>
  taxId?: InputMaybe<Scalars["String"]["input"]>
  telephone?: InputMaybe<Scalars["String"]["input"]>
}

/** Updates a Consultant. */
export type UpdateConsultantPayload = {
  __typename?: "updateConsultantPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultant?: Maybe<UpdateConsultantPayloadData>
}

/** Updates a Consultant. */
export type UpdateConsultantPayloadData = Node & {
  __typename?: "updateConsultantPayloadData"
  _id?: Maybe<Scalars["Int"]["output"]>
  avatar?: Maybe<Scalars["String"]["output"]>
  bio?: Maybe<Scalars["String"]["output"]>
  city?: Maybe<City>
  dateOfBirth?: Maybe<Scalars["String"]["output"]>
  email?: Maybe<Scalars["String"]["output"]>
  fiscalCode?: Maybe<Scalars["String"]["output"]>
  fullName?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  industries?: Maybe<Scalars["String"]["output"]>
  isRemote?: Maybe<Scalars["Boolean"]["output"]>
  knowledgeAndCertifications?: Maybe<Scalars["String"]["output"]>
  offers?: Maybe<OfferPageConnection>
  password?: Maybe<Scalars["String"]["output"]>
  professions?: Maybe<ConsultantProfessionPageConnection>
  specializations?: Maybe<Scalars["String"]["output"]>
  stripePartnerPlanSubscriptionId?: Maybe<Scalars["String"]["output"]>
  stripeSubscriptionId?: Maybe<Scalars["String"]["output"]>
  tags?: Maybe<TagPageConnection>
  taxId?: Maybe<Scalars["String"]["output"]>
  telephone?: Maybe<Scalars["String"]["output"]>
}

/** Updates a Url. */
export type UpdateUrlInput = {
  City?: InputMaybe<Scalars["String"]["input"]>
  ConsultantProfession?: InputMaybe<Scalars["String"]["input"]>
  ConsultingService?: InputMaybe<Scalars["String"]["input"]>
  ConsultingServiceCategory?: InputMaybe<Scalars["String"]["input"]>
  bodyContent?: InputMaybe<Scalars["String"]["input"]>
  city?: InputMaybe<Scalars["String"]["input"]>
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
  consultant?: InputMaybe<Scalars["String"]["input"]>
  consultantProfession?: InputMaybe<Scalars["String"]["input"]>
  consultingService?: InputMaybe<Scalars["String"]["input"]>
  consultingServiceCategory?: InputMaybe<Scalars["String"]["input"]>
  id: Scalars["ID"]["input"]
  path?: InputMaybe<Scalars["String"]["input"]>
  title?: InputMaybe<Scalars["String"]["input"]>
}

/** Updates a Url. */
export type UpdateUrlPayload = {
  __typename?: "updateUrlPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  url?: Maybe<Url>
}

/** ValidateByAppointmentHashs a ConsultantReview. */
export type ValidateByAppointmentHashConsultantReviewInput = {
  /** Hash of the appointment */
  appointmentHash: Scalars["String"]["input"]
  clientMutationId?: InputMaybe<Scalars["String"]["input"]>
}

/** ValidateByAppointmentHashs a ConsultantReview. */
export type ValidateByAppointmentHashConsultantReviewPayload = {
  __typename?: "validateByAppointmentHashConsultantReviewPayload"
  clientMutationId?: Maybe<Scalars["String"]["output"]>
  consultantReview?: Maybe<ConsultantReview>
}

export type GetConsultingServicesQueryVariables = Exact<{
  serviceUrlKey?: InputMaybe<Scalars["String"]["input"]>
}>

export type GetConsultingServicesQuery = {
  __typename?: "Query"
  consultingServices?: {
    __typename?: "ConsultingServicePageConnection"
    collection?: Array<{
      __typename?: "ConsultingService"
      name?: string | null
      urlKey?: string | null
      price?: number | null
      id: string
      _id: number
      description?: string | null
      consultingServiceCategories?: {
        __typename?: "ConsultingServiceCategoryPageConnection"
        collection?: Array<{
          __typename?: "ConsultingServiceCategory"
          id: string
          _id: number
          name: string
          urlKey?: string | null
          description?: string | null
          isFeatured?: boolean | null
          image?: string | null
        } | null> | null
        paginationInfo: {
          __typename?: "ConsultingServiceCategoryPaginationInfo"
          itemsPerPage: number
          lastPage: number
          totalCount: number
          hasNextPage: boolean
        }
      } | null
    } | null> | null
  } | null
}

export type GetConsultingServicesForLinksQueryVariables = Exact<{
  serviceUrlKey?: InputMaybe<Scalars["String"]["input"]>
}>

export type GetConsultingServicesForLinksQuery = {
  __typename?: "Query"
  consultingServices?: {
    __typename?: "ConsultingServicePageConnection"
    collection?: Array<{
      __typename?: "ConsultingService"
      name?: string | null
      urlKey?: string | null
      _id: number
    } | null> | null
  } | null
}

export type GetConsultingCategoriesQueryVariables = Exact<{
  [key: string]: never
}>

export type GetConsultingCategoriesQuery = {
  __typename?: "Query"
  consultingServiceCategories?: {
    __typename?: "ConsultingServiceCategoryPageConnection"
    collection?: Array<{
      __typename?: "ConsultingServiceCategory"
      name: string
      urlKey?: string | null
      id: string
      description?: string | null
    } | null> | null
  } | null
}

export type GetCitiesQueryVariables = Exact<{
  name?: InputMaybe<Scalars["String"]["input"]>
}>

export type GetCitiesQuery = {
  __typename?: "Query"
  cities?: {
    __typename?: "CityPageConnection"
    collection?: Array<{
      __typename?: "City"
      name: string
      urlKey?: string | null
      id: string
    } | null> | null
  } | null
}

export type GetFilteredConsultantsQueryVariables = Exact<{
  city?: InputMaybe<Scalars["String"]["input"]>
  cityId?: InputMaybe<Scalars["Int"]["input"]>
  cityName?: InputMaybe<Scalars["String"]["input"]>
  service?: InputMaybe<Scalars["Int"]["input"]>
  serviceUrlKey?: InputMaybe<Scalars["String"]["input"]>
  serviceCategoryUrlKey?: InputMaybe<Scalars["String"]["input"]>
  professionsUrlKey?: InputMaybe<Scalars["String"]["input"]>
  page?: InputMaybe<Scalars["Int"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  isOfferFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  isRemote?: InputMaybe<Scalars["Boolean"]["input"]>
  isFeatured?: InputMaybe<Scalars["Boolean"]["input"]>
  tags?: InputMaybe<
    | Array<InputMaybe<Scalars["Int"]["input"]>>
    | InputMaybe<Scalars["Int"]["input"]>
  >
}>

export type GetFilteredConsultantsQuery = {
  __typename?: "Query"
  listConsultants?: {
    __typename?: "ConsultantPageConnection"
    collection?: Array<{
      __typename?: "Consultant"
      _id: number
      fullName: string
      avatar?: string | null
      urlKey?: string | null
      reviews?: {
        __typename?: "ConsultantReviewPageConnection"
        collection?: Array<{
          __typename?: "ConsultantReview"
          createdAt?: string | null
          rating?: number | null
          description?: string | null
        } | null> | null
      } | null
      city?: { __typename?: "City"; name: string } | null
      tags?: {
        __typename?: "TagPageConnection"
        collection?: Array<{
          __typename?: "Tag"
          name: string
          _id: number
        } | null> | null
      } | null
      offers?: {
        __typename?: "OfferPageConnection"
        collection?: Array<{
          __typename?: "Offer"
          price?: number | null
          consultingService?: {
            __typename?: "ConsultingService"
            name?: string | null
            price?: number | null
            urlKey?: string | null
            _id: number
            consultingServiceCategories?: {
              __typename?: "ConsultingServiceCategoryPageConnection"
              collection?: Array<{
                __typename?: "ConsultingServiceCategory"
                id: string
                _id: number
                name: string
                urlKey?: string | null
                description?: string | null
                isFeatured?: boolean | null
                image?: string | null
              } | null> | null
            } | null
          } | null
        } | null> | null
      } | null
      professions?: {
        __typename?: "ConsultantProfessionPageConnection"
        collection?: Array<{
          __typename?: "ConsultantProfession"
          id: string
          _id: number
          name: string
          urlKey?: string | null
        } | null> | null
      } | null
    } | null> | null
    paginationInfo: {
      __typename?: "ConsultantPaginationInfo"
      totalCount: number
      lastPage: number
      itemsPerPage: number
    }
  } | null
}

export type GetConsultantQueryVariables = Exact<{
  urlKey: Scalars["String"]["input"]
}>

export type GetConsultantQuery = {
  __typename?: "Query"
  byUrlKeyConsultant?: {
    __typename?: "Consultant"
    fullName: string
    avatar?: string | null
    urlKey?: string | null
    bio?: string | null
    _id: number
    reviews?: {
      __typename?: "ConsultantReviewPageConnection"
      collection?: Array<{
        __typename?: "ConsultantReview"
        rating?: number | null
        createdAt?: string | null
        description?: string | null
        authorName?: string | null
      } | null> | null
    } | null
    city?: { __typename?: "City"; name: string } | null
    tags?: {
      __typename?: "TagPageConnection"
      collection?: Array<{ __typename?: "Tag"; name: string } | null> | null
    } | null
    offers?: {
      __typename?: "OfferPageConnection"
      collection?: Array<{
        __typename?: "Offer"
        price?: number | null
        id: string
        consultingService?: {
          __typename?: "ConsultingService"
          _id: number
          name?: string | null
          urlKey?: string | null
          price?: number | null
          consultingServiceCategories?: {
            __typename?: "ConsultingServiceCategoryPageConnection"
            collection?: Array<{
              __typename?: "ConsultingServiceCategory"
              id: string
              _id: number
              name: string
              urlKey?: string | null
              description?: string | null
              image?: string | null
              isFeatured?: boolean | null
            } | null> | null
          } | null
        } | null
      } | null> | null
    } | null
    professions?: {
      __typename?: "ConsultantProfessionPageConnection"
      collection?: Array<{
        __typename?: "ConsultantProfession"
        id: string
        _id: number
        name: string
        urlKey?: string | null
      } | null> | null
    } | null
  } | null
}

export type GetConsultantByTokenQueryVariables = Exact<{ [key: string]: never }>

export type GetConsultantByTokenQuery = {
  __typename?: "Query"
  byTokenConsultant?: {
    __typename?: "Consultant"
    fullName: string
    email: string
    avatar?: string | null
    urlKey?: string | null
    _id: number
    id: string
    bio?: string | null
    isRemote?: boolean | null
    dateOfBirth?: string | null
    fiscalCode?: string | null
    taxId?: string | null
    telephone?: string | null
    stripeSubscriptionId?: string | null
    stripePartnerPlanSubscriptionId?: string | null
    city?: { __typename?: "City"; name: string; id: string } | null
    reviews?: {
      __typename?: "ConsultantReviewPageConnection"
      collection?: Array<{
        __typename?: "ConsultantReview"
        rating?: number | null
        createdAt?: string | null
        description?: string | null
        authorName?: string | null
      } | null> | null
    } | null
    tags?: {
      __typename?: "TagPageConnection"
      collection?: Array<{
        __typename?: "Tag"
        name: string
        _id: number
      } | null> | null
    } | null
    offers?: {
      __typename?: "OfferPageConnection"
      collection?: Array<{
        __typename?: "Offer"
        price?: number | null
        id: string
        consultingService?: {
          __typename?: "ConsultingService"
          _id: number
          id: string
          name?: string | null
          urlKey?: string | null
          price?: number | null
          consultingServiceCategories?: {
            __typename?: "ConsultingServiceCategoryPageConnection"
            collection?: Array<{
              __typename?: "ConsultingServiceCategory"
              id: string
              _id: number
              name: string
              urlKey?: string | null
              description?: string | null
              isFeatured?: boolean | null
              image?: string | null
            } | null> | null
          } | null
        } | null
      } | null> | null
    } | null
    professions?: {
      __typename?: "ConsultantProfessionPageConnection"
      collection?: Array<{
        __typename?: "ConsultantProfession"
        name: string
        urlKey?: string | null
        id: string
        isPublished?: boolean | null
        description?: string | null
        image?: string | null
        isFeatured: boolean
      } | null> | null
    } | null
  } | null
}

export type CreateOrderMutationVariables = Exact<{
  input: CreateOrderInput
}>

export type CreateOrderMutation = {
  __typename?: "Mutation"
  createOrder?: {
    __typename?: "createOrderPayload"
    clientMutationId?: string | null
    order?: {
      __typename?: "Order"
      id: string
      price?: number | null
      consultant?: { __typename?: "Consultant"; id: string } | null
      service?: { __typename?: "ConsultingService"; id: string } | null
    } | null
  } | null
}

export type GetTopTenConsultantsQueryVariables = Exact<{
  page?: InputMaybe<Scalars["Int"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
}>

export type GetTopTenConsultantsQuery = {
  __typename?: "Query"
  listConsultants?: {
    __typename?: "ConsultantPageConnection"
    collection?: Array<{
      __typename?: "Consultant"
      fullName: string
      avatar?: string | null
      urlKey?: string | null
      city?: { __typename?: "City"; name: string } | null
      reviews?: {
        __typename?: "ConsultantReviewPageConnection"
        collection?: Array<{
          __typename?: "ConsultantReview"
          rating?: number | null
        } | null> | null
      } | null
    } | null> | null
  } | null
}

export type GetConsultantProfessionsQueryVariables = Exact<{
  [key: string]: never
}>

export type GetConsultantProfessionsQuery = {
  __typename?: "Query"
  consultantProfessions?: {
    __typename?: "ConsultantProfessionPageConnection"
    collection?: Array<{
      __typename?: "ConsultantProfession"
      id: string
      _id: number
      isPublished?: boolean | null
      isFeatured: boolean
      name: string
      urlKey?: string | null
      image?: string | null
      description?: string | null
    } | null> | null
  } | null
}

export type GetConsultantProfessionsAllQueryVariables = Exact<{
  [key: string]: never
}>

export type GetConsultantProfessionsAllQuery = {
  __typename?: "Query"
  consultantProfessions?: {
    __typename?: "ConsultantProfessionPageConnection"
    collection?: Array<{
      __typename?: "ConsultantProfession"
      id: string
      _id: number
      isPublished?: boolean | null
      name: string
      urlKey?: string | null
    } | null> | null
  } | null
}

export type GetFeaturedConsultingServiceCategoriesQueryVariables = Exact<{
  [key: string]: never
}>

export type GetFeaturedConsultingServiceCategoriesQuery = {
  __typename?: "Query"
  consultingServiceCategories?: {
    __typename?: "ConsultingServiceCategoryPageConnection"
    collection?: Array<{
      __typename?: "ConsultingServiceCategory"
      id: string
      name: string
      image?: string | null
      isFeatured?: boolean | null
      urlKey?: string | null
    } | null> | null
    paginationInfo: {
      __typename?: "ConsultingServiceCategoryPaginationInfo"
      totalCount: number
    }
  } | null
}

export type GetFeaturedConsultantProfessionsQueryVariables = Exact<{
  [key: string]: never
}>

export type GetFeaturedConsultantProfessionsQuery = {
  __typename?: "Query"
  consultantProfessions?: {
    __typename?: "ConsultantProfessionPageConnection"
    collection?: Array<{
      __typename?: "ConsultantProfession"
      id: string
      name: string
      isFeatured: boolean
      urlKey?: string | null
      image?: string | null
    } | null> | null
    paginationInfo: {
      __typename?: "ConsultantProfessionPaginationInfo"
      totalCount: number
    }
  } | null
}

export type GetFeaturedConsultingServicesQueryVariables = Exact<{
  [key: string]: never
}>

export type GetFeaturedConsultingServicesQuery = {
  __typename?: "Query"
  consultingServices?: {
    __typename?: "ConsultingServicePageConnection"
    collection?: Array<{
      __typename?: "ConsultingService"
      id: string
      name?: string | null
      isFeatured?: boolean | null
      urlKey?: string | null
      image?: string | null
    } | null> | null
    paginationInfo: {
      __typename?: "ConsultingServicePaginationInfo"
      totalCount: number
    }
  } | null
}

export type CreateContactRequestMutationVariables = Exact<{
  input: CreateContactRequestInput
}>

export type CreateContactRequestMutation = {
  __typename?: "Mutation"
  createContactRequest?: {
    __typename?: "createContactRequestPayload"
    contactRequest?: { __typename?: "ContactRequest"; id: string } | null
  } | null
}

export type CreateToAdminContactRequestMutationVariables = Exact<{
  input: CreateToAdminContactRequestInput
}>

export type CreateToAdminContactRequestMutation = {
  __typename?: "Mutation"
  createToAdminContactRequest?: {
    __typename?: "createToAdminContactRequestPayload"
    clientMutationId?: string | null
  } | null
}

export type CreateConsultantMutationVariables = Exact<{
  input: CreateConsultantInput
}>

export type CreateConsultantMutation = {
  __typename?: "Mutation"
  createConsultant?: {
    __typename?: "createConsultantPayload"
    consultant?: {
      __typename?: "createConsultantPayloadData"
      fullName: string
    } | null
  } | null
}

export type LoginConsultantMutationVariables = Exact<{
  input: LoginConsultantAuthInput
}>

export type LoginConsultantMutation = {
  __typename?: "Mutation"
  loginConsultantAuth?: {
    __typename?: "loginConsultantAuthPayload"
    consultantAuth?: {
      __typename?: "ConsultantAuth"
      token: string
      id: string
    } | null
  } | null
}

export type ChangePasswordByTokenConsultantMutationVariables = Exact<{
  input: ChangePasswordByTokenConsultantInput
}>

export type ChangePasswordByTokenConsultantMutation = {
  __typename?: "Mutation"
  changePasswordByTokenConsultant?: {
    __typename?: "changePasswordByTokenConsultantPayload"
    consultant?: { __typename?: "Consultant"; id: string } | null
  } | null
}

export type GetTagsQueryVariables = Exact<{ [key: string]: never }>

export type GetTagsQuery = {
  __typename?: "Query"
  tags?: {
    __typename?: "TagPageConnection"
    collection?: Array<{
      __typename?: "Tag"
      name: string
      _id: number
    } | null> | null
  } | null
}

export type AssignTagsToConsultantMutationVariables = Exact<{
  input: AssignTagsToConsultantInput
}>

export type AssignTagsToConsultantMutation = {
  __typename?: "Mutation"
  assignTagsToConsultant?: {
    __typename?: "assignTagsToConsultantPayload"
    consultant?: { __typename?: "Consultant"; id: string } | null
  } | null
}

export type AssignOffersToConsultantMutationVariables = Exact<{
  input: AssignOffersToConsultantInput
}>

export type AssignOffersToConsultantMutation = {
  __typename?: "Mutation"
  assignOffersToConsultant?: {
    __typename?: "assignOffersToConsultantPayload"
    consultant?: { __typename?: "Consultant"; id: string } | null
  } | null
}

export type UpdateConsultantMutationVariables = Exact<{
  input: UpdateConsultantInput
}>

export type UpdateConsultantMutation = {
  __typename?: "Mutation"
  updateConsultant?: {
    __typename?: "updateConsultantPayload"
    consultant?: {
      __typename?: "updateConsultantPayloadData"
      id: string
    } | null
  } | null
}

export type GetAppointmentsQueryVariables = Exact<{ [key: string]: never }>

export type GetAppointmentsQuery = {
  __typename?: "Query"
  myAppointments?: {
    __typename?: "AppointmentPageConnection"
    collection?: Array<{
      __typename?: "Appointment"
      id: string
      scheduledAt?: string | null
      paymentStatus?: AppointmentPaymentStatus | null
      customer?: {
        __typename?: "Customer"
        telephone: string
        email: string
        fullName: string
      } | null
      customerOrder?: {
        __typename?: "Order"
        price?: number | null
        service?: {
          __typename?: "ConsultingService"
          name?: string | null
        } | null
      } | null
    } | null> | null
  } | null
}

export type GetAgreementsQueryVariables = Exact<{ [key: string]: never }>

export type GetAgreementsQuery = {
  __typename?: "Query"
  agreements?: {
    __typename?: "AgreementPageConnection"
    collection?: Array<{
      __typename?: "Agreement"
      id: string
      content: string
    } | null> | null
  } | null
}

export type GetTitleFromPathQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type GetTitleFromPathQuery = {
  __typename?: "Query"
  url?: {
    __typename?: "Url"
    title?: string | null
    bodyContent?: string | null
  } | null
}

export type CreateConsultantReviewMutationVariables = Exact<{
  appointmentHash: Scalars["String"]["input"]
  description: Scalars["String"]["input"]
  rating: Scalars["Int"]["input"]
}>

export type CreateConsultantReviewMutation = {
  __typename?: "Mutation"
  createConsultantReview?: {
    __typename?: "createConsultantReviewPayload"
    consultantReview?: { __typename?: "ConsultantReview"; id: string } | null
  } | null
}

export type ValidateByAppointmentHashConsultantReviewMutationVariables = Exact<{
  appointmentHash: Scalars["String"]["input"]
}>

export type ValidateByAppointmentHashConsultantReviewMutation = {
  __typename?: "Mutation"
  validateByAppointmentHashConsultantReview?: {
    __typename?: "validateByAppointmentHashConsultantReviewPayload"
    consultantReview?: { __typename?: "ConsultantReview"; id: string } | null
  } | null
}

export type SettingByCodeSettingQueryVariables = Exact<{ [key: string]: never }>

export type SettingByCodeSettingQuery = {
  __typename?: "Query"
  settingByCodeSetting?: {
    __typename?: "Setting"
    code: string
    value?: string | null
  } | null
}

export type ContentsQueryVariables = Exact<{
  page?: InputMaybe<Scalars["Int"]["input"]>
  itemsPerPage?: InputMaybe<Scalars["Int"]["input"]>
  consultantProfession_name?: InputMaybe<Scalars["String"]["input"]>
  consultantProfession_name_list?: InputMaybe<
    | Array<InputMaybe<Scalars["String"]["input"]>>
    | InputMaybe<Scalars["String"]["input"]>
  >
  consultantProfession_id?: InputMaybe<Scalars["Int"]["input"]>
  consultantProfession_id_list?: InputMaybe<
    | Array<InputMaybe<Scalars["Int"]["input"]>>
    | InputMaybe<Scalars["Int"]["input"]>
  >
  consultantService_id?: InputMaybe<Scalars["Int"]["input"]>
  consultantService_id_list?: InputMaybe<
    | Array<InputMaybe<Scalars["Int"]["input"]>>
    | InputMaybe<Scalars["Int"]["input"]>
  >
  city_name?: InputMaybe<Scalars["String"]["input"]>
  city_name_list?: InputMaybe<
    | Array<InputMaybe<Scalars["String"]["input"]>>
    | InputMaybe<Scalars["String"]["input"]>
  >
  city?: InputMaybe<Scalars["String"]["input"]>
  city_list?: InputMaybe<
    | Array<InputMaybe<Scalars["String"]["input"]>>
    | InputMaybe<Scalars["String"]["input"]>
  >
  city_id?: InputMaybe<Scalars["Int"]["input"]>
  city_id_list?: InputMaybe<
    | Array<InputMaybe<Scalars["Int"]["input"]>>
    | InputMaybe<Scalars["Int"]["input"]>
  >
  consultantService_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultantService_urlKey_list?: InputMaybe<
    | Array<InputMaybe<Scalars["String"]["input"]>>
    | InputMaybe<Scalars["String"]["input"]>
  >
  consultantProfession_urlKey?: InputMaybe<Scalars["String"]["input"]>
  consultantProfession_urlKey_list?: InputMaybe<
    | Array<InputMaybe<Scalars["String"]["input"]>>
    | InputMaybe<Scalars["String"]["input"]>
  >
}>

export type ContentsQuery = {
  __typename?: "Query"
  contents?: {
    __typename?: "ContentPageConnection"
    collection?: Array<{
      __typename?: "Content"
      id: string
      _id: number
      createdAt: string
      updatedAt?: string | null
      lastUpdateByAIAt?: string | null
      title: string
      body?: string | null
      faqHTML?: string | null
      metadataTitle?: string | null
      metadataDescription?: string | null
      city?: {
        __typename?: "City"
        id: string
        name: string
        urlKey?: string | null
      } | null
      consultantProfession?: {
        __typename?: "ConsultantProfession"
        id: string
        _id: number
        name: string
        urlKey?: string | null
        isPublished?: boolean | null
        description?: string | null
        image?: string | null
        isFeatured: boolean
      } | null
      consultantService?: {
        __typename?: "ConsultingService"
        id: string
        _id: number
        name?: string | null
        price?: number | null
        urlKey?: string | null
        isFeatured?: boolean | null
        description?: string | null
        image?: string | null
      } | null
    } | null> | null
    paginationInfo: {
      __typename?: "ContentPaginationInfo"
      itemsPerPage: number
      lastPage: number
      totalCount: number
      hasNextPage: boolean
    }
  } | null
}

export type RatingsForServicesQueryVariables = Exact<{
  serviceId: Scalars["Int"]["input"]
}>

export type RatingsForServicesQuery = {
  __typename?: "Query"
  statsByServiceIdServiceReviewStatistic?: {
    __typename?: "ServiceReviewStatistic"
    averageRating?: string | null
    reviewCount?: number | null
    bestRating?: number | null
    worstRating?: number | null
    service: { __typename?: "ConsultingService"; name?: string | null }
  } | null
}

export type GetConsultingServiceIdByUrlKeyQueryVariables = Exact<{
  urlKey: Scalars["String"]["input"]
}>

export type GetConsultingServiceIdByUrlKeyQuery = {
  __typename?: "Query"
  consultingServices?: {
    __typename?: "ConsultingServicePageConnection"
    collection?: Array<{
      __typename?: "ConsultingService"
      _id: number
    } | null> | null
  } | null
}

export const GetConsultingServicesDocument = `
    query getConsultingServices($serviceUrlKey: String) {
  consultingServices(
    offers_consultingService_urlKey: $serviceUrlKey
    itemsPerPage: 10000
  ) {
    collection {
      name
      urlKey
      price
      id
      _id
      description
      consultingServiceCategories {
        collection {
          id
          _id
          name
          urlKey
          description
          isFeatured
          image
        }
        paginationInfo {
          itemsPerPage
          lastPage
          totalCount
          hasNextPage
        }
      }
    }
  }
}
    `

export const useGetConsultingServicesQuery = <
  TData = GetConsultingServicesQuery,
  TError = unknown,
>(
  variables?: GetConsultingServicesQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultingServicesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultingServices"]
        : ["getConsultingServices", variables],
    queryFn: fetcher<
      GetConsultingServicesQuery,
      GetConsultingServicesQueryVariables
    >(GetConsultingServicesDocument, variables),
    ...options,
  })
}

useGetConsultingServicesQuery.getKey = (
  variables?: GetConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServices"]
    : ["getConsultingServices", variables]

export const useSuspenseGetConsultingServicesQuery = <
  TData = GetConsultingServicesQuery,
  TError = unknown,
>(
  variables?: GetConsultingServicesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultingServicesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultingServicesSuspense"]
        : ["getConsultingServicesSuspense", variables],
    queryFn: fetcher<
      GetConsultingServicesQuery,
      GetConsultingServicesQueryVariables
    >(GetConsultingServicesDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultingServicesQuery.getKey = (
  variables?: GetConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServicesSuspense"]
    : ["getConsultingServicesSuspense", variables]

export const useInfiniteGetConsultingServicesQuery = <
  TData = InfiniteData<GetConsultingServicesQuery>,
  TError = unknown,
>(
  variables: GetConsultingServicesQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultingServicesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultingServices.infinite"]
            : ["getConsultingServices.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingServicesQuery,
            GetConsultingServicesQueryVariables
          >(GetConsultingServicesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultingServicesQuery.getKey = (
  variables?: GetConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServices.infinite"]
    : ["getConsultingServices.infinite", variables]

export const useSuspenseInfiniteGetConsultingServicesQuery = <
  TData = InfiniteData<GetConsultingServicesQuery>,
  TError = unknown,
>(
  variables: GetConsultingServicesQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetConsultingServicesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultingServices.infiniteSuspense"]
            : ["getConsultingServices.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingServicesQuery,
            GetConsultingServicesQueryVariables
          >(GetConsultingServicesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultingServicesQuery.getKey = (
  variables?: GetConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServices.infiniteSuspense"]
    : ["getConsultingServices.infiniteSuspense", variables]

useGetConsultingServicesQuery.fetcher = (
  variables?: GetConsultingServicesQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetConsultingServicesQuery, GetConsultingServicesQueryVariables>(
    GetConsultingServicesDocument,
    variables,
    options
  )

export const GetConsultingServicesForLinksDocument = `
    query getConsultingServicesForLinks($serviceUrlKey: String) {
  consultingServices(
    offers_consultingService_urlKey: $serviceUrlKey
    itemsPerPage: 10000
  ) {
    collection {
      name
      urlKey
      _id
    }
  }
}
    `

export const useGetConsultingServicesForLinksQuery = <
  TData = GetConsultingServicesForLinksQuery,
  TError = unknown,
>(
  variables?: GetConsultingServicesForLinksQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultingServicesForLinksQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultingServicesForLinksQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultingServicesForLinksQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultingServicesForLinks"]
        : ["getConsultingServicesForLinks", variables],
    queryFn: fetcher<
      GetConsultingServicesForLinksQuery,
      GetConsultingServicesForLinksQueryVariables
    >(GetConsultingServicesForLinksDocument, variables),
    ...options,
  })
}

useGetConsultingServicesForLinksQuery.getKey = (
  variables?: GetConsultingServicesForLinksQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServicesForLinks"]
    : ["getConsultingServicesForLinks", variables]

export const useSuspenseGetConsultingServicesForLinksQuery = <
  TData = GetConsultingServicesForLinksQuery,
  TError = unknown,
>(
  variables?: GetConsultingServicesForLinksQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultingServicesForLinksQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultingServicesForLinksQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultingServicesForLinksQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultingServicesForLinksSuspense"]
        : ["getConsultingServicesForLinksSuspense", variables],
    queryFn: fetcher<
      GetConsultingServicesForLinksQuery,
      GetConsultingServicesForLinksQueryVariables
    >(GetConsultingServicesForLinksDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultingServicesForLinksQuery.getKey = (
  variables?: GetConsultingServicesForLinksQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServicesForLinksSuspense"]
    : ["getConsultingServicesForLinksSuspense", variables]

export const useInfiniteGetConsultingServicesForLinksQuery = <
  TData = InfiniteData<GetConsultingServicesForLinksQuery>,
  TError = unknown,
>(
  variables: GetConsultingServicesForLinksQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultingServicesForLinksQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultingServicesForLinksQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultingServicesForLinksQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultingServicesForLinks.infinite"]
            : ["getConsultingServicesForLinks.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingServicesForLinksQuery,
            GetConsultingServicesForLinksQueryVariables
          >(GetConsultingServicesForLinksDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultingServicesForLinksQuery.getKey = (
  variables?: GetConsultingServicesForLinksQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServicesForLinks.infinite"]
    : ["getConsultingServicesForLinks.infinite", variables]

export const useSuspenseInfiniteGetConsultingServicesForLinksQuery = <
  TData = InfiniteData<GetConsultingServicesForLinksQuery>,
  TError = unknown,
>(
  variables: GetConsultingServicesForLinksQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetConsultingServicesForLinksQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultingServicesForLinksQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<
    GetConsultingServicesForLinksQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultingServicesForLinks.infiniteSuspense"]
            : ["getConsultingServicesForLinks.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingServicesForLinksQuery,
            GetConsultingServicesForLinksQueryVariables
          >(GetConsultingServicesForLinksDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultingServicesForLinksQuery.getKey = (
  variables?: GetConsultingServicesForLinksQueryVariables
) =>
  variables === undefined
    ? ["getConsultingServicesForLinks.infiniteSuspense"]
    : ["getConsultingServicesForLinks.infiniteSuspense", variables]

useGetConsultingServicesForLinksQuery.fetcher = (
  variables?: GetConsultingServicesForLinksQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetConsultingServicesForLinksQuery,
    GetConsultingServicesForLinksQueryVariables
  >(GetConsultingServicesForLinksDocument, variables, options)

export const GetConsultingCategoriesDocument = `
    query getConsultingCategories {
  consultingServiceCategories(itemsPerPage: 10000) {
    collection {
      name
      urlKey
      id
      description
    }
  }
}
    `

export const useGetConsultingCategoriesQuery = <
  TData = GetConsultingCategoriesQuery,
  TError = unknown,
>(
  variables?: GetConsultingCategoriesQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultingCategoriesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultingCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultingCategoriesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultingCategories"]
        : ["getConsultingCategories", variables],
    queryFn: fetcher<
      GetConsultingCategoriesQuery,
      GetConsultingCategoriesQueryVariables
    >(GetConsultingCategoriesDocument, variables),
    ...options,
  })
}

useGetConsultingCategoriesQuery.getKey = (
  variables?: GetConsultingCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingCategories"]
    : ["getConsultingCategories", variables]

export const useSuspenseGetConsultingCategoriesQuery = <
  TData = GetConsultingCategoriesQuery,
  TError = unknown,
>(
  variables?: GetConsultingCategoriesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultingCategoriesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultingCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultingCategoriesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultingCategoriesSuspense"]
        : ["getConsultingCategoriesSuspense", variables],
    queryFn: fetcher<
      GetConsultingCategoriesQuery,
      GetConsultingCategoriesQueryVariables
    >(GetConsultingCategoriesDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultingCategoriesQuery.getKey = (
  variables?: GetConsultingCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingCategoriesSuspense"]
    : ["getConsultingCategoriesSuspense", variables]

export const useInfiniteGetConsultingCategoriesQuery = <
  TData = InfiniteData<GetConsultingCategoriesQuery>,
  TError = unknown,
>(
  variables: GetConsultingCategoriesQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultingCategoriesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultingCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultingCategoriesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultingCategories.infinite"]
            : ["getConsultingCategories.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingCategoriesQuery,
            GetConsultingCategoriesQueryVariables
          >(GetConsultingCategoriesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultingCategoriesQuery.getKey = (
  variables?: GetConsultingCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingCategories.infinite"]
    : ["getConsultingCategories.infinite", variables]

export const useSuspenseInfiniteGetConsultingCategoriesQuery = <
  TData = InfiniteData<GetConsultingCategoriesQuery>,
  TError = unknown,
>(
  variables: GetConsultingCategoriesQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetConsultingCategoriesQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultingCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetConsultingCategoriesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultingCategories.infiniteSuspense"]
            : ["getConsultingCategories.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingCategoriesQuery,
            GetConsultingCategoriesQueryVariables
          >(GetConsultingCategoriesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultingCategoriesQuery.getKey = (
  variables?: GetConsultingCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getConsultingCategories.infiniteSuspense"]
    : ["getConsultingCategories.infiniteSuspense", variables]

useGetConsultingCategoriesQuery.fetcher = (
  variables?: GetConsultingCategoriesQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetConsultingCategoriesQuery, GetConsultingCategoriesQueryVariables>(
    GetConsultingCategoriesDocument,
    variables,
    options
  )

export const GetCitiesDocument = `
    query getCities($name: String) {
  cities(name: $name, itemsPerPage: 999999) {
    collection {
      name
      urlKey
      id
    }
  }
}
    `

export const useGetCitiesQuery = <TData = GetCitiesQuery, TError = unknown>(
  variables?: GetCitiesQueryVariables,
  options?: Omit<UseQueryOptions<GetCitiesQuery, TError, TData>, "queryKey"> & {
    queryKey?: UseQueryOptions<GetCitiesQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<GetCitiesQuery, TError, TData>({
    queryKey:
      variables === undefined ? ["getCities"] : ["getCities", variables],
    queryFn: fetcher<GetCitiesQuery, GetCitiesQueryVariables>(
      GetCitiesDocument,
      variables
    ),
    ...options,
  })
}

useGetCitiesQuery.getKey = (variables?: GetCitiesQueryVariables) =>
  variables === undefined ? ["getCities"] : ["getCities", variables]

export const useSuspenseGetCitiesQuery = <
  TData = GetCitiesQuery,
  TError = unknown,
>(
  variables?: GetCitiesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetCitiesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetCitiesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetCitiesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getCitiesSuspense"]
        : ["getCitiesSuspense", variables],
    queryFn: fetcher<GetCitiesQuery, GetCitiesQueryVariables>(
      GetCitiesDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseGetCitiesQuery.getKey = (variables?: GetCitiesQueryVariables) =>
  variables === undefined
    ? ["getCitiesSuspense"]
    : ["getCitiesSuspense", variables]

export const useInfiniteGetCitiesQuery = <
  TData = InfiniteData<GetCitiesQuery>,
  TError = unknown,
>(
  variables: GetCitiesQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetCitiesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetCitiesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetCitiesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getCities.infinite"]
            : ["getCities.infinite", variables],
        queryFn: (metaData) =>
          fetcher<GetCitiesQuery, GetCitiesQueryVariables>(GetCitiesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetCitiesQuery.getKey = (variables?: GetCitiesQueryVariables) =>
  variables === undefined
    ? ["getCities.infinite"]
    : ["getCities.infinite", variables]

export const useSuspenseInfiniteGetCitiesQuery = <
  TData = InfiniteData<GetCitiesQuery>,
  TError = unknown,
>(
  variables: GetCitiesQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetCitiesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetCitiesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetCitiesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getCities.infiniteSuspense"]
            : ["getCities.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<GetCitiesQuery, GetCitiesQueryVariables>(GetCitiesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetCitiesQuery.getKey = (
  variables?: GetCitiesQueryVariables
) =>
  variables === undefined
    ? ["getCities.infiniteSuspense"]
    : ["getCities.infiniteSuspense", variables]

useGetCitiesQuery.fetcher = (
  variables?: GetCitiesQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetCitiesQuery, GetCitiesQueryVariables>(
    GetCitiesDocument,
    variables,
    options
  )

export const GetFilteredConsultantsDocument = `
    query getFilteredConsultants($city: String, $cityId: Int, $cityName: String, $service: Int, $serviceUrlKey: String, $serviceCategoryUrlKey: String, $professionsUrlKey: String, $page: Int, $itemsPerPage: Int, $isOfferFeatured: Boolean, $isRemote: Boolean, $isFeatured: Boolean, $tags: [Int]) {
  listConsultants(
    city_id: $cityId
    city: $city
    city_name: $cityName
    offers_consultingService_id: $service
    offers_consultingService_urlKey: $serviceUrlKey
    offers_consultingService_consultingServiceCategories_urlKey: $serviceCategoryUrlKey
    professions_urlKey: $professionsUrlKey
    page: $page
    itemsPerPage: $itemsPerPage
    isRemote: $isRemote
    isFeatured: $isFeatured
    order: {position: "ASC"}
    tags_id_list: $tags
  ) {
    collection {
      _id
      fullName
      avatar
      urlKey
      reviews {
        collection {
          createdAt
          rating
          createdAt
          description
        }
      }
      city {
        name
      }
      tags {
        collection {
          name
          _id
        }
      }
      offers(isFeatured: $isOfferFeatured, itemsPerPage: 250) {
        collection {
          price
          consultingService {
            name
            price
            urlKey
            _id
            consultingServiceCategories {
              collection {
                id
                _id
                name
                urlKey
                description
                isFeatured
                image
              }
            }
          }
        }
      }
      professions {
        collection {
          id
          _id
          name
          urlKey
        }
      }
    }
    paginationInfo {
      totalCount
      lastPage
      itemsPerPage
    }
  }
}
    `

export const useGetFilteredConsultantsQuery = <
  TData = GetFilteredConsultantsQuery,
  TError = unknown,
>(
  variables?: GetFilteredConsultantsQueryVariables,
  options?: Omit<
    UseQueryOptions<GetFilteredConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetFilteredConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetFilteredConsultantsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getFilteredConsultants"]
        : ["getFilteredConsultants", variables],
    queryFn: fetcher<
      GetFilteredConsultantsQuery,
      GetFilteredConsultantsQueryVariables
    >(GetFilteredConsultantsDocument, variables),
    ...options,
  })
}

useGetFilteredConsultantsQuery.getKey = (
  variables?: GetFilteredConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getFilteredConsultants"]
    : ["getFilteredConsultants", variables]

export const useSuspenseGetFilteredConsultantsQuery = <
  TData = GetFilteredConsultantsQuery,
  TError = unknown,
>(
  variables?: GetFilteredConsultantsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetFilteredConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetFilteredConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetFilteredConsultantsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getFilteredConsultantsSuspense"]
        : ["getFilteredConsultantsSuspense", variables],
    queryFn: fetcher<
      GetFilteredConsultantsQuery,
      GetFilteredConsultantsQueryVariables
    >(GetFilteredConsultantsDocument, variables),
    ...options,
  })
}

useSuspenseGetFilteredConsultantsQuery.getKey = (
  variables?: GetFilteredConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getFilteredConsultantsSuspense"]
    : ["getFilteredConsultantsSuspense", variables]

export const useInfiniteGetFilteredConsultantsQuery = <
  TData = InfiniteData<GetFilteredConsultantsQuery>,
  TError = unknown,
>(
  variables: GetFilteredConsultantsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetFilteredConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetFilteredConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetFilteredConsultantsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFilteredConsultants.infinite"]
            : ["getFilteredConsultants.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFilteredConsultantsQuery,
            GetFilteredConsultantsQueryVariables
          >(GetFilteredConsultantsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetFilteredConsultantsQuery.getKey = (
  variables?: GetFilteredConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getFilteredConsultants.infinite"]
    : ["getFilteredConsultants.infinite", variables]

export const useSuspenseInfiniteGetFilteredConsultantsQuery = <
  TData = InfiniteData<GetFilteredConsultantsQuery>,
  TError = unknown,
>(
  variables: GetFilteredConsultantsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetFilteredConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetFilteredConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetFilteredConsultantsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFilteredConsultants.infiniteSuspense"]
            : ["getFilteredConsultants.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFilteredConsultantsQuery,
            GetFilteredConsultantsQueryVariables
          >(GetFilteredConsultantsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetFilteredConsultantsQuery.getKey = (
  variables?: GetFilteredConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getFilteredConsultants.infiniteSuspense"]
    : ["getFilteredConsultants.infiniteSuspense", variables]

useGetFilteredConsultantsQuery.fetcher = (
  variables?: GetFilteredConsultantsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetFilteredConsultantsQuery, GetFilteredConsultantsQueryVariables>(
    GetFilteredConsultantsDocument,
    variables,
    options
  )

export const GetConsultantDocument = `
    query getConsultant($urlKey: String!) {
  byUrlKeyConsultant(urlKey: $urlKey) {
    fullName
    avatar
    urlKey
    bio
    _id
    reviews {
      collection {
        rating
        createdAt
        description
        authorName
      }
    }
    city {
      name
    }
    tags {
      collection {
        name
      }
    }
    offers(itemsPerPage: 10000) {
      collection {
        price
        id
        consultingService {
          _id
          name
          urlKey
          price
          consultingServiceCategories {
            collection {
              id
              _id
              name
              urlKey
              description
              image
              isFeatured
            }
          }
        }
      }
    }
    professions {
      collection {
        id
        _id
        name
        urlKey
      }
    }
  }
}
    `

export const useGetConsultantQuery = <
  TData = GetConsultantQuery,
  TError = unknown,
>(
  variables: GetConsultantQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultantQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<GetConsultantQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<GetConsultantQuery, TError, TData>({
    queryKey: ["getConsultant", variables],
    queryFn: fetcher<GetConsultantQuery, GetConsultantQueryVariables>(
      GetConsultantDocument,
      variables
    ),
    ...options,
  })
}

useGetConsultantQuery.getKey = (variables: GetConsultantQueryVariables) => [
  "getConsultant",
  variables,
]

export const useSuspenseGetConsultantQuery = <
  TData = GetConsultantQuery,
  TError = unknown,
>(
  variables: GetConsultantQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultantQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultantQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultantQuery, TError, TData>({
    queryKey: ["getConsultantSuspense", variables],
    queryFn: fetcher<GetConsultantQuery, GetConsultantQueryVariables>(
      GetConsultantDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseGetConsultantQuery.getKey = (
  variables: GetConsultantQueryVariables
) => ["getConsultantSuspense", variables]

export const useInfiniteGetConsultantQuery = <
  TData = InfiniteData<GetConsultantQuery>,
  TError = unknown,
>(
  variables: GetConsultantQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultantQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultantQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultantQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? ["getConsultant.infinite", variables],
        queryFn: (metaData) =>
          fetcher<GetConsultantQuery, GetConsultantQueryVariables>(
            GetConsultantDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultantQuery.getKey = (
  variables: GetConsultantQueryVariables
) => ["getConsultant.infinite", variables]

export const useSuspenseInfiniteGetConsultantQuery = <
  TData = InfiniteData<GetConsultantQuery>,
  TError = unknown,
>(
  variables: GetConsultantQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetConsultantQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultantQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetConsultantQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? [
          "getConsultant.infiniteSuspense",
          variables,
        ],
        queryFn: (metaData) =>
          fetcher<GetConsultantQuery, GetConsultantQueryVariables>(
            GetConsultantDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultantQuery.getKey = (
  variables: GetConsultantQueryVariables
) => ["getConsultant.infiniteSuspense", variables]

useGetConsultantQuery.fetcher = (
  variables: GetConsultantQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetConsultantQuery, GetConsultantQueryVariables>(
    GetConsultantDocument,
    variables,
    options
  )

export const GetConsultantByTokenDocument = `
    query getConsultantByToken {
  byTokenConsultant {
    fullName
    email
    avatar
    urlKey
    _id
    id
    bio
    isRemote
    dateOfBirth
    fiscalCode
    taxId
    telephone
    city {
      name
      id
    }
    reviews {
      collection {
        rating
        createdAt
        description
        authorName
      }
    }
    tags {
      collection {
        name
        _id
      }
    }
    offers(itemsPerPage: 10000) {
      collection {
        price
        id
        consultingService {
          _id
          id
          name
          urlKey
          price
          consultingServiceCategories {
            collection {
              id
              _id
              name
              urlKey
              description
              isFeatured
              image
            }
          }
        }
      }
    }
    stripeSubscriptionId
    stripePartnerPlanSubscriptionId
    professions {
      collection {
        name
        urlKey
        id
        isPublished
        description
        image
        isFeatured
      }
    }
  }
}
    `

export const useGetConsultantByTokenQuery = <
  TData = GetConsultantByTokenQuery,
  TError = unknown,
>(
  variables?: GetConsultantByTokenQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultantByTokenQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultantByTokenQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultantByTokenQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultantByToken"]
        : ["getConsultantByToken", variables],
    queryFn: fetcher<
      GetConsultantByTokenQuery,
      GetConsultantByTokenQueryVariables
    >(GetConsultantByTokenDocument, variables),
    ...options,
  })
}

useGetConsultantByTokenQuery.getKey = (
  variables?: GetConsultantByTokenQueryVariables
) =>
  variables === undefined
    ? ["getConsultantByToken"]
    : ["getConsultantByToken", variables]

export const useSuspenseGetConsultantByTokenQuery = <
  TData = GetConsultantByTokenQuery,
  TError = unknown,
>(
  variables?: GetConsultantByTokenQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultantByTokenQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultantByTokenQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultantByTokenQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultantByTokenSuspense"]
        : ["getConsultantByTokenSuspense", variables],
    queryFn: fetcher<
      GetConsultantByTokenQuery,
      GetConsultantByTokenQueryVariables
    >(GetConsultantByTokenDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultantByTokenQuery.getKey = (
  variables?: GetConsultantByTokenQueryVariables
) =>
  variables === undefined
    ? ["getConsultantByTokenSuspense"]
    : ["getConsultantByTokenSuspense", variables]

export const useInfiniteGetConsultantByTokenQuery = <
  TData = InfiniteData<GetConsultantByTokenQuery>,
  TError = unknown,
>(
  variables: GetConsultantByTokenQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultantByTokenQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultantByTokenQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultantByTokenQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultantByToken.infinite"]
            : ["getConsultantByToken.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultantByTokenQuery,
            GetConsultantByTokenQueryVariables
          >(GetConsultantByTokenDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultantByTokenQuery.getKey = (
  variables?: GetConsultantByTokenQueryVariables
) =>
  variables === undefined
    ? ["getConsultantByToken.infinite"]
    : ["getConsultantByToken.infinite", variables]

export const useSuspenseInfiniteGetConsultantByTokenQuery = <
  TData = InfiniteData<GetConsultantByTokenQuery>,
  TError = unknown,
>(
  variables: GetConsultantByTokenQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetConsultantByTokenQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultantByTokenQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetConsultantByTokenQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultantByToken.infiniteSuspense"]
            : ["getConsultantByToken.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultantByTokenQuery,
            GetConsultantByTokenQueryVariables
          >(GetConsultantByTokenDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultantByTokenQuery.getKey = (
  variables?: GetConsultantByTokenQueryVariables
) =>
  variables === undefined
    ? ["getConsultantByToken.infiniteSuspense"]
    : ["getConsultantByToken.infiniteSuspense", variables]

useGetConsultantByTokenQuery.fetcher = (
  variables?: GetConsultantByTokenQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetConsultantByTokenQuery, GetConsultantByTokenQueryVariables>(
    GetConsultantByTokenDocument,
    variables,
    options
  )

export const CreateOrderDocument = `
    mutation createOrder($input: createOrderInput!) {
  createOrder(input: $input) {
    order {
      id
      consultant {
        id
      }
      price
      service {
        id
      }
    }
    clientMutationId
  }
}
    `

export const useCreateOrderMutation = <TError = unknown, TContext = unknown>(
  options?: UseMutationOptions<
    CreateOrderMutation,
    TError,
    CreateOrderMutationVariables,
    TContext
  >
) => {
  return useMutation<
    CreateOrderMutation,
    TError,
    CreateOrderMutationVariables,
    TContext
  >({
    mutationKey: ["createOrder"],
    mutationFn: (variables?: CreateOrderMutationVariables) =>
      fetcher<CreateOrderMutation, CreateOrderMutationVariables>(
        CreateOrderDocument,
        variables
      )(),
    ...options,
  })
}

useCreateOrderMutation.fetcher = (
  variables: CreateOrderMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<CreateOrderMutation, CreateOrderMutationVariables>(
    CreateOrderDocument,
    variables,
    options
  )

export const GetTopTenConsultantsDocument = `
    query getTopTenConsultants($page: Int, $itemsPerPage: Int) {
  listConsultants(
    page: $page
    itemsPerPage: $itemsPerPage
    order: {isFeatured: "DESC", position: "ASC"}
  ) {
    collection {
      fullName
      avatar
      urlKey
      city {
        name
      }
      reviews {
        collection {
          rating
        }
      }
    }
  }
}
    `

export const useGetTopTenConsultantsQuery = <
  TData = GetTopTenConsultantsQuery,
  TError = unknown,
>(
  variables?: GetTopTenConsultantsQueryVariables,
  options?: Omit<
    UseQueryOptions<GetTopTenConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetTopTenConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetTopTenConsultantsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getTopTenConsultants"]
        : ["getTopTenConsultants", variables],
    queryFn: fetcher<
      GetTopTenConsultantsQuery,
      GetTopTenConsultantsQueryVariables
    >(GetTopTenConsultantsDocument, variables),
    ...options,
  })
}

useGetTopTenConsultantsQuery.getKey = (
  variables?: GetTopTenConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getTopTenConsultants"]
    : ["getTopTenConsultants", variables]

export const useSuspenseGetTopTenConsultantsQuery = <
  TData = GetTopTenConsultantsQuery,
  TError = unknown,
>(
  variables?: GetTopTenConsultantsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetTopTenConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetTopTenConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetTopTenConsultantsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getTopTenConsultantsSuspense"]
        : ["getTopTenConsultantsSuspense", variables],
    queryFn: fetcher<
      GetTopTenConsultantsQuery,
      GetTopTenConsultantsQueryVariables
    >(GetTopTenConsultantsDocument, variables),
    ...options,
  })
}

useSuspenseGetTopTenConsultantsQuery.getKey = (
  variables?: GetTopTenConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getTopTenConsultantsSuspense"]
    : ["getTopTenConsultantsSuspense", variables]

export const useInfiniteGetTopTenConsultantsQuery = <
  TData = InfiniteData<GetTopTenConsultantsQuery>,
  TError = unknown,
>(
  variables: GetTopTenConsultantsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetTopTenConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetTopTenConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetTopTenConsultantsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getTopTenConsultants.infinite"]
            : ["getTopTenConsultants.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetTopTenConsultantsQuery,
            GetTopTenConsultantsQueryVariables
          >(GetTopTenConsultantsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetTopTenConsultantsQuery.getKey = (
  variables?: GetTopTenConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getTopTenConsultants.infinite"]
    : ["getTopTenConsultants.infinite", variables]

export const useSuspenseInfiniteGetTopTenConsultantsQuery = <
  TData = InfiniteData<GetTopTenConsultantsQuery>,
  TError = unknown,
>(
  variables: GetTopTenConsultantsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetTopTenConsultantsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetTopTenConsultantsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetTopTenConsultantsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getTopTenConsultants.infiniteSuspense"]
            : ["getTopTenConsultants.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetTopTenConsultantsQuery,
            GetTopTenConsultantsQueryVariables
          >(GetTopTenConsultantsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetTopTenConsultantsQuery.getKey = (
  variables?: GetTopTenConsultantsQueryVariables
) =>
  variables === undefined
    ? ["getTopTenConsultants.infiniteSuspense"]
    : ["getTopTenConsultants.infiniteSuspense", variables]

useGetTopTenConsultantsQuery.fetcher = (
  variables?: GetTopTenConsultantsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetTopTenConsultantsQuery, GetTopTenConsultantsQueryVariables>(
    GetTopTenConsultantsDocument,
    variables,
    options
  )

export const GetConsultantProfessionsDocument = `
    query getConsultantProfessions {
  consultantProfessions {
    collection {
      id
      _id
      isPublished
      isFeatured
      name
      urlKey
      image
      description
    }
  }
}
    `

export const useGetConsultantProfessionsQuery = <
  TData = GetConsultantProfessionsQuery,
  TError = unknown,
>(
  variables?: GetConsultantProfessionsQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultantProfessionsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultantProfessionsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultantProfessions"]
        : ["getConsultantProfessions", variables],
    queryFn: fetcher<
      GetConsultantProfessionsQuery,
      GetConsultantProfessionsQueryVariables
    >(GetConsultantProfessionsDocument, variables),
    ...options,
  })
}

useGetConsultantProfessionsQuery.getKey = (
  variables?: GetConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessions"]
    : ["getConsultantProfessions", variables]

export const useSuspenseGetConsultantProfessionsQuery = <
  TData = GetConsultantProfessionsQuery,
  TError = unknown,
>(
  variables?: GetConsultantProfessionsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultantProfessionsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultantProfessionsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultantProfessionsSuspense"]
        : ["getConsultantProfessionsSuspense", variables],
    queryFn: fetcher<
      GetConsultantProfessionsQuery,
      GetConsultantProfessionsQueryVariables
    >(GetConsultantProfessionsDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultantProfessionsQuery.getKey = (
  variables?: GetConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessionsSuspense"]
    : ["getConsultantProfessionsSuspense", variables]

export const useInfiniteGetConsultantProfessionsQuery = <
  TData = InfiniteData<GetConsultantProfessionsQuery>,
  TError = unknown,
>(
  variables: GetConsultantProfessionsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultantProfessionsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultantProfessionsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultantProfessions.infinite"]
            : ["getConsultantProfessions.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultantProfessionsQuery,
            GetConsultantProfessionsQueryVariables
          >(GetConsultantProfessionsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultantProfessionsQuery.getKey = (
  variables?: GetConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessions.infinite"]
    : ["getConsultantProfessions.infinite", variables]

export const useSuspenseInfiniteGetConsultantProfessionsQuery = <
  TData = InfiniteData<GetConsultantProfessionsQuery>,
  TError = unknown,
>(
  variables: GetConsultantProfessionsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetConsultantProfessionsQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetConsultantProfessionsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultantProfessions.infiniteSuspense"]
            : ["getConsultantProfessions.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultantProfessionsQuery,
            GetConsultantProfessionsQueryVariables
          >(GetConsultantProfessionsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultantProfessionsQuery.getKey = (
  variables?: GetConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessions.infiniteSuspense"]
    : ["getConsultantProfessions.infiniteSuspense", variables]

useGetConsultantProfessionsQuery.fetcher = (
  variables?: GetConsultantProfessionsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetConsultantProfessionsQuery,
    GetConsultantProfessionsQueryVariables
  >(GetConsultantProfessionsDocument, variables, options)

export const GetConsultantProfessionsAllDocument = `
    query getConsultantProfessionsAll {
  consultantProfessions(itemsPerPage: 1000) {
    collection {
      id
      _id
      isPublished
      name
      urlKey
    }
  }
}
    `

export const useGetConsultantProfessionsAllQuery = <
  TData = GetConsultantProfessionsAllQuery,
  TError = unknown,
>(
  variables?: GetConsultantProfessionsAllQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultantProfessionsAllQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultantProfessionsAllQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultantProfessionsAllQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultantProfessionsAll"]
        : ["getConsultantProfessionsAll", variables],
    queryFn: fetcher<
      GetConsultantProfessionsAllQuery,
      GetConsultantProfessionsAllQueryVariables
    >(GetConsultantProfessionsAllDocument, variables),
    ...options,
  })
}

useGetConsultantProfessionsAllQuery.getKey = (
  variables?: GetConsultantProfessionsAllQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessionsAll"]
    : ["getConsultantProfessionsAll", variables]

export const useSuspenseGetConsultantProfessionsAllQuery = <
  TData = GetConsultantProfessionsAllQuery,
  TError = unknown,
>(
  variables?: GetConsultantProfessionsAllQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultantProfessionsAllQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultantProfessionsAllQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultantProfessionsAllQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getConsultantProfessionsAllSuspense"]
        : ["getConsultantProfessionsAllSuspense", variables],
    queryFn: fetcher<
      GetConsultantProfessionsAllQuery,
      GetConsultantProfessionsAllQueryVariables
    >(GetConsultantProfessionsAllDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultantProfessionsAllQuery.getKey = (
  variables?: GetConsultantProfessionsAllQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessionsAllSuspense"]
    : ["getConsultantProfessionsAllSuspense", variables]

export const useInfiniteGetConsultantProfessionsAllQuery = <
  TData = InfiniteData<GetConsultantProfessionsAllQuery>,
  TError = unknown,
>(
  variables: GetConsultantProfessionsAllQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultantProfessionsAllQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultantProfessionsAllQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultantProfessionsAllQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultantProfessionsAll.infinite"]
            : ["getConsultantProfessionsAll.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultantProfessionsAllQuery,
            GetConsultantProfessionsAllQueryVariables
          >(GetConsultantProfessionsAllDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultantProfessionsAllQuery.getKey = (
  variables?: GetConsultantProfessionsAllQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessionsAll.infinite"]
    : ["getConsultantProfessionsAll.infinite", variables]

export const useSuspenseInfiniteGetConsultantProfessionsAllQuery = <
  TData = InfiniteData<GetConsultantProfessionsAllQuery>,
  TError = unknown,
>(
  variables: GetConsultantProfessionsAllQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetConsultantProfessionsAllQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultantProfessionsAllQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<
    GetConsultantProfessionsAllQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getConsultantProfessionsAll.infiniteSuspense"]
            : ["getConsultantProfessionsAll.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetConsultantProfessionsAllQuery,
            GetConsultantProfessionsAllQueryVariables
          >(GetConsultantProfessionsAllDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultantProfessionsAllQuery.getKey = (
  variables?: GetConsultantProfessionsAllQueryVariables
) =>
  variables === undefined
    ? ["getConsultantProfessionsAll.infiniteSuspense"]
    : ["getConsultantProfessionsAll.infiniteSuspense", variables]

useGetConsultantProfessionsAllQuery.fetcher = (
  variables?: GetConsultantProfessionsAllQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetConsultantProfessionsAllQuery,
    GetConsultantProfessionsAllQueryVariables
  >(GetConsultantProfessionsAllDocument, variables, options)

export const GetFeaturedConsultingServiceCategoriesDocument = `
    query getFeaturedConsultingServiceCategories {
  consultingServiceCategories(isFeatured: true) {
    collection {
      id
      name
      image
      isFeatured
      urlKey
    }
    paginationInfo {
      totalCount
    }
  }
}
    `

export const useGetFeaturedConsultingServiceCategoriesQuery = <
  TData = GetFeaturedConsultingServiceCategoriesQuery,
  TError = unknown,
>(
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables,
  options?: Omit<
    UseQueryOptions<GetFeaturedConsultingServiceCategoriesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetFeaturedConsultingServiceCategoriesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getFeaturedConsultingServiceCategories"]
        : ["getFeaturedConsultingServiceCategories", variables],
    queryFn: fetcher<
      GetFeaturedConsultingServiceCategoriesQuery,
      GetFeaturedConsultingServiceCategoriesQueryVariables
    >(GetFeaturedConsultingServiceCategoriesDocument, variables),
    ...options,
  })
}

useGetFeaturedConsultingServiceCategoriesQuery.getKey = (
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServiceCategories"]
    : ["getFeaturedConsultingServiceCategories", variables]

export const useSuspenseGetFeaturedConsultingServiceCategoriesQuery = <
  TData = GetFeaturedConsultingServiceCategoriesQuery,
  TError = unknown,
>(
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<
    GetFeaturedConsultingServiceCategoriesQuery,
    TError,
    TData
  >({
    queryKey:
      variables === undefined
        ? ["getFeaturedConsultingServiceCategoriesSuspense"]
        : ["getFeaturedConsultingServiceCategoriesSuspense", variables],
    queryFn: fetcher<
      GetFeaturedConsultingServiceCategoriesQuery,
      GetFeaturedConsultingServiceCategoriesQueryVariables
    >(GetFeaturedConsultingServiceCategoriesDocument, variables),
    ...options,
  })
}

useSuspenseGetFeaturedConsultingServiceCategoriesQuery.getKey = (
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServiceCategoriesSuspense"]
    : ["getFeaturedConsultingServiceCategoriesSuspense", variables]

export const useInfiniteGetFeaturedConsultingServiceCategoriesQuery = <
  TData = InfiniteData<GetFeaturedConsultingServiceCategoriesQuery>,
  TError = unknown,
>(
  variables: GetFeaturedConsultingServiceCategoriesQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<
    GetFeaturedConsultingServiceCategoriesQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFeaturedConsultingServiceCategories.infinite"]
            : ["getFeaturedConsultingServiceCategories.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFeaturedConsultingServiceCategoriesQuery,
            GetFeaturedConsultingServiceCategoriesQueryVariables
          >(GetFeaturedConsultingServiceCategoriesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetFeaturedConsultingServiceCategoriesQuery.getKey = (
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServiceCategories.infinite"]
    : ["getFeaturedConsultingServiceCategories.infinite", variables]

export const useSuspenseInfiniteGetFeaturedConsultingServiceCategoriesQuery = <
  TData = InfiniteData<GetFeaturedConsultingServiceCategoriesQuery>,
  TError = unknown,
>(
  variables: GetFeaturedConsultingServiceCategoriesQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetFeaturedConsultingServiceCategoriesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<
    GetFeaturedConsultingServiceCategoriesQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFeaturedConsultingServiceCategories.infiniteSuspense"]
            : [
                "getFeaturedConsultingServiceCategories.infiniteSuspense",
                variables,
              ],
        queryFn: (metaData) =>
          fetcher<
            GetFeaturedConsultingServiceCategoriesQuery,
            GetFeaturedConsultingServiceCategoriesQueryVariables
          >(GetFeaturedConsultingServiceCategoriesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetFeaturedConsultingServiceCategoriesQuery.getKey = (
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServiceCategories.infiniteSuspense"]
    : ["getFeaturedConsultingServiceCategories.infiniteSuspense", variables]

useGetFeaturedConsultingServiceCategoriesQuery.fetcher = (
  variables?: GetFeaturedConsultingServiceCategoriesQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetFeaturedConsultingServiceCategoriesQuery,
    GetFeaturedConsultingServiceCategoriesQueryVariables
  >(GetFeaturedConsultingServiceCategoriesDocument, variables, options)

export const GetFeaturedConsultantProfessionsDocument = `
    query getFeaturedConsultantProfessions {
  consultantProfessions(isFeatured: true) {
    collection {
      id
      name
      isFeatured
      urlKey
      image
    }
    paginationInfo {
      totalCount
    }
  }
}
    `

export const useGetFeaturedConsultantProfessionsQuery = <
  TData = GetFeaturedConsultantProfessionsQuery,
  TError = unknown,
>(
  variables?: GetFeaturedConsultantProfessionsQueryVariables,
  options?: Omit<
    UseQueryOptions<GetFeaturedConsultantProfessionsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetFeaturedConsultantProfessionsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getFeaturedConsultantProfessions"]
        : ["getFeaturedConsultantProfessions", variables],
    queryFn: fetcher<
      GetFeaturedConsultantProfessionsQuery,
      GetFeaturedConsultantProfessionsQueryVariables
    >(GetFeaturedConsultantProfessionsDocument, variables),
    ...options,
  })
}

useGetFeaturedConsultantProfessionsQuery.getKey = (
  variables?: GetFeaturedConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultantProfessions"]
    : ["getFeaturedConsultantProfessions", variables]

export const useSuspenseGetFeaturedConsultantProfessionsQuery = <
  TData = GetFeaturedConsultantProfessionsQuery,
  TError = unknown,
>(
  variables?: GetFeaturedConsultantProfessionsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetFeaturedConsultantProfessionsQuery, TError, TData>(
    {
      queryKey:
        variables === undefined
          ? ["getFeaturedConsultantProfessionsSuspense"]
          : ["getFeaturedConsultantProfessionsSuspense", variables],
      queryFn: fetcher<
        GetFeaturedConsultantProfessionsQuery,
        GetFeaturedConsultantProfessionsQueryVariables
      >(GetFeaturedConsultantProfessionsDocument, variables),
      ...options,
    }
  )
}

useSuspenseGetFeaturedConsultantProfessionsQuery.getKey = (
  variables?: GetFeaturedConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultantProfessionsSuspense"]
    : ["getFeaturedConsultantProfessionsSuspense", variables]

export const useInfiniteGetFeaturedConsultantProfessionsQuery = <
  TData = InfiniteData<GetFeaturedConsultantProfessionsQuery>,
  TError = unknown,
>(
  variables: GetFeaturedConsultantProfessionsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetFeaturedConsultantProfessionsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFeaturedConsultantProfessions.infinite"]
            : ["getFeaturedConsultantProfessions.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFeaturedConsultantProfessionsQuery,
            GetFeaturedConsultantProfessionsQueryVariables
          >(GetFeaturedConsultantProfessionsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetFeaturedConsultantProfessionsQuery.getKey = (
  variables?: GetFeaturedConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultantProfessions.infinite"]
    : ["getFeaturedConsultantProfessions.infinite", variables]

export const useSuspenseInfiniteGetFeaturedConsultantProfessionsQuery = <
  TData = InfiniteData<GetFeaturedConsultantProfessionsQuery>,
  TError = unknown,
>(
  variables: GetFeaturedConsultantProfessionsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetFeaturedConsultantProfessionsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<
    GetFeaturedConsultantProfessionsQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFeaturedConsultantProfessions.infiniteSuspense"]
            : ["getFeaturedConsultantProfessions.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFeaturedConsultantProfessionsQuery,
            GetFeaturedConsultantProfessionsQueryVariables
          >(GetFeaturedConsultantProfessionsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetFeaturedConsultantProfessionsQuery.getKey = (
  variables?: GetFeaturedConsultantProfessionsQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultantProfessions.infiniteSuspense"]
    : ["getFeaturedConsultantProfessions.infiniteSuspense", variables]

useGetFeaturedConsultantProfessionsQuery.fetcher = (
  variables?: GetFeaturedConsultantProfessionsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetFeaturedConsultantProfessionsQuery,
    GetFeaturedConsultantProfessionsQueryVariables
  >(GetFeaturedConsultantProfessionsDocument, variables, options)

export const GetFeaturedConsultingServicesDocument = `
    query getFeaturedConsultingServices {
  consultingServices(isFeatured: true) {
    collection {
      id
      name
      isFeatured
      urlKey
      image
    }
    paginationInfo {
      totalCount
    }
  }
}
    `

export const useGetFeaturedConsultingServicesQuery = <
  TData = GetFeaturedConsultingServicesQuery,
  TError = unknown,
>(
  variables?: GetFeaturedConsultingServicesQueryVariables,
  options?: Omit<
    UseQueryOptions<GetFeaturedConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetFeaturedConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetFeaturedConsultingServicesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getFeaturedConsultingServices"]
        : ["getFeaturedConsultingServices", variables],
    queryFn: fetcher<
      GetFeaturedConsultingServicesQuery,
      GetFeaturedConsultingServicesQueryVariables
    >(GetFeaturedConsultingServicesDocument, variables),
    ...options,
  })
}

useGetFeaturedConsultingServicesQuery.getKey = (
  variables?: GetFeaturedConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServices"]
    : ["getFeaturedConsultingServices", variables]

export const useSuspenseGetFeaturedConsultingServicesQuery = <
  TData = GetFeaturedConsultingServicesQuery,
  TError = unknown,
>(
  variables?: GetFeaturedConsultingServicesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetFeaturedConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetFeaturedConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetFeaturedConsultingServicesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getFeaturedConsultingServicesSuspense"]
        : ["getFeaturedConsultingServicesSuspense", variables],
    queryFn: fetcher<
      GetFeaturedConsultingServicesQuery,
      GetFeaturedConsultingServicesQueryVariables
    >(GetFeaturedConsultingServicesDocument, variables),
    ...options,
  })
}

useSuspenseGetFeaturedConsultingServicesQuery.getKey = (
  variables?: GetFeaturedConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServicesSuspense"]
    : ["getFeaturedConsultingServicesSuspense", variables]

export const useInfiniteGetFeaturedConsultingServicesQuery = <
  TData = InfiniteData<GetFeaturedConsultingServicesQuery>,
  TError = unknown,
>(
  variables: GetFeaturedConsultingServicesQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetFeaturedConsultingServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetFeaturedConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetFeaturedConsultingServicesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFeaturedConsultingServices.infinite"]
            : ["getFeaturedConsultingServices.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFeaturedConsultingServicesQuery,
            GetFeaturedConsultingServicesQueryVariables
          >(GetFeaturedConsultingServicesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetFeaturedConsultingServicesQuery.getKey = (
  variables?: GetFeaturedConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServices.infinite"]
    : ["getFeaturedConsultingServices.infinite", variables]

export const useSuspenseInfiniteGetFeaturedConsultingServicesQuery = <
  TData = InfiniteData<GetFeaturedConsultingServicesQuery>,
  TError = unknown,
>(
  variables: GetFeaturedConsultingServicesQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetFeaturedConsultingServicesQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetFeaturedConsultingServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<
    GetFeaturedConsultingServicesQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getFeaturedConsultingServices.infiniteSuspense"]
            : ["getFeaturedConsultingServices.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            GetFeaturedConsultingServicesQuery,
            GetFeaturedConsultingServicesQueryVariables
          >(GetFeaturedConsultingServicesDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetFeaturedConsultingServicesQuery.getKey = (
  variables?: GetFeaturedConsultingServicesQueryVariables
) =>
  variables === undefined
    ? ["getFeaturedConsultingServices.infiniteSuspense"]
    : ["getFeaturedConsultingServices.infiniteSuspense", variables]

useGetFeaturedConsultingServicesQuery.fetcher = (
  variables?: GetFeaturedConsultingServicesQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetFeaturedConsultingServicesQuery,
    GetFeaturedConsultingServicesQueryVariables
  >(GetFeaturedConsultingServicesDocument, variables, options)

export const CreateContactRequestDocument = `
    mutation createContactRequest($input: createContactRequestInput!) {
  createContactRequest(input: $input) {
    contactRequest {
      id
    }
  }
}
    `

export const useCreateContactRequestMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    CreateContactRequestMutation,
    TError,
    CreateContactRequestMutationVariables,
    TContext
  >
) => {
  return useMutation<
    CreateContactRequestMutation,
    TError,
    CreateContactRequestMutationVariables,
    TContext
  >({
    mutationKey: ["createContactRequest"],
    mutationFn: (variables?: CreateContactRequestMutationVariables) =>
      fetcher<
        CreateContactRequestMutation,
        CreateContactRequestMutationVariables
      >(CreateContactRequestDocument, variables)(),
    ...options,
  })
}

useCreateContactRequestMutation.fetcher = (
  variables: CreateContactRequestMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<CreateContactRequestMutation, CreateContactRequestMutationVariables>(
    CreateContactRequestDocument,
    variables,
    options
  )

export const CreateToAdminContactRequestDocument = `
    mutation createToAdminContactRequest($input: createToAdminContactRequestInput!) {
  createToAdminContactRequest(input: $input) {
    clientMutationId
  }
}
    `

export const useCreateToAdminContactRequestMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    CreateToAdminContactRequestMutation,
    TError,
    CreateToAdminContactRequestMutationVariables,
    TContext
  >
) => {
  return useMutation<
    CreateToAdminContactRequestMutation,
    TError,
    CreateToAdminContactRequestMutationVariables,
    TContext
  >({
    mutationKey: ["createToAdminContactRequest"],
    mutationFn: (variables?: CreateToAdminContactRequestMutationVariables) =>
      fetcher<
        CreateToAdminContactRequestMutation,
        CreateToAdminContactRequestMutationVariables
      >(CreateToAdminContactRequestDocument, variables)(),
    ...options,
  })
}

useCreateToAdminContactRequestMutation.fetcher = (
  variables: CreateToAdminContactRequestMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    CreateToAdminContactRequestMutation,
    CreateToAdminContactRequestMutationVariables
  >(CreateToAdminContactRequestDocument, variables, options)

export const CreateConsultantDocument = `
    mutation createConsultant($input: createConsultantInput!) {
  createConsultant(input: $input) {
    consultant {
      fullName
    }
  }
}
    `

export const useCreateConsultantMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    CreateConsultantMutation,
    TError,
    CreateConsultantMutationVariables,
    TContext
  >
) => {
  return useMutation<
    CreateConsultantMutation,
    TError,
    CreateConsultantMutationVariables,
    TContext
  >({
    mutationKey: ["createConsultant"],
    mutationFn: (variables?: CreateConsultantMutationVariables) =>
      fetcher<CreateConsultantMutation, CreateConsultantMutationVariables>(
        CreateConsultantDocument,
        variables
      )(),
    ...options,
  })
}

useCreateConsultantMutation.fetcher = (
  variables: CreateConsultantMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<CreateConsultantMutation, CreateConsultantMutationVariables>(
    CreateConsultantDocument,
    variables,
    options
  )

export const LoginConsultantDocument = `
    mutation loginConsultant($input: loginConsultantAuthInput!) {
  loginConsultantAuth(input: $input) {
    consultantAuth {
      token
      id
    }
  }
}
    `

export const useLoginConsultantMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    LoginConsultantMutation,
    TError,
    LoginConsultantMutationVariables,
    TContext
  >
) => {
  return useMutation<
    LoginConsultantMutation,
    TError,
    LoginConsultantMutationVariables,
    TContext
  >({
    mutationKey: ["loginConsultant"],
    mutationFn: (variables?: LoginConsultantMutationVariables) =>
      fetcher<LoginConsultantMutation, LoginConsultantMutationVariables>(
        LoginConsultantDocument,
        variables
      )(),
    ...options,
  })
}

useLoginConsultantMutation.fetcher = (
  variables: LoginConsultantMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<LoginConsultantMutation, LoginConsultantMutationVariables>(
    LoginConsultantDocument,
    variables,
    options
  )

export const ChangePasswordByTokenConsultantDocument = `
    mutation changePasswordByTokenConsultant($input: changePasswordByTokenConsultantInput!) {
  changePasswordByTokenConsultant(input: $input) {
    consultant {
      id
    }
  }
}
    `

export const useChangePasswordByTokenConsultantMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    ChangePasswordByTokenConsultantMutation,
    TError,
    ChangePasswordByTokenConsultantMutationVariables,
    TContext
  >
) => {
  return useMutation<
    ChangePasswordByTokenConsultantMutation,
    TError,
    ChangePasswordByTokenConsultantMutationVariables,
    TContext
  >({
    mutationKey: ["changePasswordByTokenConsultant"],
    mutationFn: (
      variables?: ChangePasswordByTokenConsultantMutationVariables
    ) =>
      fetcher<
        ChangePasswordByTokenConsultantMutation,
        ChangePasswordByTokenConsultantMutationVariables
      >(ChangePasswordByTokenConsultantDocument, variables)(),
    ...options,
  })
}

useChangePasswordByTokenConsultantMutation.fetcher = (
  variables: ChangePasswordByTokenConsultantMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    ChangePasswordByTokenConsultantMutation,
    ChangePasswordByTokenConsultantMutationVariables
  >(ChangePasswordByTokenConsultantDocument, variables, options)

export const GetTagsDocument = `
    query getTags {
  tags {
    collection {
      name
      _id
    }
  }
}
    `

export const useGetTagsQuery = <TData = GetTagsQuery, TError = unknown>(
  variables?: GetTagsQueryVariables,
  options?: Omit<UseQueryOptions<GetTagsQuery, TError, TData>, "queryKey"> & {
    queryKey?: UseQueryOptions<GetTagsQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<GetTagsQuery, TError, TData>({
    queryKey: variables === undefined ? ["getTags"] : ["getTags", variables],
    queryFn: fetcher<GetTagsQuery, GetTagsQueryVariables>(
      GetTagsDocument,
      variables
    ),
    ...options,
  })
}

useGetTagsQuery.getKey = (variables?: GetTagsQueryVariables) =>
  variables === undefined ? ["getTags"] : ["getTags", variables]

export const useSuspenseGetTagsQuery = <TData = GetTagsQuery, TError = unknown>(
  variables?: GetTagsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetTagsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<GetTagsQuery, TError, TData>["queryKey"]
  }
) => {
  return useSuspenseQuery<GetTagsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getTagsSuspense"]
        : ["getTagsSuspense", variables],
    queryFn: fetcher<GetTagsQuery, GetTagsQueryVariables>(
      GetTagsDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseGetTagsQuery.getKey = (variables?: GetTagsQueryVariables) =>
  variables === undefined ? ["getTagsSuspense"] : ["getTagsSuspense", variables]

export const useInfiniteGetTagsQuery = <
  TData = InfiniteData<GetTagsQuery>,
  TError = unknown,
>(
  variables: GetTagsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetTagsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<GetTagsQuery, TError, TData>["queryKey"]
  }
) => {
  return useInfiniteQuery<GetTagsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getTags.infinite"]
            : ["getTags.infinite", variables],
        queryFn: (metaData) =>
          fetcher<GetTagsQuery, GetTagsQueryVariables>(GetTagsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetTagsQuery.getKey = (variables?: GetTagsQueryVariables) =>
  variables === undefined
    ? ["getTags.infinite"]
    : ["getTags.infinite", variables]

export const useSuspenseInfiniteGetTagsQuery = <
  TData = InfiniteData<GetTagsQuery>,
  TError = unknown,
>(
  variables: GetTagsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetTagsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetTagsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetTagsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getTags.infiniteSuspense"]
            : ["getTags.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<GetTagsQuery, GetTagsQueryVariables>(GetTagsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetTagsQuery.getKey = (variables?: GetTagsQueryVariables) =>
  variables === undefined
    ? ["getTags.infiniteSuspense"]
    : ["getTags.infiniteSuspense", variables]

useGetTagsQuery.fetcher = (
  variables?: GetTagsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetTagsQuery, GetTagsQueryVariables>(
    GetTagsDocument,
    variables,
    options
  )

export const AssignTagsToConsultantDocument = `
    mutation assignTagsToConsultant($input: assignTagsToConsultantInput!) {
  assignTagsToConsultant(input: $input) {
    consultant {
      id
    }
  }
}
    `

export const useAssignTagsToConsultantMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    AssignTagsToConsultantMutation,
    TError,
    AssignTagsToConsultantMutationVariables,
    TContext
  >
) => {
  return useMutation<
    AssignTagsToConsultantMutation,
    TError,
    AssignTagsToConsultantMutationVariables,
    TContext
  >({
    mutationKey: ["assignTagsToConsultant"],
    mutationFn: (variables?: AssignTagsToConsultantMutationVariables) =>
      fetcher<
        AssignTagsToConsultantMutation,
        AssignTagsToConsultantMutationVariables
      >(AssignTagsToConsultantDocument, variables)(),
    ...options,
  })
}

useAssignTagsToConsultantMutation.fetcher = (
  variables: AssignTagsToConsultantMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    AssignTagsToConsultantMutation,
    AssignTagsToConsultantMutationVariables
  >(AssignTagsToConsultantDocument, variables, options)

export const AssignOffersToConsultantDocument = `
    mutation assignOffersToConsultant($input: assignOffersToConsultantInput!) {
  assignOffersToConsultant(input: $input) {
    consultant {
      id
    }
  }
}
    `

export const useAssignOffersToConsultantMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    AssignOffersToConsultantMutation,
    TError,
    AssignOffersToConsultantMutationVariables,
    TContext
  >
) => {
  return useMutation<
    AssignOffersToConsultantMutation,
    TError,
    AssignOffersToConsultantMutationVariables,
    TContext
  >({
    mutationKey: ["assignOffersToConsultant"],
    mutationFn: (variables?: AssignOffersToConsultantMutationVariables) =>
      fetcher<
        AssignOffersToConsultantMutation,
        AssignOffersToConsultantMutationVariables
      >(AssignOffersToConsultantDocument, variables)(),
    ...options,
  })
}

useAssignOffersToConsultantMutation.fetcher = (
  variables: AssignOffersToConsultantMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    AssignOffersToConsultantMutation,
    AssignOffersToConsultantMutationVariables
  >(AssignOffersToConsultantDocument, variables, options)

export const UpdateConsultantDocument = `
    mutation updateConsultant($input: updateConsultantInput!) {
  updateConsultant(input: $input) {
    consultant {
      id
    }
  }
}
    `

export const useUpdateConsultantMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    UpdateConsultantMutation,
    TError,
    UpdateConsultantMutationVariables,
    TContext
  >
) => {
  return useMutation<
    UpdateConsultantMutation,
    TError,
    UpdateConsultantMutationVariables,
    TContext
  >({
    mutationKey: ["updateConsultant"],
    mutationFn: (variables?: UpdateConsultantMutationVariables) =>
      fetcher<UpdateConsultantMutation, UpdateConsultantMutationVariables>(
        UpdateConsultantDocument,
        variables
      )(),
    ...options,
  })
}

useUpdateConsultantMutation.fetcher = (
  variables: UpdateConsultantMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<UpdateConsultantMutation, UpdateConsultantMutationVariables>(
    UpdateConsultantDocument,
    variables,
    options
  )

export const GetAppointmentsDocument = `
    query getAppointments {
  myAppointments {
    collection {
      id
      scheduledAt
      customer {
        telephone
        email
        fullName
      }
      customerOrder {
        price
        service {
          name
        }
      }
      paymentStatus
    }
  }
}
    `

export const useGetAppointmentsQuery = <
  TData = GetAppointmentsQuery,
  TError = unknown,
>(
  variables?: GetAppointmentsQueryVariables,
  options?: Omit<
    UseQueryOptions<GetAppointmentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<GetAppointmentsQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<GetAppointmentsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getAppointments"]
        : ["getAppointments", variables],
    queryFn: fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
      GetAppointmentsDocument,
      variables
    ),
    ...options,
  })
}

useGetAppointmentsQuery.getKey = (variables?: GetAppointmentsQueryVariables) =>
  variables === undefined ? ["getAppointments"] : ["getAppointments", variables]

export const useSuspenseGetAppointmentsQuery = <
  TData = GetAppointmentsQuery,
  TError = unknown,
>(
  variables?: GetAppointmentsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetAppointmentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetAppointmentsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetAppointmentsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getAppointmentsSuspense"]
        : ["getAppointmentsSuspense", variables],
    queryFn: fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
      GetAppointmentsDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseGetAppointmentsQuery.getKey = (
  variables?: GetAppointmentsQueryVariables
) =>
  variables === undefined
    ? ["getAppointmentsSuspense"]
    : ["getAppointmentsSuspense", variables]

export const useInfiniteGetAppointmentsQuery = <
  TData = InfiniteData<GetAppointmentsQuery>,
  TError = unknown,
>(
  variables: GetAppointmentsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetAppointmentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetAppointmentsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetAppointmentsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getAppointments.infinite"]
            : ["getAppointments.infinite", variables],
        queryFn: (metaData) =>
          fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
            GetAppointmentsDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetAppointmentsQuery.getKey = (
  variables?: GetAppointmentsQueryVariables
) =>
  variables === undefined
    ? ["getAppointments.infinite"]
    : ["getAppointments.infinite", variables]

export const useSuspenseInfiniteGetAppointmentsQuery = <
  TData = InfiniteData<GetAppointmentsQuery>,
  TError = unknown,
>(
  variables: GetAppointmentsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetAppointmentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetAppointmentsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetAppointmentsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getAppointments.infiniteSuspense"]
            : ["getAppointments.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
            GetAppointmentsDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetAppointmentsQuery.getKey = (
  variables?: GetAppointmentsQueryVariables
) =>
  variables === undefined
    ? ["getAppointments.infiniteSuspense"]
    : ["getAppointments.infiniteSuspense", variables]

useGetAppointmentsQuery.fetcher = (
  variables?: GetAppointmentsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetAppointmentsQuery, GetAppointmentsQueryVariables>(
    GetAppointmentsDocument,
    variables,
    options
  )

export const GetAgreementsDocument = `
    query getAgreements {
  agreements {
    collection {
      id
      content
    }
  }
}
    `

export const useGetAgreementsQuery = <
  TData = GetAgreementsQuery,
  TError = unknown,
>(
  variables?: GetAgreementsQueryVariables,
  options?: Omit<
    UseQueryOptions<GetAgreementsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<GetAgreementsQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<GetAgreementsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getAgreements"]
        : ["getAgreements", variables],
    queryFn: fetcher<GetAgreementsQuery, GetAgreementsQueryVariables>(
      GetAgreementsDocument,
      variables
    ),
    ...options,
  })
}

useGetAgreementsQuery.getKey = (variables?: GetAgreementsQueryVariables) =>
  variables === undefined ? ["getAgreements"] : ["getAgreements", variables]

export const useSuspenseGetAgreementsQuery = <
  TData = GetAgreementsQuery,
  TError = unknown,
>(
  variables?: GetAgreementsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetAgreementsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetAgreementsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetAgreementsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["getAgreementsSuspense"]
        : ["getAgreementsSuspense", variables],
    queryFn: fetcher<GetAgreementsQuery, GetAgreementsQueryVariables>(
      GetAgreementsDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseGetAgreementsQuery.getKey = (
  variables?: GetAgreementsQueryVariables
) =>
  variables === undefined
    ? ["getAgreementsSuspense"]
    : ["getAgreementsSuspense", variables]

export const useInfiniteGetAgreementsQuery = <
  TData = InfiniteData<GetAgreementsQuery>,
  TError = unknown,
>(
  variables: GetAgreementsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetAgreementsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetAgreementsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetAgreementsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getAgreements.infinite"]
            : ["getAgreements.infinite", variables],
        queryFn: (metaData) =>
          fetcher<GetAgreementsQuery, GetAgreementsQueryVariables>(
            GetAgreementsDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetAgreementsQuery.getKey = (
  variables?: GetAgreementsQueryVariables
) =>
  variables === undefined
    ? ["getAgreements.infinite"]
    : ["getAgreements.infinite", variables]

export const useSuspenseInfiniteGetAgreementsQuery = <
  TData = InfiniteData<GetAgreementsQuery>,
  TError = unknown,
>(
  variables: GetAgreementsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetAgreementsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetAgreementsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetAgreementsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["getAgreements.infiniteSuspense"]
            : ["getAgreements.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<GetAgreementsQuery, GetAgreementsQueryVariables>(
            GetAgreementsDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetAgreementsQuery.getKey = (
  variables?: GetAgreementsQueryVariables
) =>
  variables === undefined
    ? ["getAgreements.infiniteSuspense"]
    : ["getAgreements.infiniteSuspense", variables]

useGetAgreementsQuery.fetcher = (
  variables?: GetAgreementsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetAgreementsQuery, GetAgreementsQueryVariables>(
    GetAgreementsDocument,
    variables,
    options
  )

export const GetTitleFromPathDocument = `
    query getTitleFromPath($id: ID!) {
  url(id: $id) {
    title
    bodyContent
  }
}
    `

export const useGetTitleFromPathQuery = <
  TData = GetTitleFromPathQuery,
  TError = unknown,
>(
  variables: GetTitleFromPathQueryVariables,
  options?: Omit<
    UseQueryOptions<GetTitleFromPathQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<GetTitleFromPathQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<GetTitleFromPathQuery, TError, TData>({
    queryKey: ["getTitleFromPath", variables],
    queryFn: fetcher<GetTitleFromPathQuery, GetTitleFromPathQueryVariables>(
      GetTitleFromPathDocument,
      variables
    ),
    ...options,
  })
}

useGetTitleFromPathQuery.getKey = (
  variables: GetTitleFromPathQueryVariables
) => ["getTitleFromPath", variables]

export const useSuspenseGetTitleFromPathQuery = <
  TData = GetTitleFromPathQuery,
  TError = unknown,
>(
  variables: GetTitleFromPathQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetTitleFromPathQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetTitleFromPathQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetTitleFromPathQuery, TError, TData>({
    queryKey: ["getTitleFromPathSuspense", variables],
    queryFn: fetcher<GetTitleFromPathQuery, GetTitleFromPathQueryVariables>(
      GetTitleFromPathDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseGetTitleFromPathQuery.getKey = (
  variables: GetTitleFromPathQueryVariables
) => ["getTitleFromPathSuspense", variables]

export const useInfiniteGetTitleFromPathQuery = <
  TData = InfiniteData<GetTitleFromPathQuery>,
  TError = unknown,
>(
  variables: GetTitleFromPathQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetTitleFromPathQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetTitleFromPathQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetTitleFromPathQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? ["getTitleFromPath.infinite", variables],
        queryFn: (metaData) =>
          fetcher<GetTitleFromPathQuery, GetTitleFromPathQueryVariables>(
            GetTitleFromPathDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetTitleFromPathQuery.getKey = (
  variables: GetTitleFromPathQueryVariables
) => ["getTitleFromPath.infinite", variables]

export const useSuspenseInfiniteGetTitleFromPathQuery = <
  TData = InfiniteData<GetTitleFromPathQuery>,
  TError = unknown,
>(
  variables: GetTitleFromPathQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<GetTitleFromPathQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetTitleFromPathQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<GetTitleFromPathQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? [
          "getTitleFromPath.infiniteSuspense",
          variables,
        ],
        queryFn: (metaData) =>
          fetcher<GetTitleFromPathQuery, GetTitleFromPathQueryVariables>(
            GetTitleFromPathDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetTitleFromPathQuery.getKey = (
  variables: GetTitleFromPathQueryVariables
) => ["getTitleFromPath.infiniteSuspense", variables]

useGetTitleFromPathQuery.fetcher = (
  variables: GetTitleFromPathQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<GetTitleFromPathQuery, GetTitleFromPathQueryVariables>(
    GetTitleFromPathDocument,
    variables,
    options
  )

export const CreateConsultantReviewDocument = `
    mutation createConsultantReview($appointmentHash: String!, $description: String!, $rating: Int!) {
  createConsultantReview(
    input: {appointmentHash: $appointmentHash, description: $description, rating: $rating}
  ) {
    consultantReview {
      id
    }
  }
}
    `

export const useCreateConsultantReviewMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    CreateConsultantReviewMutation,
    TError,
    CreateConsultantReviewMutationVariables,
    TContext
  >
) => {
  return useMutation<
    CreateConsultantReviewMutation,
    TError,
    CreateConsultantReviewMutationVariables,
    TContext
  >({
    mutationKey: ["createConsultantReview"],
    mutationFn: (variables?: CreateConsultantReviewMutationVariables) =>
      fetcher<
        CreateConsultantReviewMutation,
        CreateConsultantReviewMutationVariables
      >(CreateConsultantReviewDocument, variables)(),
    ...options,
  })
}

useCreateConsultantReviewMutation.fetcher = (
  variables: CreateConsultantReviewMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    CreateConsultantReviewMutation,
    CreateConsultantReviewMutationVariables
  >(CreateConsultantReviewDocument, variables, options)

export const ValidateByAppointmentHashConsultantReviewDocument = `
    mutation validateByAppointmentHashConsultantReview($appointmentHash: String!) {
  validateByAppointmentHashConsultantReview(
    input: {appointmentHash: $appointmentHash}
  ) {
    consultantReview {
      id
    }
  }
}
    `

export const useValidateByAppointmentHashConsultantReviewMutation = <
  TError = unknown,
  TContext = unknown,
>(
  options?: UseMutationOptions<
    ValidateByAppointmentHashConsultantReviewMutation,
    TError,
    ValidateByAppointmentHashConsultantReviewMutationVariables,
    TContext
  >
) => {
  return useMutation<
    ValidateByAppointmentHashConsultantReviewMutation,
    TError,
    ValidateByAppointmentHashConsultantReviewMutationVariables,
    TContext
  >({
    mutationKey: ["validateByAppointmentHashConsultantReview"],
    mutationFn: (
      variables?: ValidateByAppointmentHashConsultantReviewMutationVariables
    ) =>
      fetcher<
        ValidateByAppointmentHashConsultantReviewMutation,
        ValidateByAppointmentHashConsultantReviewMutationVariables
      >(ValidateByAppointmentHashConsultantReviewDocument, variables)(),
    ...options,
  })
}

useValidateByAppointmentHashConsultantReviewMutation.fetcher = (
  variables: ValidateByAppointmentHashConsultantReviewMutationVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    ValidateByAppointmentHashConsultantReviewMutation,
    ValidateByAppointmentHashConsultantReviewMutationVariables
  >(ValidateByAppointmentHashConsultantReviewDocument, variables, options)

export const SettingByCodeSettingDocument = `
    query settingByCodeSetting {
  settingByCodeSetting(code: "consultantCommissionAmount") {
    code
    value
  }
}
    `

export const useSettingByCodeSettingQuery = <
  TData = SettingByCodeSettingQuery,
  TError = unknown,
>(
  variables?: SettingByCodeSettingQueryVariables,
  options?: Omit<
    UseQueryOptions<SettingByCodeSettingQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      SettingByCodeSettingQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<SettingByCodeSettingQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["settingByCodeSetting"]
        : ["settingByCodeSetting", variables],
    queryFn: fetcher<
      SettingByCodeSettingQuery,
      SettingByCodeSettingQueryVariables
    >(SettingByCodeSettingDocument, variables),
    ...options,
  })
}

useSettingByCodeSettingQuery.getKey = (
  variables?: SettingByCodeSettingQueryVariables
) =>
  variables === undefined
    ? ["settingByCodeSetting"]
    : ["settingByCodeSetting", variables]

export const useSuspenseSettingByCodeSettingQuery = <
  TData = SettingByCodeSettingQuery,
  TError = unknown,
>(
  variables?: SettingByCodeSettingQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<SettingByCodeSettingQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      SettingByCodeSettingQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<SettingByCodeSettingQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["settingByCodeSettingSuspense"]
        : ["settingByCodeSettingSuspense", variables],
    queryFn: fetcher<
      SettingByCodeSettingQuery,
      SettingByCodeSettingQueryVariables
    >(SettingByCodeSettingDocument, variables),
    ...options,
  })
}

useSuspenseSettingByCodeSettingQuery.getKey = (
  variables?: SettingByCodeSettingQueryVariables
) =>
  variables === undefined
    ? ["settingByCodeSettingSuspense"]
    : ["settingByCodeSettingSuspense", variables]

export const useInfiniteSettingByCodeSettingQuery = <
  TData = InfiniteData<SettingByCodeSettingQuery>,
  TError = unknown,
>(
  variables: SettingByCodeSettingQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<SettingByCodeSettingQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      SettingByCodeSettingQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<SettingByCodeSettingQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["settingByCodeSetting.infinite"]
            : ["settingByCodeSetting.infinite", variables],
        queryFn: (metaData) =>
          fetcher<
            SettingByCodeSettingQuery,
            SettingByCodeSettingQueryVariables
          >(SettingByCodeSettingDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteSettingByCodeSettingQuery.getKey = (
  variables?: SettingByCodeSettingQueryVariables
) =>
  variables === undefined
    ? ["settingByCodeSetting.infinite"]
    : ["settingByCodeSetting.infinite", variables]

export const useSuspenseInfiniteSettingByCodeSettingQuery = <
  TData = InfiniteData<SettingByCodeSettingQuery>,
  TError = unknown,
>(
  variables: SettingByCodeSettingQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<SettingByCodeSettingQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      SettingByCodeSettingQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<SettingByCodeSettingQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["settingByCodeSetting.infiniteSuspense"]
            : ["settingByCodeSetting.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<
            SettingByCodeSettingQuery,
            SettingByCodeSettingQueryVariables
          >(SettingByCodeSettingDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteSettingByCodeSettingQuery.getKey = (
  variables?: SettingByCodeSettingQueryVariables
) =>
  variables === undefined
    ? ["settingByCodeSetting.infiniteSuspense"]
    : ["settingByCodeSetting.infiniteSuspense", variables]

useSettingByCodeSettingQuery.fetcher = (
  variables?: SettingByCodeSettingQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<SettingByCodeSettingQuery, SettingByCodeSettingQueryVariables>(
    SettingByCodeSettingDocument,
    variables,
    options
  )

export const ContentsDocument = `
    query contents($page: Int, $itemsPerPage: Int, $consultantProfession_name: String, $consultantProfession_name_list: [String], $consultantProfession_id: Int, $consultantProfession_id_list: [Int], $consultantService_id: Int, $consultantService_id_list: [Int], $city_name: String, $city_name_list: [String], $city: String, $city_list: [String], $city_id: Int, $city_id_list: [Int], $consultantService_urlKey: String, $consultantService_urlKey_list: [String], $consultantProfession_urlKey: String, $consultantProfession_urlKey_list: [String]) {
  contents(
    page: $page
    itemsPerPage: $itemsPerPage
    consultantProfession_name: $consultantProfession_name
    consultantProfession_name_list: $consultantProfession_name_list
    consultantProfession_id: $consultantProfession_id
    consultantProfession_id_list: $consultantProfession_id_list
    consultantService_id: $consultantService_id
    consultantService_id_list: $consultantService_id_list
    city_name: $city_name
    city_name_list: $city_name_list
    city: $city
    city_list: $city_list
    city_id: $city_id
    city_id_list: $city_id_list
    consultantService_urlKey: $consultantService_urlKey
    consultantService_urlKey_list: $consultantService_urlKey_list
    consultantProfession_urlKey: $consultantProfession_urlKey
    consultantProfession_urlKey_list: $consultantProfession_urlKey_list
  ) {
    collection {
      id
      _id
      createdAt
      updatedAt
      lastUpdateByAIAt
      title
      body
      city {
        id
        name
        urlKey
      }
      consultantProfession {
        id
        _id
        name
        urlKey
        isPublished
        description
        image
        isFeatured
      }
      consultantService {
        id
        _id
        name
        price
        urlKey
        isFeatured
        description
        image
      }
      faqHTML
      metadataTitle
      metadataDescription
    }
    paginationInfo {
      itemsPerPage
      lastPage
      totalCount
      hasNextPage
    }
  }
}
    `

export const useContentsQuery = <TData = ContentsQuery, TError = unknown>(
  variables?: ContentsQueryVariables,
  options?: Omit<UseQueryOptions<ContentsQuery, TError, TData>, "queryKey"> & {
    queryKey?: UseQueryOptions<ContentsQuery, TError, TData>["queryKey"]
  }
) => {
  return useQuery<ContentsQuery, TError, TData>({
    queryKey: variables === undefined ? ["contents"] : ["contents", variables],
    queryFn: fetcher<ContentsQuery, ContentsQueryVariables>(
      ContentsDocument,
      variables
    ),
    ...options,
  })
}

useContentsQuery.getKey = (variables?: ContentsQueryVariables) =>
  variables === undefined ? ["contents"] : ["contents", variables]

export const useSuspenseContentsQuery = <
  TData = ContentsQuery,
  TError = unknown,
>(
  variables?: ContentsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<ContentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<ContentsQuery, TError, TData>["queryKey"]
  }
) => {
  return useSuspenseQuery<ContentsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ["contentsSuspense"]
        : ["contentsSuspense", variables],
    queryFn: fetcher<ContentsQuery, ContentsQueryVariables>(
      ContentsDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseContentsQuery.getKey = (variables?: ContentsQueryVariables) =>
  variables === undefined
    ? ["contentsSuspense"]
    : ["contentsSuspense", variables]

export const useInfiniteContentsQuery = <
  TData = InfiniteData<ContentsQuery>,
  TError = unknown,
>(
  variables: ContentsQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<ContentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<ContentsQuery, TError, TData>["queryKey"]
  }
) => {
  return useInfiniteQuery<ContentsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["contents.infinite"]
            : ["contents.infinite", variables],
        queryFn: (metaData) =>
          fetcher<ContentsQuery, ContentsQueryVariables>(ContentsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteContentsQuery.getKey = (variables?: ContentsQueryVariables) =>
  variables === undefined
    ? ["contents.infinite"]
    : ["contents.infinite", variables]

export const useSuspenseInfiniteContentsQuery = <
  TData = InfiniteData<ContentsQuery>,
  TError = unknown,
>(
  variables: ContentsQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<ContentsQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      ContentsQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<ContentsQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey:
          (optionsQueryKey ?? variables === undefined)
            ? ["contents.infiniteSuspense"]
            : ["contents.infiniteSuspense", variables],
        queryFn: (metaData) =>
          fetcher<ContentsQuery, ContentsQueryVariables>(ContentsDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteContentsQuery.getKey = (
  variables?: ContentsQueryVariables
) =>
  variables === undefined
    ? ["contents.infiniteSuspense"]
    : ["contents.infiniteSuspense", variables]

useContentsQuery.fetcher = (
  variables?: ContentsQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<ContentsQuery, ContentsQueryVariables>(
    ContentsDocument,
    variables,
    options
  )

export const RatingsForServicesDocument = `
    query ratingsForServices($serviceId: Int!) {
  statsByServiceIdServiceReviewStatistic(serviceId: $serviceId) {
    averageRating
    reviewCount
    bestRating
    worstRating
    service {
      name
    }
  }
}
    `

export const useRatingsForServicesQuery = <
  TData = RatingsForServicesQuery,
  TError = unknown,
>(
  variables: RatingsForServicesQueryVariables,
  options?: Omit<
    UseQueryOptions<RatingsForServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      RatingsForServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<RatingsForServicesQuery, TError, TData>({
    queryKey: ["ratingsForServices", variables],
    queryFn: fetcher<RatingsForServicesQuery, RatingsForServicesQueryVariables>(
      RatingsForServicesDocument,
      variables
    ),
    ...options,
  })
}

useRatingsForServicesQuery.getKey = (
  variables: RatingsForServicesQueryVariables
) => ["ratingsForServices", variables]

export const useSuspenseRatingsForServicesQuery = <
  TData = RatingsForServicesQuery,
  TError = unknown,
>(
  variables: RatingsForServicesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<RatingsForServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      RatingsForServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<RatingsForServicesQuery, TError, TData>({
    queryKey: ["ratingsForServicesSuspense", variables],
    queryFn: fetcher<RatingsForServicesQuery, RatingsForServicesQueryVariables>(
      RatingsForServicesDocument,
      variables
    ),
    ...options,
  })
}

useSuspenseRatingsForServicesQuery.getKey = (
  variables: RatingsForServicesQueryVariables
) => ["ratingsForServicesSuspense", variables]

export const useInfiniteRatingsForServicesQuery = <
  TData = InfiniteData<RatingsForServicesQuery>,
  TError = unknown,
>(
  variables: RatingsForServicesQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<RatingsForServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      RatingsForServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<RatingsForServicesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? ["ratingsForServices.infinite", variables],
        queryFn: (metaData) =>
          fetcher<RatingsForServicesQuery, RatingsForServicesQueryVariables>(
            RatingsForServicesDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteRatingsForServicesQuery.getKey = (
  variables: RatingsForServicesQueryVariables
) => ["ratingsForServices.infinite", variables]

export const useSuspenseInfiniteRatingsForServicesQuery = <
  TData = InfiniteData<RatingsForServicesQuery>,
  TError = unknown,
>(
  variables: RatingsForServicesQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<RatingsForServicesQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      RatingsForServicesQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<RatingsForServicesQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? [
          "ratingsForServices.infiniteSuspense",
          variables,
        ],
        queryFn: (metaData) =>
          fetcher<RatingsForServicesQuery, RatingsForServicesQueryVariables>(
            RatingsForServicesDocument,
            { ...variables, ...(metaData.pageParam ?? {}) }
          )(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteRatingsForServicesQuery.getKey = (
  variables: RatingsForServicesQueryVariables
) => ["ratingsForServices.infiniteSuspense", variables]

useRatingsForServicesQuery.fetcher = (
  variables: RatingsForServicesQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<RatingsForServicesQuery, RatingsForServicesQueryVariables>(
    RatingsForServicesDocument,
    variables,
    options
  )

export const GetConsultingServiceIdByUrlKeyDocument = `
    query GetConsultingServiceIdByUrlKey($urlKey: String!) {
  consultingServices(offers_consultingService_urlKey: $urlKey, itemsPerPage: 1) {
    collection {
      _id
    }
  }
}
    `

export const useGetConsultingServiceIdByUrlKeyQuery = <
  TData = GetConsultingServiceIdByUrlKeyQuery,
  TError = unknown,
>(
  variables: GetConsultingServiceIdByUrlKeyQueryVariables,
  options?: Omit<
    UseQueryOptions<GetConsultingServiceIdByUrlKeyQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseQueryOptions<
      GetConsultingServiceIdByUrlKeyQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useQuery<GetConsultingServiceIdByUrlKeyQuery, TError, TData>({
    queryKey: ["GetConsultingServiceIdByUrlKey", variables],
    queryFn: fetcher<
      GetConsultingServiceIdByUrlKeyQuery,
      GetConsultingServiceIdByUrlKeyQueryVariables
    >(GetConsultingServiceIdByUrlKeyDocument, variables),
    ...options,
  })
}

useGetConsultingServiceIdByUrlKeyQuery.getKey = (
  variables: GetConsultingServiceIdByUrlKeyQueryVariables
) => ["GetConsultingServiceIdByUrlKey", variables]

export const useSuspenseGetConsultingServiceIdByUrlKeyQuery = <
  TData = GetConsultingServiceIdByUrlKeyQuery,
  TError = unknown,
>(
  variables: GetConsultingServiceIdByUrlKeyQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<GetConsultingServiceIdByUrlKeyQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseSuspenseQueryOptions<
      GetConsultingServiceIdByUrlKeyQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseQuery<GetConsultingServiceIdByUrlKeyQuery, TError, TData>({
    queryKey: ["GetConsultingServiceIdByUrlKeySuspense", variables],
    queryFn: fetcher<
      GetConsultingServiceIdByUrlKeyQuery,
      GetConsultingServiceIdByUrlKeyQueryVariables
    >(GetConsultingServiceIdByUrlKeyDocument, variables),
    ...options,
  })
}

useSuspenseGetConsultingServiceIdByUrlKeyQuery.getKey = (
  variables: GetConsultingServiceIdByUrlKeyQueryVariables
) => ["GetConsultingServiceIdByUrlKeySuspense", variables]

export const useInfiniteGetConsultingServiceIdByUrlKeyQuery = <
  TData = InfiniteData<GetConsultingServiceIdByUrlKeyQuery>,
  TError = unknown,
>(
  variables: GetConsultingServiceIdByUrlKeyQueryVariables,
  options: Omit<
    UseInfiniteQueryOptions<GetConsultingServiceIdByUrlKeyQuery, TError, TData>,
    "queryKey"
  > & {
    queryKey?: UseInfiniteQueryOptions<
      GetConsultingServiceIdByUrlKeyQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useInfiniteQuery<GetConsultingServiceIdByUrlKeyQuery, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? [
          "GetConsultingServiceIdByUrlKey.infinite",
          variables,
        ],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingServiceIdByUrlKeyQuery,
            GetConsultingServiceIdByUrlKeyQueryVariables
          >(GetConsultingServiceIdByUrlKeyDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useInfiniteGetConsultingServiceIdByUrlKeyQuery.getKey = (
  variables: GetConsultingServiceIdByUrlKeyQueryVariables
) => ["GetConsultingServiceIdByUrlKey.infinite", variables]

export const useSuspenseInfiniteGetConsultingServiceIdByUrlKeyQuery = <
  TData = InfiniteData<GetConsultingServiceIdByUrlKeyQuery>,
  TError = unknown,
>(
  variables: GetConsultingServiceIdByUrlKeyQueryVariables,
  options: Omit<
    UseSuspenseInfiniteQueryOptions<
      GetConsultingServiceIdByUrlKeyQuery,
      TError,
      TData
    >,
    "queryKey"
  > & {
    queryKey?: UseSuspenseInfiniteQueryOptions<
      GetConsultingServiceIdByUrlKeyQuery,
      TError,
      TData
    >["queryKey"]
  }
) => {
  return useSuspenseInfiniteQuery<
    GetConsultingServiceIdByUrlKeyQuery,
    TError,
    TData
  >(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options
      return {
        queryKey: optionsQueryKey ?? [
          "GetConsultingServiceIdByUrlKey.infiniteSuspense",
          variables,
        ],
        queryFn: (metaData) =>
          fetcher<
            GetConsultingServiceIdByUrlKeyQuery,
            GetConsultingServiceIdByUrlKeyQueryVariables
          >(GetConsultingServiceIdByUrlKeyDocument, {
            ...variables,
            ...(metaData.pageParam ?? {}),
          })(),
        ...restOptions,
      }
    })()
  )
}

useSuspenseInfiniteGetConsultingServiceIdByUrlKeyQuery.getKey = (
  variables: GetConsultingServiceIdByUrlKeyQueryVariables
) => ["GetConsultingServiceIdByUrlKey.infiniteSuspense", variables]

useGetConsultingServiceIdByUrlKeyQuery.fetcher = (
  variables: GetConsultingServiceIdByUrlKeyQueryVariables,
  options?: RequestInit["headers"]
) =>
  fetcher<
    GetConsultingServiceIdByUrlKeyQuery,
    GetConsultingServiceIdByUrlKeyQueryVariables
  >(GetConsultingServiceIdByUrlKeyDocument, variables, options)
