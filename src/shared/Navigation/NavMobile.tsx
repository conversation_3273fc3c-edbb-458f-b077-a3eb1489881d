"use client"

import React from "react"
import Link from "next/link"
import { openChat } from "@/utils/HubspotInteractions"
import ncNanoId from "@/utils/ncNanoId"
import { Disclosure } from "@headlessui/react"
import { ChevronDownIcon } from "@heroicons/react/24/solid"

import { useMenuItems } from "@/hooks/useMenuItems"
import ButtonClose from "@/shared/ButtonClose"
import Logo from "@/shared/Logo"
import SocialsList from "@/shared/SocialsList"
import { Button } from "@/components/ui/button"

import { NavItemType } from "./NavigationItem"

export interface NavMobileProps {
  data?: NavItemType[]
  onClickClose?: () => void
}

export interface RenderChatItemProps {
  onClickClose?: () => void
}

const RenderChatItem: React.FC<RenderChatItemProps> = ({ onClickClose }) => {
  return (
    <Disclosure as="li" className="text-neutral-900 dark:text-white">
      <Button
        className="flex w-full rounded-lg px-4 text-sm font-medium uppercase tracking-wide hover:bg-neutral-100 dark:hover:bg-neutral-800"
        onClick={() => {
          openChat()
          onClickClose && onClickClose()
        }}
      >
        <span className={`block w-full py-2.5 pr-3`}>Chiedi aiuto</span>
      </Button>
    </Disclosure>
  )
}

const NavMobile: React.FC<NavMobileProps> = ({ onClickClose }) => {
  const menuItems = useMenuItems()

  const _renderMenuChild = (item: NavItemType) => {
    return (
      <ul className="nav-mobile-sub-menu pb-1 pl-6 text-base">
        {item.children?.map((i, index) => (
          <Disclosure key={i.href + index} as="li">
            <Link
              href={{
                pathname: i.href || undefined,
              }}
              className="mt-0.5 flex rounded-lg px-4 text-sm font-medium text-neutral-900 hover:bg-neutral-100 dark:text-neutral-200 dark:hover:bg-neutral-800"
            >
              <span
                className={`py-2.5 pr-3 ${!i.children ? "block w-full" : ""}`}
              >
                {i.name}
              </span>
              {i.children && (
                <span
                  className="flex flex-1"
                  onClick={(e) => e.preventDefault()}
                >
                  <Disclosure.Button
                    as="span"
                    className="flex flex-1 justify-end py-2.5"
                  >
                    <ChevronDownIcon
                      className="ml-2 h-4 w-4 text-neutral-500"
                      aria-hidden="true"
                    />
                  </Disclosure.Button>
                </span>
              )}
            </Link>
            {i.children && (
              <Disclosure.Panel>{_renderMenuChild(i)}</Disclosure.Panel>
            )}
          </Disclosure>
        ))}
      </ul>
    )
  }

  const _renderItem = (item: NavItemType) => {
    return (
      <Disclosure
        key={item.id}
        as="li"
        className="text-neutral-900 dark:text-white"
      >
        <Link
          className="flex w-full rounded-lg px-4 text-sm font-medium uppercase tracking-wide hover:bg-neutral-100 dark:hover:bg-neutral-800"
          href={{
            pathname: item.href || undefined,
          }}
          scroll
          onClick={onClickClose}
        >
          <span
            className={`py-2.5 pr-3 ${!item.children ? "block w-full" : ""}`}
          >
            {item.name}
          </span>
          {item.children && (
            <span className="flex flex-1" onClick={(e) => e.preventDefault()}>
              <Disclosure.Button
                as="span"
                className="flex flex-1 items-center justify-end py-2.5 "
              >
                <ChevronDownIcon
                  className="ml-2 h-4 w-4 text-neutral-500"
                  aria-hidden="true"
                />
              </Disclosure.Button>
            </span>
          )}
        </Link>
        {item.children && (
          <Disclosure.Panel>{_renderMenuChild(item)}</Disclosure.Panel>
        )}
      </Disclosure>
    )
  }

  return (
    <div className="h-screen w-full transform divide-y-2 divide-neutral-100 overflow-y-auto bg-white py-2 shadow-lg ring-1 transition dark:divide-neutral-800 dark:bg-neutral-900 dark:ring-neutral-700">
      <div className="px-5 py-6">
        <Logo className="w-36" />
        <div className="mt-5 flex flex-col text-sm text-neutral-700 dark:text-neutral-300">
          <div className="mt-4 flex items-center justify-between">
            <SocialsList itemClass="w-9 h-9 flex items-center justify-center rounded-full bg-neutral-100 text-xl dark:bg-neutral-800 dark:text-neutral-300" />
          </div>
        </div>
        <span className="absolute right-2 top-2 p-1">
          <ButtonClose onClick={onClickClose} />
        </span>
      </div>
      <ul className="flex flex-col space-y-1 px-2 py-6">
        {menuItems.map(_renderItem)}
        <RenderChatItem onClickClose={onClickClose} />
      </ul>
    </div>
  )
}

export default NavMobile
