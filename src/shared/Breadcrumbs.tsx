import React, { type FC } from "react"
import Link from "next/link"
import { ChevronRightIcon } from "@heroicons/react/24/outline"
import { BreadcrumbJsonLd, type BreadCrumbJsonLdProps } from "next-seo"

import { cn } from "@/lib/utils"

export interface BreadcrumbProps {
  items: { label: string; path: string }[] | undefined
}

const Breadcrumb: FC<BreadcrumbProps> = ({ items }) => {
  const itemsForBreadcrumb: BreadCrumbJsonLdProps["itemListElements"] =
    items ? items.map((item, index) => {
      return {
        position: index + 1,
        name: item.label,
        item: item.path,
      }
    }) : []

  if (!items || items?.length <= 1) {
    return null;
  }
  return (
    <nav
      className="flex w-full overflow-hidden rounded-lg  px-0 py-3 capitalize text-gray-700 dark:border-gray-700 dark:bg-gray-800 sm:w-fit"
      aria-label="Breadcrumb"
    >
      <ol className="inline-flex items-center space-x-1 overflow-hidden rtl:space-x-reverse md:space-x-2">
        {items?.map((item, index) => {
          const isLast = index === items.length - 1
          const isFirst = index === 0
          return (
            <li
              key={item.path}
              className={cn(isLast && "overflow-hidden", "flex items-center")}
            >
              {index > 0 && (
                <ChevronRightIcon
                  className="mx-1 h-3 w-3 shrink-0 text-gray-400"
                  aria-hidden="true"
                />
              )}
              {isLast ? (
                <span
                  className={cn(
                    "truncate text-sm font-medium capitalize text-gray-500 dark:text-gray-400 sm:max-w-fit",
                    !isFirst && "ms-1 md:ms-2"
                  )}
                >
                  {item.label}
                </span>
              ) : (
                <Link
                  href={item.path || "#"}
                  className={cn(
                    "shrink-0 truncate text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white",
                    !isFirst && "ms-1 md:ms-2"
                  )}
                >
                  {item.label}
                </Link>
              )}
            </li>
          )
        })}
      </ol>
      <BreadcrumbJsonLd itemListElements={itemsForBreadcrumb} useAppDir={true} />
    </nav>
  )
}

export default Breadcrumb
