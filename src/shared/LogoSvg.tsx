const LogoSvg = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.2"
      viewBox="0 0 1000 322"
    >
      <path
        d="M94.1 207.4v-17.7c0-14.4-9.6-24.1-23.8-24.1-14.3 0-23.9 9.7-23.9 24.1v83.5c0 16.9 10 27.8 25.4 27.8 6.2 0 12.7-1.9 20.5-6l.9-.5v-10.3l-2.6 1.4c-7.4 4.1-13.2 5.9-18.5 5.9-9.9 0-16-7-16-18.3v-83.5c0-8.9 5.5-14.6 14.2-14.6 8.6 0 14.2 5.7 14.2 14.6v17.7h9.6z"
        fill="#e0a305"
      />
      <path
        fillRule="evenodd"
        d="M152.1 226.8V277c0 14.3-9.7 24-24.3 24-14.5 0-24.4-9.7-24.4-24v-50.2c0-14.4 9.9-24 24.4-24 14.5 0 24.3 9.6 24.3 24zm-24.3-14.6c-8.8 0-14.5 5.8-14.5 14.6V277c0 8.8 5.8 14.5 14.6 14.5 8.7 0 14.4-5.7 14.4-14.5v-50.2c0-8.8-5.6-14.6-14.4-14.6z"
        fill="#e0a305"
      />
      <path
        d="M196.8 202.8c-6.5 0-12 2.2-15.9 6.1v-5.2H171v96.4h9.9v-73.3c0-9 5.5-14.6 14.4-14.6 8.8 0 14.4 5.8 14.4 14.6v73.3h9.8v-73.3c0-14.4-9.2-24-22.8-24zm66.5 44.3-.6-.3c-8.6-4.9-15.9-9.2-15.9-19.2 0-9.5 5.1-15.4 13.3-15.4 8.2 0 13.1 5.8 13.1 14.6v7h9.8v-7c0-14.4-9.2-24-22.9-24s-23.1 9.7-23.1 24.8c0 15.8 10.9 22 20.5 27.5 8.7 5.1 17 9.9 17 21.1 0 9.5-5.5 15.3-14.2 15.3-8.8 0-14.3-5.7-14.3-14.5v-7h-9.6v7c0 14.3 9.6 24 23.9 24 14.2 0 23.8-9.7 23.8-24.8 0-16.9-11.1-23.4-20.9-29.1zm75.6-43.4V277c0 8.8-5.6 14.6-14.4 14.6-8.7 0-14.4-5.6-14.4-14.6v-73.3h-9.8V277c0 14.3 9.1 24 22.5 24 6.6 0 12.1-2.2 16.1-6.2v5.3h9.8v-96.4h-9.8zm31.2-46.4h9.8v142.8h-9.8z"
        fill="#e0a305"
      />
      <path
        fillRule="evenodd"
        d="M408.5 258.5v14.7c0 11.3 6.3 18.3 16.5 18.3 5.3 0 11.3-1.8 18.6-5.9l2.6-1.4v10.3l-.9.5c-7.8 4.1-14.4 6-20.9 6-15.6 0-25.7-10.9-25.7-27.8v-46.4c0-14.4 9.9-24 24.4-24 14.5 0 24.2 9.6 24.2 24v28.6zm29-11.6v-20.1c0-8.9-5.5-14.6-14.4-14.6-8.8 0-14.6 5.7-14.6 14.6v22.4z"
        fill="#e0a305"
      />
      <path
        d="M490.2 202.8c-6.5 0-12 2.2-15.9 6.1v-5.2h-9.8v96.4h9.8v-73.3c0-9 5.5-14.6 14.4-14.6s14.4 5.8 14.4 14.6v73.3h9.8v-73.3c0-14.4-9.1-24-22.8-24zm75.9 83.6c-5.4 3.4-9.9 5.1-13.2 5.1-5.3 0-8.8-4.9-8.8-12.5v-65.8h22.7v-9.5h-22.7v-18.2h-9.8V279c0 13.2 6.9 22 17.3 22 4.7 0 10.5-1.8 16.3-5.1l.9-.5v-10.6l-2.7 1.6z"
        fill="#e0a305"
      />
      <path
        fillRule="evenodd"
        d="M589.6 258.5v14.7c0 11.3 6.3 18.3 16.4 18.3 5.4 0 11.3-1.8 18.7-5.9l2.6-1.4v10.3l-.9.5c-7.8 4.1-14.4 6-20.9 6-15.6 0-25.7-10.9-25.7-27.8v-46.4c0-14.4 9.9-24 24.4-24 14.5 0 24.2 9.6 24.2 24v28.6zm29-11.6v-20.1c0-8.9-5.6-14.6-14.4-14.6-8.9 0-14.6 5.7-14.6 14.6v22.4z"
        fill="#e0a305"
      />
      <path d="M646.4 166.6h9.9v133.5h-9.9z" fill="#e0a305" />
      <path
        fillRule="evenodd"
        d="M724.6 157.3v142.8h-9.8v-5.3c-4 4-9.6 6.2-16.1 6.2-13.6 0-22.7-9.7-22.7-24v-50.2c0-14.4 9.1-24 22.7-24 6.5 0 12.1 2.2 16.1 6.2v-51.7zm-24.2 54.9c-8.9 0-14.6 5.8-14.6 14.6V277c0 8.8 5.8 14.6 14.6 14.6 8.7 0 14.4-5.8 14.4-14.6v-50.2c0-8.8-5.6-14.6-14.4-14.6zm52.8 46.3v14.7c0 11.3 6.3 18.3 16.5 18.3 5.3 0 11.2-1.8 18.6-5.9l2.6-1.4v10.3l-.9.5c-7.8 4.1-14.4 6-20.9 6-15.6 0-25.7-10.9-25.7-27.8v-46.4c0-14.4 9.9-24 24.4-24 14.5 0 24.2 9.6 24.2 24v28.6zm29-11.6v-20.1c0-8.9-5.5-14.6-14.4-14.6s-14.6 5.7-14.6 14.6v22.4zm49.1-44.1c14.2 0 23.7 9.7 23.7 24v73.3h-9.8v-5.6c-4.3 4.2-10.2 6.5-17.2 6.5-13.3 0-22.6-8.7-22.6-21.1v-8.7c0-13 8.9-22.3 23.2-24.4l16.6-2.4v-17.6c0-8.8-5.5-14.6-13.9-14.6s-13.8 5.8-13.8 14.6v8.5h-9.9v-8.5c0-14.4 9.6-24 23.7-24zm-1 53c-9.5 1.5-15.1 7.1-15.1 15.2v8.7c0 7 5.9 11.8 14.4 11.8 9.6 0 15.5-5.6 15.5-14.6v-23.2l-14.8 2.1z"
        fill="#e0a305"
      />
      <path d="M876.3 157.3h9.9v142.8h-9.9z" fill="#e0a305" />
      <path
        fillRule="evenodd"
        d="M914.8 258.5v14.7c0 11.3 6.3 18.3 16.4 18.3 5.4 0 11.3-1.8 18.7-5.9l2.6-1.4v10.3l-1 .5c-7.7 4.1-14.3 6-20.8 6-15.7 0-25.8-10.9-25.8-27.8v-46.4c0-14.4 10-24 24.5-24 14.4 0 24.2 9.6 24.2 24v28.6zm29-11.6v-20.1c0-8.9-5.6-14.6-14.4-14.6-8.9 0-14.6 5.7-14.6 14.6v22.4z"
        fill="#e0a305"
      />
      <path
        d="M593.8 76.5q4.8-10.5-3.1-2c-15.5 16.8-35.8 33.9-61 20.9q-18.2-9.4-27.1-28.6-.9-1.9-1.8-.1c-7.7 15.2-16.3 32.4-32.1 39.4-14.2 6.4-35.2-9.6-46-17.7-.7-.5-1.7-.4-2.2.3-.3.5-.4 1.1-.1 1.6q13.1 25.6 8 54c-.1.8.4 1.5 1.2 1.6.3.1.6 0 .8-.1 27.2-13.5 114.7-39.5 138.1-12.5q1.8 2.1-.9 1.7c-51.6-7.5-97.3 4.5-142.6 28.8-1 .6-2.2.5-3.2-.1l-9.7-6.1q-1.7-1-1.1-3c8-27.6 5.6-46.4-4-72.2-6.2-16.6-18.3-32.3-25.7-50.1q-1-2.3 1.4-1.7c15.4 4.2 21.8 21.3 28.4 34.1 6.2 11.8 23.8 34.3 37.6 34.7 23 .5 42.1-48.5 44.2-65.8q.8-6.9 1.9-10.9.6-2.6 2.8-1.3l9.8 5.8q1.9 1.1 2.1 3.3c1.8 16.6 4.2 38.2 13.2 52.4 21.3 33.8 77.8-43.7 89.4-60.1q1.4-2 3.3-.5l10 7.3c1 .8 1.4 2.1 1.1 3.3q-6.2 19-28.5 78.1-2.6 6.8-6.7 29.3-.3 1.7-2 1.2-15.5-4.7-11.1-20.8 7-25.3 15.6-44.2z"
        fill="#e0a305"
      />
    </svg>
  )
}

export default LogoSvg
