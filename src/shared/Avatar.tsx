import React, { <PERSON> } from "react"
import Image, { StaticImageData } from "next/image"
import { UserIcon } from "@heroicons/react/24/outline"
import { XMarkIcon } from "@heroicons/react/24/solid"

export interface AvatarProps {
  containerClassName?: string
  sizeClass?: string
  radius?: string
  imgUrl?: string | null | StaticImageData
  customUrl?: string
  userName?: string
  hasChecked?: boolean
  hasCheckedClass?: string
  hasClose?: boolean
  handleClose?: () => void
}

const Avatar: FC<AvatarProps> = ({
  containerClassName = "ring-1 ring-white dark:ring-neutral-900",
  sizeClass = "h-6 w-6 text-sm",
  radius = "rounded-full",
  imgUrl,
  customUrl,
  userName,
  hasChecked,
  hasClose,
  handleClose,
  hasCheckedClass = "w-4 h-4 -top-0.5 -right-0.5",
}) => {
  const url = imgUrl
  const name = userName

  return (
    <div
      className={`wil-avatar relative inline-flex flex-shrink-0 items-center justify-center font-semibold uppercase text-neutral-100 shadow-inner ${radius} ${sizeClass} ${containerClassName}`}
    >
      {url ? (
        <Image
          className={`absolute inset-0 h-full w-full object-cover ${radius}`}
          src={`${process.env.NEXT_PUBLIC_BACKEND_MEDIA_URL}/avatars/${url}`}
          alt={name || "Avatar"}
          width={180}
          height={180}
        />
      ) : customUrl ? (
        <Image
          className={`absolute inset-0 h-full w-full object-cover ${radius}`}
          src={customUrl}
          alt={"Avatar"}
          width={180}
          height={180}
        />
      ) : (
        <UserIcon className="h-full w-full" />
      )}

      {hasChecked && (
        <span
          className={` absolute flex items-center justify-center rounded-full bg-teal-500 text-xs text-white  ${hasCheckedClass}`}
        >
          <i className="las la-check"></i>
        </span>
      )}
      {hasClose && (
        <button
          onClick={(e) => {
            e.stopPropagation()
            handleClose && handleClose()
          }}
          className={` absolute flex items-center justify-center rounded-full bg-red-500 text-xs text-white  ${hasCheckedClass}`}
        >
          <XMarkIcon className="h-4 w-4" />
        </button>
      )}
    </div>
  )
}

export default Avatar
