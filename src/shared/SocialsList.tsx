import React, { <PERSON> } from "react"

import { SocialType } from "@/shared/SocialsShare"

export interface SocialsListProps {
  className?: string
  itemClass?: string
  socials?: SocialType[]
}

const socialsDemo: SocialType[] = [
  {
    name: "Facebook",
    icon: "lab la-facebook-square",
    href: "https://www.facebook.com/Consulenteideale",
  },
  {
    name: "LinkedIn",
    icon: "lab la-linkedin",
    href: "https://www.linkedin.com/company/consulenteideale",
  },
  {
    name: "YouTube",
    icon: "lab la-youtube",
    href: "https://www.youtube.com/channel/UCuYfWZ3222h4CynKVhkBtrw",
  },
  {
    name: "Instagram",
    icon: "lab la-instagram",
    href: "https://www.instagram.com/consulenteideale.it",
  },
]

const SocialsList: FC<SocialsListProps> = ({
  className = "",
  itemClass = "block",
  socials = socialsDemo,
}) => {
  return (
    <nav
      className={`nc-SocialsList flex space-x-2.5 text-2xl text-neutral-6000 dark:text-neutral-300 ${className}`}
      data-nc-id="SocialsList"
    >
      {socials.map((item, i) => (
        <a
          key={i}
          className={`${itemClass}`}
          href={item.href}
          target="_blank"
          rel="noopener noreferrer"
          title={item.name}
        >
          <i className={item.icon}></i>
        </a>
      ))}
    </nav>
  )
}

export default SocialsList
