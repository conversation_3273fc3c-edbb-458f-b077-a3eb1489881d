import React from "react"
import { openChat } from "@/utils/HubspotInteractions"
import { LifebuoyIcon } from "@heroicons/react/24/outline"

export interface MenuBarProps {
  className?: string
  iconClassName?: string
}
const MobileChat: React.FC<MenuBarProps> = ({
  className = "p-2.5 rounded-lg text-neutral-700 dark:text-neutral-300",
  iconClassName = "h-8 w-8",
}) => {
  return (
    <>
      <button
        onClick={() => openChat()}
        className={`flex items-center justify-center focus:outline-none ${className}`}
      >
        <LifebuoyIcon className={iconClassName} />
      </button>
    </>
  )
}

export default MobileChat
