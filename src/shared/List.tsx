import { FC, ReactFragment } from "react"
import { cn } from "@/utils/cn"

export interface ListProps {
  listItems: {
    id: number
    content: React.ReactNode
    icon: any
    iconBackground: string
  }[]
}

const List: FC<ListProps> = ({ listItems }) => {
  return (
    <div className="flow-root">
      <ul role="list" className="-mb-8">
        {listItems.map((event, eventIdx) => (
          <li key={event.id}>
            <div className="relative pb-8">
              {eventIdx !== listItems.length - 1 ? (
                <span
                  className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                  aria-hidden="true"
                />
              ) : null}
              <div className="relative flex space-x-3">
                <div>
                  <span
                    className={cn(
                      event.iconBackground,
                      "flex h-8 w-8 items-center justify-center rounded-full ring-8 ring-white"
                    )}
                  >
                    <event.icon
                      className="h-5 w-5 text-white"
                      aria-hidden="true"
                    />
                  </span>
                </div>
                <div className="flex min-w-0 flex-1 pt-1.5">
                  <div>
                    <p className="text-sm text-gray-500">{event.content}</p>
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default List
