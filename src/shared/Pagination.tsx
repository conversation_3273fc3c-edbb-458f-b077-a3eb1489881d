"use client"

import React, { FC } from "react"
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation"
import { cn } from "@/utils/cn"
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline"

export interface PaginationProps {
  className?: string
  currentPage: number
  numberOfPages: number
}

const renderEllipsis = () => (
  <span className="pointer-events-none inline-flex h-11 w-11 items-center justify-center">
    ...
  </span>
)

const Pagination: FC<PaginationProps> = ({
  className = "",
  currentPage,
  numberOfPages,
}) => {
  const pathname = usePathname()
  const router = useRouter()
  const MAX_PAGE_ITEMS_DISPLAYED = 5

  // we must keep every other query parameter
  const params = useSearchParams()

  // Function to handle page change
  const handlePageChange = (page: number) => {
    const newParams = new URLSearchParams(params.toString())

    newParams.set("page", page.toString())

    router.push(`${pathname}?${newParams.toString()}`)
  }

  const renderPageItem = (page: number, isCurrent: boolean = false) => (
    <button
      key={page}
      className={cn(
        `inline-flex h-11 w-11 items-center justify-center rounded-full border border-neutral-200 bg-white text-neutral-600 hover:bg-neutral-100 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:hover:bg-neutral-800 ${
          isCurrent ? "pointer-events-none bg-primary-6000 text-white" : ""
        }`
      )}
      onClick={() => handlePageChange(page)}
    >
      {page}
    </button>
  )

  const getPageItems = () => {
    const pages = []
    let startPage, endPage

    if (numberOfPages <= MAX_PAGE_ITEMS_DISPLAYED) {
      // Display all pages if total pages are less than or equal to MAX_PAGE_ITEMS_DISPLAYED
      startPage = 1
      endPage = numberOfPages
    } else {
      // Calculate range around current page for more pages
      startPage = Math.max(2, currentPage - 1)
      endPage = Math.min(numberOfPages - 1, currentPage + 1)

      // Adjust if near start or end
      if (currentPage < 4) {
        endPage = 4
      }
      if (currentPage > numberOfPages - 3) {
        startPage = numberOfPages - 3
      }
    }

    // Always show first page
    pages.push(renderPageItem(1, currentPage === 1))

    // Ellipsis after first page if needed
    if (startPage > 2) {
      pages.push(renderEllipsis())
    }

    // Middle pages
    for (let i = startPage; i <= endPage; i++) {
      if (i !== 1 && i !== numberOfPages) {
        pages.push(renderPageItem(i, currentPage === i))
      }
    }

    // Ellipsis before last page if needed
    if (endPage < numberOfPages - 1) {
      pages.push(renderEllipsis())
    }

    // Always show last page if it's not the first page
    if (numberOfPages > 1) {
      pages.push(renderPageItem(numberOfPages, currentPage === numberOfPages))
    }

    return pages
  }
  return (
    <nav
      className={`nc-Pagination inline-flex space-x-1 text-base font-medium ${className}`}
    >
      {currentPage > 1 && (
        <button
          className="inline-flex h-11 w-11 cursor-pointer items-center justify-center rounded-full border border-neutral-200 bg-white text-neutral-600 hover:bg-neutral-100 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:hover:bg-neutral-800"
          onClick={() => handlePageChange(currentPage - 1)}
        >
          <ChevronLeftIcon className="h-5 w-5" />
        </button>
      )}
      {getPageItems()}
      {currentPage < numberOfPages && (
        <button
          className="inline-flex h-11 w-11 cursor-pointer items-center justify-center rounded-full border border-neutral-200 bg-white text-neutral-600 hover:bg-neutral-100 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:hover:bg-neutral-800"
          onClick={() => handlePageChange(currentPage + 1)}
        >
          <ChevronRightIcon className="h-5 w-5" />
        </button>
      )}
    </nav>
  )
}

export default Pagination
