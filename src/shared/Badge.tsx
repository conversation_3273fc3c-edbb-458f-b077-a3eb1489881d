import React, { FC, ReactNode } from "react"
import Link from "next/link"
import { Route } from "@/routers/types"
import chroma from "chroma-js"

export interface BadgeProps {
  className?: string
  name: string
  href?: Route<string>
  color?: string
}

function stringToColor(str: string) {
  let hash = 0
  for (let i = 0; i < String(str).length; i++) {
    hash = String(str).charCodeAt(i) + ((hash << 5) - hash)
  }

  let color = "#"
  for (let i = 0; i < 3; i++) {
    let value = (hash >> (i * 8)) & 0xff
    color += ("00" + value.toString(16)).substr(-2)
  }

  return chroma(color).hex()
}

const Badge: FC<BadgeProps> = ({ className = "relative", name, href }) => {
  const bgColor = stringToColor(name)
  const textColor = chroma.contrast(bgColor, "black") > 8 ? "black" : "white"

  const CLASSES =
    "nc-Badge inline-flex px-2.5 py-1 rounded-full font-medium text-xs transition-colors duration-300 " +
    className

  const inlineStyle = {
    backgroundColor: bgColor,
    color: textColor,
  }

  return href ? (
    <Link href={href} style={inlineStyle} className={CLASSES}>
      {name}
    </Link>
  ) : (
    <span style={inlineStyle} className={CLASSES}>
      {name}
    </span>
  )
}

export default Badge
