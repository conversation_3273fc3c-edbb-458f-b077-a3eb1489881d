import React, { ReactNode } from "react"

export interface Heading2Props {
  heading?: ReactNode
  subHeading?: ReactNode
  className?: string
}

const Heading2: React.FC<Heading2Props> = ({
  className = "",
  heading = "Stays in Tokyo",
  subHeading,
}) => {
  return (
    <div className={`mb-12 lg:mb-16 ${className}`}>
      <h1 className="text-2xl">{heading}</h1>
      {subHeading}
    </div>
  )
}

export default Heading2
