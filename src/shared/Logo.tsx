import React from "react"
import { StaticImageData } from "next/image"
import Link from "next/link"
import logoLightImg from "@/images/logo-light.png"
import logoImg from "@/images/logo.png"

import { cn } from "@/lib/utils"

import LogoSvg from "./LogoSvg"
import LogoSvgLight from "./LogoSvgLight"

export interface LogoProps {
  img?: StaticImageData
  imgLight?: StaticImageData
  className?: string
}

const Logo: React.FC<LogoProps> = ({
  img = logoImg,
  imgLight = logoLightImg,
  className,
}) => {
  return (
    <Link
      href="/"
      className={cn(
        "ttnc-logo flex h-12 focus:outline-none focus:ring-0 lg:h-14",
        className
      )}
    >
      <LogoSvg />
      {/* {imgLight && (
        <img
          className="hidden max-h-12 dark:block"
          src={imgLight}
          alt="Logo-Light"
        />
      )} */}
    </Link>
  )
}

export default Logo
