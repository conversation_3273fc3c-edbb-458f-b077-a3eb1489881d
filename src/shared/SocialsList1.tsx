import React, { <PERSON> } from "react"

import { SocialType } from "@/shared/SocialsShare"

export interface SocialsList1Props {
  className?: string
}

const socials: SocialType[] = [
  {
    name: "Facebook",
    icon: "lab la-facebook-square",
    href: "https://www.facebook.com/Consulenteideale",
  },
  {
    name: "LinkedIn",
    icon: "lab la-linkedin",
    href: "https://www.linkedin.com/company/consulenteideale",
  },
  {
    name: "YouTube",
    icon: "lab la-youtube",
    href: "https://www.youtube.com/channel/UCuYfWZ3222h4CynKVhkBtrw",
  },
  {
    name: "Instagram",
    icon: "lab la-instagram",
    href: "https://www.instagram.com/consulenteideale.it",
  },
]

const SocialsList1: FC<SocialsList1Props> = ({ className = "space-y-2.5" }) => {
  const renderItem = (item: SocialType, index: number) => {
    return (
      <a
        href={item.href}
        className="group flex items-center space-x-2 text-2xl leading-none text-neutral-700 hover:text-black dark:text-neutral-300 dark:hover:text-white"
        key={index}
        target="_blank"
        rel="noreferrer noopener"
      >
        <i className={item.icon}></i>
        <span className="hidden text-sm lg:block">{item.name}</span>
      </a>
    )
  }

  return (
    <div className={`nc-SocialsList1 ${className}`} data-nc-id="SocialsList1">
      {socials.map(renderItem)}
    </div>
  )
}

export default SocialsList1
