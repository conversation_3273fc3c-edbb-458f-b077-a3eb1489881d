import {
  maskitoDateOptionsGenerator,
  maskitoNumberOptionsGenerator,
} from "@maskito/kit"
import {
  blob,
  boolean,
  custom,
  email,
  forward,
  maxLength,
  minLength,
  nullable,
  nullish,
  object,
  string,
  pipe,
  check
} from "valibot"

export const Professions = {
  COMMERCIALISTA: "Commercialista",
  AVVOCATO: "Avvocato",
} as const

export const PATHS_WITH_SEARCH_HEADER = ["/ricerca", "/professionisti"]

export type ProfessionKeys = (typeof Professions)[keyof typeof Professions]

export const LoginSchema = object({
  email: pipe(string("Per favore inserisci una mail corretta."),
    minLength(1, "Per favore inserisci una mail."),
    email("Per favore inserisci una mail corretta.")
  ),
  password: pipe(string("Per favore inserisci una password."),
    minLength(8, "Per favore inserisci una password più lunga."),
    maxLength(40, "Per favore inserisci una password più corta.")
  ),
})

export const ResetPasswordSchema = object({
  email: pipe(string("Per favore inserisci una mail corretta."),
    minLength(1, "Per favore inserisci una mail."),
    email("Per favore inserisci una mail corretta.")
  ),
})

export const NewPasswordSchema = pipe(object(
  {
    password: pipe(string("Per favore inserisci una password."),
      minLength(8, "Per favore inserisci una password più lunga."),
      maxLength(40, "Per favore inserisci una password più corta.")
    ),
    confirmPassword: string(),
  }),


  check((input) => {
    return input.password === input.confirmPassword
  }, "Le password non coincidono."),


)

export const maskitoOptions = maskitoDateOptionsGenerator({
  mode: "dd/mm/yyyy",
  min: new Date(1900, 0, 1),
  max: new Date(),
  separator: "/",
})

export const userChangePasswordSchema = pipe(object(
  {
    currentPassword: pipe(string("Per favore inserisci la tua password attuale."),
      minLength(4, "Per favore inserisci la tua password attuale.")
    ),
    newPassword: pipe(string("Per favore inserisci la tua nuova password."),
      minLength(4, "Per favore inserisci la tua nuova password."),
    ),
    confirmPassword: string(),
  }),


  check((input) =>
    input.newPassword === input.confirmPassword,
    "Le password non coincidono."
  ),


)
