import { DateTime } from "luxon"

const appointmentTimeframe = (date: DateTime) => {
  const hour = date.hour
  switch (hour) {
    case 9:
      return "09:00 - 12:00"
    case 12:
      return "12:00 - 16:00"
    case 16:
      return "16:00 - 20:00"
    default:
      return "09:00 - 12:00"
  }
}

const splitTimeframe = (timeframe: string) => {
  const [start, end] = timeframe.split(" - ")
  return { start, end }
}

export { appointmentTimeframe, splitTimeframe }
