import ky, { HTTPError, TimeoutError } from "ky"
import { nullable, number, object, InferOutput, parse, string, union } from "valibot"

export const RouteSchema = nullable(
  object({
    url: object({
      id: string(),
      path: nullable(string()),
      city: nullable(number()),
      consultingServiceCategory: nullable(number()),
      consultantProfession: nullable(number()),
      consultant: nullable(number()),
      consultingService: nullable(number()),
    }),
  })
)

export type RouteSchemaType = InferOutput<typeof RouteSchema>

/**
 * Check if a route is valid
 * @param route - The route to check
 * @returns A boolean indicating if the route is valid
 */

export const getListConsultantsParams = async (route: string) => {
  const response = await ky
    .post(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/check-if-url-exists-by-path`,
      {
        headers: { "Content-Type": "application/json" },
        json: { path: route },
        cache: "no-cache",
      }
    )
    .json()

  try {
    const parsedResponse = parse(RouteSchema, response)
    return parsedResponse
  } catch (error) {
    if (error instanceof HTTPError) {
      console.error(
        "HTTP Error:",
        error.response.status,
        await error.response.text()
      )
    } else if (error instanceof TimeoutError) {
      console.error("Timeout Error")
    } else {
      console.error("Fetching data failed:", error)
    }
    return null
  }
}
