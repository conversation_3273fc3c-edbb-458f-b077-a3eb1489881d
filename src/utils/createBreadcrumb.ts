import { capitalizeAndSeparateString } from "./stringUtils"

export interface BreadcrumbParams {
  city?: string
  profession: string
  service: string
}

export const createBreadcrumbArray = (params: BreadcrumbParams) => {
  const { city, profession, service } = params

  const breadcrumbArray = [
    {
      label: capitalizeAndSeparateString(profession),
      path: `/ricerca/${profession}`,
    },
  ]

  if (service) {
    breadcrumbArray.push({
      label: capitalizeAndSeparateString(service),
      path: `/ricerca/${profession}/${service}`,
    })
  }

  if (city) {
    breadcrumbArray.push({
      label: capitalizeAndSeparateString(city),
      path: `/ricerca/${profession}/${service}/${city}`,
    })
  }

  return breadcrumbArray
}
