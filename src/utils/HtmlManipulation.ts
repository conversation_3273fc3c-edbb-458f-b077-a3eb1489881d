import { convertToRaw, EditorState } from "draft-js"

type StyledChar = {
  text: string
  styles?: string[] // The styles are now strings representing HTML tags
}

// Define your style-to-tag mapping
const matchStyle: Record<string, string> = {
  BOLD: "strong",
  ITALIC: "em",
  UNDERLINE: "u",
  STRIKETHROUGH: "s",
  // Add other styles as needed
}

export const getHtml = (editorState: EditorState): string => {
  const rawContentState = convertToRaw(editorState.getCurrentContent())
  const blocks = rawContentState.blocks

  const blockHtmlArray = blocks.map((block) => {
    let styledTextArray: StyledChar[] = block.text
      .split("")
      .map((char) => ({ text: char }))

    block.inlineStyleRanges?.forEach((range) => {
      // Use matchStyle to get the correct HTML tag for the style
      const styleTag = matchStyle[range.style] || range.style.toLowerCase()
      for (let i = range.offset; i < range.offset + range.length; i++) {
        styledTextArray[i].styles = styledTextArray[i].styles || []
        styledTextArray[i].styles?.push(styleTag)
      }
    })

    const styledHtml = styledTextArray.reduce((acc, charObj) => {
      // Apply the HTML tags based on the styles array
      const openingTags = charObj.styles
        ? charObj.styles.map((style) => `<${style}>`).join("")
        : ""
      const closingTags = charObj.styles
        ? charObj.styles
            .map((style) => `</${style}>`)
            .reverse()
            .join("")
        : ""
      return `${acc}${openingTags}${charObj.text}${closingTags}`
    }, "")

    const blockTag =
      {
        unstyled: "p",
        "header-three": "h3",
        // Add more block type mappings as needed
      }[block.type] || "p"

    return `<${blockTag}>${styledHtml}</${blockTag}>`
  })

  return blockHtmlArray.join("")
}
