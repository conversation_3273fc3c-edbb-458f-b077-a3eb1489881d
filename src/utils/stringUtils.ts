// a replacer function that replaces both spaces with dashes and remove commas plus everything lowercase to pass to replace<PERSON>ll that accepts a replacer function
import { capitalize } from "lodash"

import { Professions } from "@/lib/constants"

export const replaceAll = (str: string | null | undefined) =>
  (str ?? "").replace(/\b \b/g, "-").replace(/,/g, "").trim().toLowerCase()

// Restore the string to re-add spaces instead of dashes
export const capitalizeAndSeparateString = (str: string | undefined) => {
  return capitalize(str)?.replaceAll(/-/g, " ")
}

export const separateString = (str: string | undefined) => {
  return str?.toLowerCase()?.replaceAll(/-/g, " ")
}

function capitalizeWords(input: string): string {
  return input.replace(/\b\w/g, (match) => match.toUpperCase())
}

export const makePlural = (str: string) => {
  switch (true) {
    case str.toLowerCase() === Professions.AVVOCATO.toLowerCase():
      return "Avvocati"
    case str.toLowerCase() === Professions.COMMERCIALISTA.toLowerCase():
      return "Commercialisti"
    default:
      return str
  }
}

export function parseBoolean(value: string) {
  if (typeof value === "string") {
    const lowerCaseValue = value.toLowerCase()
    if (lowerCaseValue === "true") return true
    if (lowerCaseValue === "false") return false
  }
  return null
}
