import {
  GetConsultantQuery,
  GetConsultingServicesQuery,
} from "@/api/gql/generated"

export type GroupedConsultingServiceItem = {
  name: string | null | undefined
  urlKey: string | null | undefined
  consultingServiceCategory: string | undefined
  consultingServiceCategoryUrlKey: string | null | undefined
  price: number | null | undefined
  id: number | undefined
}

export type GroupedConsultingServiceItemByProfession = {
  name: string | null | undefined
  urlKey: string | null | undefined
  consultingServiceCategory: string | undefined
  consultingServiceCategoryUrlKey: string | null | undefined
  price: number | null | undefined
  id: number | undefined
}

export type GroupedCategory = {
  category: string | undefined
  items: GroupedConsultingServiceItem[]
}

type GroupedServicesByProfessional = {
  category: string | undefined
  items: GroupedConsultingServiceItemByProfession[]
}

type OffersType = NonNullable<
  NonNullable<GetConsultantQuery["byUrlKeyConsultant"]>["offers"]
>

type OfferCollectionArray = NonNullable<OffersType["collection"]>

export const groupByCategoryProfessional = (
  collection: OfferCollectionArray
): GroupedServicesByProfessional[] => {
  const grouped: GroupedServicesByProfessional[] = []

  collection?.forEach((edge) => {
    const category = edge?.consultingService?.consultingServiceCategories?.collection?.map((serviceCategory) => serviceCategory?.name)?.join(', ')

    // Find the existing group or create a new one
    let group = grouped.find((g) => g.category === category)
    if (!group) {
      group = { category, items: [] }
      grouped.push(group)
    }

    const item: GroupedConsultingServiceItemByProfession = {
      name: edge?.consultingService?.name,
      urlKey: edge?.consultingService?.urlKey,
      consultingServiceCategory:
        edge?.consultingService?.consultingServiceCategories?.collection?.map((serviceCategory) => serviceCategory?.name)?.join(', '),
      consultingServiceCategoryUrlKey:
        edge?.consultingService?.consultingServiceCategories?.collection?.map((serviceCategory) => serviceCategory?.urlKey)?.join(', '),
      price: edge?.price ?? edge?.consultingService?.price,
      id: edge?.consultingService?._id,
    }

    group.items.push(item)
  })

  return grouped
}

export const groupByCategory = (
  collection: NonNullable<
    GetConsultingServicesQuery["consultingServices"]
  >["collection"]
): GroupedCategory[] => {
  const grouped: GroupedCategory[] = []

  collection?.forEach((edge) => {
    edge?.consultingServiceCategories?.collection?.forEach((serviceCategory) => {
      // Find the existing group or create a new one
      const category = serviceCategory?.name;
      if (!grouped.find(c => c.category === category && c.items.find(item => item.urlKey === edge.urlKey))) {
        let group = grouped.find((g) => g.category === category)
        if (!group) {
          group = { category, items: [] }
          grouped.push(group)
        }

        const item: GroupedConsultingServiceItem = {
          name: edge?.name,
          urlKey: edge?.urlKey,
          price: edge?.price,
          id: edge?._id,
          consultingServiceCategory: category,
          consultingServiceCategoryUrlKey: edge?.consultingServiceCategories?.collection?.map((serviceCategory) => serviceCategory?.urlKey)?.join(', '),
        }

        group.items.push(item)
      }
    })


  })

  return grouped
}
