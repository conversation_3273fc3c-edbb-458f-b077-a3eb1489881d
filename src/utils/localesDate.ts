import { DateTime } from "luxon"

export const convertDateToItalianWithTime = (
  date: Date | null,
  time: string | null
) => {
  if (!date || !time) {
    return null
  }

  if (!(date instanceof Date)) {
    return null
  }

  // time is in ISO format (T16:00:00) for example
  const DateLuxon = DateTime.fromJSDate(date)

  if (!DateLuxon.isValid) {
    return null
  }

  const dateWithTimeAndAppendedTime = `${DateLuxon.toLocaleString(
    DateTime.DATE_MED_WITH_WEEKDAY,
    {
      locale: "it",
    }
  )}, ${time}`

  return dateWithTimeAndAppendedTime
}

export const convertDateToItalian = (date: string | null) => {
  if (!date) {
    return undefined
  }

  const DateLuxon = DateTime.fromISO(date)

  if (!DateLuxon.isValid) {
    return undefined
  }

  const dateWithTime = DateLuxon.toLocaleString(DateTime.DATE_SHORT, {
    locale: "en-GB",
  })

  return dateWithTime
}
