import { atom, SetStateAction } from "jotai"

export type RegistrationFormFields = {
  fullName: string
  email: string
  confirmEmail: string
  password: string
  confirmPassword: string
  city: string
  dateOfBirth: string
  remote: boolean
  image: File | null
}

export const registrationFormFieldsAtom = atom<RegistrationFormFields>({
  fullName: "",
  email: "",
  confirmEmail: "",
  password: "",
  confirmPassword: "",
  city: "",
  dateOfBirth: "",
  remote: false,
  image: null,
})

// readWriteAtom

export const registrationFormFieldsReadWriteAtom = atom(
  (get) => get(registrationFormFieldsAtom),
  (get, set, update: SetStateAction<RegistrationFormFields>) => {
    set(registrationFormFieldsAtom, update)
  }
)
