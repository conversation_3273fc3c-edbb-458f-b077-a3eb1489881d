import { atom } from "jotai"

import { Timeframe } from "@/components/SelectTimeframe"

interface FormState {
  day: Date | null
  time: Timeframe
  endTime: string
  service: string
  name: string
  surname: string
  email: string
  telephone: string
  acceptedConditions: boolean
  professional: string
  serviceId: number
}

export const formStateAtom = atom(
  {
    day: null,
    time: {
      id: 0,
      name: "",
      time: "",
      isoTime: "",
      isoTimeEnd: "",
    },
    endTime: "",
    service: "",
    name: "",
    surname: "",
    email: "",
    telephone: "",
    acceptedConditions: false,
    professional: "",
    serviceId: 0,
  },
  (get, set, update: Partial<FormState>) => {
    const currentFormState = get(formStateAtom)
    set(formStateAtom, {
      ...currentFormState,
      ...update,
    })
  }
)

export const formStepAtom = atom<"form" | "completed">("form")
