import { atom } from "jotai"

import { SearchFormFields } from "@/app/(client-components)/type"

export const byTypeAtom = atom<"byCity" | "online" | null>("online")
export const searchAtom = atom<SearchFormFields | null>(null)
export const professionOrService = atom<"service" | "profession">('service')
export const searchInputErrorAtom = atom<boolean>(false)
export const searchInputLocationErrorAtom = atom<boolean>(false)
export const searchPathServiceAtom = atom<string | null>("")
export const searchPathCityAtom = atom<string | null>("")
export const oldCity = atom<string | null>("")
export const searchCityAtom = atom<string | null>("")
export const searchServiceAtom = atom<string>("")
export const isSearchLoading = atom<boolean>(false)
export const modalSearchAtom = atom<boolean>(false)
export const searchCursorAtom = atom<string | null | undefined>(null)
export const isForwardNavigationAtom = atom<boolean>(false)
export const isOnlineAtom = atom<boolean>(false)
