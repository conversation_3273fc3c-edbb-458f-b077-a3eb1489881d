"use client"

import { DEFAULT_LOGIN_REDIRECT } from "@/routers/routes"
import { AuthError } from "next-auth"
import { signIn } from "next-auth/react"
import * as v from "valibot"

import { getUserByEmailAndPassword } from "@/data/user"
import { LoginSchema } from "@/lib/constants"

export const login = async (
  values: v.InferOutput<typeof LoginSchema>,
  callbackUrl?: string | null
) => {
  const validatedFields = v.safeParse(LoginSchema, values)

  if (!validatedFields.success) {
    return { error: "Invalid fields" }
  }

  const { email, password } = validatedFields.output

  try {
    await getUserByEmailAndPassword(email, password)
  } catch (error) {
    if (error instanceof Error) {
      switch (error.message) {
        case "Invalid credentials.":
          return { error: "Credenziali non valide" }
        default:
          return { error: "Si è verificato un errore" }
      }
    }
  }

  try {
    const result = await signIn("credentials", {
      email,
      password,
      redirect: true,
      callbackUrl: callbackUrl ?? DEFAULT_LOGIN_REDIRECT
    })

    if (result?.error) {
      return { error: result.error }
    }
  } catch (error) {
    if (error instanceof AuthError) {
      switch (error.type) {
        case "CredentialsSignin":
          return { error: "Credenziali non valide" }
        default:
          return { error: "Si è verificato un errore" }
      }
    }

    throw error
  }
}
