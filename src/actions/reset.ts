"use server"

import * as v from "valibot"

import { sendResetTokenEmail } from "@/data/reset"
import { ResetPasswordSchema } from "@/lib/constants"

export const reset = async (values: v.InferOutput<typeof ResetPasswordSchema>) => {
  const validatedFields = v.safeParse(ResetPasswordSchema, values)

  if (!validatedFields.success) {
    return { error: "Mail non valida" }
  }

  const { email } = validatedFields.output
  try {
    await sendResetTokenEmail(email)
  } catch (error) {
    return { error: "Errore durante l'invio della mail" }
  }

  return {
    success:
      "Se esiste una mail associata a questo account, riceverai un'email con le istruzioni per resettare la password",
  }
}
