"use server"

import * as v from "valibot"

import "@/data/reset"

import { changePasswordByTokenConsultant } from "@/data/new-password"
import { NewPasswordSchema } from "@/lib/constants"

export const newPassword = async (
  values: v.InferOutput<typeof NewPasswordSchema>,
  token?: string | null
) => {
  if (!token) {
    return { error: "Token mancante" }
  }

  const validatedFields = v.safeParse(NewPasswordSchema, values)

  if (!validatedFields.success) {
    return { error: "Campi non validi" }
  }

  const { password } = validatedFields.output
  try {
    await changePasswordByTokenConsultant(token, password)
  } catch (error) {
    return { error: "Errore nel cambio password" }
  }

  return { success: "Password cambiata con successo" }
}
