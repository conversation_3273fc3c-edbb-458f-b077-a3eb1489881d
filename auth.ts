import NextAuth from "next-auth"

import authConfig from "./auth.config"

export const {
  handlers,
  auth,
  signIn,
  signOut,
} = NextAuth({
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  callbacks: {
    async session({ token, session }) {
      if (token.sub && session.user) {
        session.user.id = token.sub
      }

      if (session.user) {
        session.user.name = token.name
        session.user.email = token.email
        session.user.token = token.token
      }

      return session
    },
    async jwt({ token, user }) {
      if (user) {
        token.token = user.token
      }
      if (!token.sub) return token
      const now = Date.now() / 1000

      if (token.exp && token.exp < now) {
        return null
      }
      return token
    },
  },
  session: { strategy: "jwt", maxAge: 24 * 60 * 60 },
  secret: process.env.AUTH_SECRET,
  ...authConfig,
})
