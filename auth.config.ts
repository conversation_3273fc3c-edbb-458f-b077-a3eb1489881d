import type { NextAuthConfig } from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { safeParse } from "valibot"

import { getUserByEmailAndPassword } from "@/data/user"
import { LoginSchema } from "@/lib/constants"

export default {
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" }
      },
      authorize: async (credentials) => {
        const validatedFields = safeParse(LoginSchema, credentials)

        if (validatedFields.success) {
          const { email, password } = validatedFields.output
          const user = await getUserByEmailAndPassword(email, password)
          if (!user) return null

          return user
        }
        return null
      },
    }),
  ],
} satisfies NextAuthConfig
