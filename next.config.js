/**
 * @type {import('next').NextConfig}
 */

const nextConfig = {
  serverRuntimeConfig: {
    // Will only be available on the server side
    rollbarServerToken: process.env.ROLLBAR_SERVER_TOKEN,
  },
  publicRuntimeConfig: {
    // Will be available on both server and client
    rollbarClientToken: process.env.ROLLBAR_CLIENT_TOKEN,
  },
  reactStrictMode: false,
  async redirects() {
    return [
      {
        source: '/prodotto/:path*', // Match all paths starting with /prodotto/
        destination: '/blog/prodotto/:path*', // Redirect to the same path under /blog/prodotto/
        permanent: true, // This indicates a 301 Permanent Redirect
      },
      {
        source: '/categoria-servizio/:path*', // Match all paths starting with /prodotto/
        destination: '/blog/categoria-servizio/:path*', // Redirect to the same path under /blog/prodotto/
        permanent: true, // This indicates a 301 Permanent Redirect
      },
      {
        source: '/privacy-policy',
        destination: '/blog/privacy-policy',
        permanent: true,
      },
      {
        source: '/chi-siamo',
        destination: '/blog/chi-siamo',
        permanent: true,
      },
      {
        source: '/servizi',
        destination: '/blog/servizi',
        permanent: true,
      },
      {
        source: '/under-construction',
        destination: '/blog/under-construction',
        permanent: true,
      },
      {
        source: '/commercialisti',
        destination: '/blog/commercialisti',
        permanent: true,
      },
      {
        source: '/avvocati',
        destination: '/blog/avvocati',
        permanent: true,
      },
      {
        source: '/esperti-contabili',
        destination: '/blog/esperti-contabili',
        permanent: true,
      },
      {
        source: '/come-funziona-consulenti/modalita-discrizione-iscriviti-e-incassa',
        destination: '/blog/come-funziona-consulenti/modalita-discrizione-iscriviti-e-incassa',
        permanent: true,
      },
      {
        source: '/stores-list',
        destination: '/blog/stores-list',
        permanent: true,
      },
      {
        source: '/dashboard-venditore',
        destination: '/blog/dashboard-venditore',
        permanent: true,
      },
      {
        source: '/contatti',
        destination: '/blog/contatti',
        permanent: true,
      },
      {
        source: '/carrello',
        destination: '/blog/carrello',
        permanent: true,
      },
      {
        source: '/pagamento',
        destination: '/blog/pagamento',
        permanent: true,
      },
      {
        source: '/account-cliente',
        destination: '/blog/account-cliente',
        permanent: true,
      },
      {
        source: '/candidature',
        destination: '/blog/candidature',
        permanent: true,
      },
      {
        source: '/ordini-e-rimborsi',
        destination: '/blog/ordini-e-rimborsi',
        permanent: true,
      },
      {
        source: '/vinci-le-hawaii',
        destination: '/blog/vinci-le-hawaii',
        permanent: true,
      },
      {
        source: '/consulenti',
        destination: '/blog/consulenti',
        permanent: true,
      },
      {
        source: '/servizi-per-regione',
        destination: '/blog/servizi-per-regione',
        permanent: true,
      },
      {
        source: '/come-funziona-clienti',
        destination: '/blog/come-funziona-clienti',
        permanent: true,
      },
      {
        source: '/rimborsi-clienti',
        destination: '/blog/rimborsi-clienti',
        permanent: true,
      },
      {
        source: '/portafoglio-digitale-clienti',
        destination: '/blog/portafoglio-digitale-clienti',
        permanent: true,
      },
      {
        source: '/premi-viaggio-alle-hawaii-clienti',
        destination: '/blog/premi-viaggio-alle-hawaii-clienti',
        permanent: true,
      },
      {
        source: '/sconti-e-coupon-clienti',
        destination: '/blog/sconti-e-coupon-clienti',
        permanent: true,
      },
      {
        source: '/faq-domande-e-risposte-clienti',
        destination: '/blog/faq-domande-e-risposte-clienti',
        permanent: true,
      },
      {
        source: '/come-funziona-consulenti',
        destination: '/blog/come-funziona-consulenti',
        permanent: true,
      },
      {
        source: '/partner-consulenti',
        destination: '/blog/partner-consulenti',
        permanent: true,
      },
      {
        source: '/erogazione-premi-regionali-consulenti',
        destination: '/blog/erogazione-premi-regionali-consulenti',
        permanent: true,
      },
      {
        source: '/formazione-gestionale-clienti-consulenti',
        destination: '/blog/formazione-gestionale-clienti-consulenti',
        permanent: true,
      },
      {
        source: '/ordini-rifiutati-contestazioni-consulenti',
        destination: '/blog/ordini-rifiutati-contestazioni-consulenti',
        permanent: true,
      },
      {
        source: '/webinar-dinformazione-consulenti',
        destination: '/blog/webinar-dinformazione-consulenti',
        permanent: true,
      },
      {
        source: '/faq-domande-e-risposte-consulenti',
        destination: '/blog/faq-domande-e-risposte-consulenti',
        permanent: true,
      },
      {
        source: '/assistenza',
        destination: '/blog/assistenza',
        permanent: true,
      },
      {
        source: '/faq-domande-e-risposte-investitori',
        destination: '/blog/faq-domande-e-risposte-investitori',
        permanent: true,
      },
      {
        source: '/registrazione-clienti',
        destination: '/blog/registrazione-clienti',
        permanent: true,
      },
      {
        source: '/richiesta-informazione-investitori-privati',
        destination: '/blog/richiesta-informazione-investitori-privati',
        permanent: true,
      },
      {
        source: '/richiesta-incontro-in-sede-investitori-privati',
        destination: '/blog/richiesta-incontro-in-sede-investitori-privati',
        permanent: true,
      },
      {
        source: '/professionisti-piu-economici',
        destination: '/blog/professionisti-piu-economici',
        permanent: true,
      },
      {
        source: '/professionisti-piu-richiesti',
        destination: '/blog/professionisti-piu-richiesti',
        permanent: true,
      },
      {
        source: '/professionisti-piu-amati',
        destination: '/blog/professionisti-piu-amati',
        permanent: true,
      },
      {
        source: '/prenotazioni-webinar',
        destination: '/blog/prenotazioni-webinar',
        permanent: true,
      },
      {
        source: '/webinar-registrati',
        destination: '/blog/webinar-registrati',
        permanent: true,
      },
      {
        source: '/sei-alla-ricerca-di-un-consulente-fiscale',
        destination: '/blog/sei-alla-ricerca-di-un-consulente-fiscale',
        permanent: true,
      },
      {
        source: '/consulenti-del-lavoro',
        destination: '/blog/consulenti-del-lavoro',
        permanent: true,
      },
      {
        source: '/altri-consulenti',
        destination: '/blog/altri-consulenti',
        permanent: true,
      },
      {
        source: '/candidaturaconsulenteideale',
        destination: '/blog/candidaturaconsulenteideale',
        permanent: true,
      },
      {
        source: '/datifiscali',
        destination: '/blog/datifiscali',
        permanent: true,
      },
      {
        source: '/consulente-finanza-agevolata',
        destination: '/blog/consulente-finanza-agevolata',
        permanent: true,
      },
    ];
  },
  async headers() {
    return [
      {
        source: '/account/subscription',
        headers: [
          {
            key: "Cache-Control",
            value: "s-maxage=1, stale-while-revalidate=59",
          }
        ]
      },
      {
        source: '/sitemap.xml',
        headers: [
          {
            key: "Cache-Control",
            value: "no-store, no-cache, must-revalidate, proxy-revalidate",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          }
        ]
      },
      {
        source: '/sitemap-:path*.xml',
        headers: [
          {
            key: "Cache-Control",
            value: "no-store, no-cache, must-revalidate, proxy-revalidate",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          }
        ]
      },
      {
        source: '/api/sitemap/:path*',
        headers: [
          {
            key: "Cache-Control",
            value: "no-store, no-cache, must-revalidate, proxy-revalidate",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          }
        ]
      }
    ]
  },
  experimental: {
    swcPlugins: [["@swc-jotai/react-refresh", {}]],
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "a0.muscache.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.gstatic.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "api.consulenteideale.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "api.consulenteideale.it",
        port: "",
        pathname: "/**",
      },
    ],
  },
}

// Add rewrites for sitemap XML files
nextConfig.rewrites = async () => {
  return [
    // Generic sitemap.xml file
    {
      source: '/sitemap.xml',
      destination: '/api/sitemap/sitemap.xml',
    },
    // Handle specific sitemap files by exact name
    {
      source: '/sitemap-static.xml',
      destination: '/api/sitemap/sitemap-static.xml',
    },
    {
      source: '/sitemap-:num(\\d+).xml',
      destination: '/api/sitemap/sitemap-:num.xml',
    }
  ];
};

module.exports = nextConfig
