/* eslint-disable import/no-extraneous-dependencies */
import { type CodegenConfig } from "@graphql-codegen/cli"

const config: CodegenConfig = {
  schema: "https://api.consulenteideale.it/api/graphql",
  overwrite: true,
  documents: "./src/api/gql/documents/operations.graphql",
  generates: {
    "./src/api/gql/generated.tsx": {
      plugins: [
        "typescript",
        "typescript-operations",
        "typescript-react-query",
        {
          add: {
            content: `
            type FetchOptions = {
              cache?: RequestCache;
              next?: NextFetchRequestConfig;
            };
            
            type RequestInit = {
              headers: (HeadersInit & FetchOptions) | FetchOptions;
            };`,
          },
        },
      ],
      config: {
        reactQueryVersion: 5,
        legacyMode: false,
        exposeFetcher: true,
        exposeQueryKeys: true,
        addSuspenseQuery: true,
        addInfiniteQuery: true,
        fetcher: "./fetcher#fetcher",
      },
    },
  },
  hooks: { afterAllFileWrite: ["prettier --write"] },
}

export default config
