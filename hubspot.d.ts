declare global {
  interface Window {
    HubSpotConversations?: {
      clear: (e: any) => void
      debug: () => void
      on: (e: string, cb: (message: string) => void) => void
      off: () => void
      resetAndReloadWidget: () => void
      widget?: {
        close: () => void
        load: () => void
        open: () => void
        refresh: () => void
        remove: () => void
        status: () => void
      }
    }
  }
}

export {}
