{"name": "chisfis-nextjs", "version": "0.2.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.ts --verbose"}, "dependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@graphql-codegen/cli": "^5.0.0", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.6.0", "@maskito/core": "^2.0.2", "@maskito/kit": "^2.0.2", "@maskito/react": "^2.0.2", "@next/third-parties": "^14.1.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@rollbar/react": "0.12.0-beta", "@smastrom/react-rating": "^1.5.0", "@stripe/react-stripe-js": "^2.7.0", "@stripe/stripe-js": "^3.3.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.8.7", "@tanstack/react-query-devtools": "^5.8.4", "@tanstack/react-query-next-experimental": "^5.8.7", "@types/sanitize-html": "^2.11.0", "@uidotdev/usehooks": "^2.4.1", "capitalize": "^2.0.4", "chroma-js": "^2.4.2", "class-variance-authority": "^0.7.0", "client-only": "^0.0.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "draft-js": "^0.11.7", "framer-motion": "^10.16.4", "google-map-react": "^2.2.1", "graphql": "^16.8.1", "graphql-request": "^6.1.0", "install": "^0.13.0", "intl-dateformat": "^0.1.4", "jotai": "^2.5.1", "ky": "^1.2.3", "logrocket": "^8.1.0", "luxon": "^3.4.4", "next": "^14.2.4", "next-auth": "5.0.0-beta.19", "next-seo": "^6.4.0", "next-themes": "^0.2.1", "npm": "^10.8.1", "rc-slider": "^10.4.0", "react": "^18.3.1", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.51.5", "react-hooks-global-state": "^2.1.0", "react-swipeable": "^7.0.1", "react-use-keypress": "^1.3.1", "rollbar": "^2.26.4", "sanitize-html": "^2.12.1", "sass": "^1.69.5", "server-only": "^0.0.1", "sharp": "^0.32.6", "sonner": "^1.4.0", "stripe": "^15.3.0", "tailwindcss-animate": "^1.0.7", "valibot": "^0.33.3"}, "devDependencies": {"@graphql-codegen/add": "^5.0.0", "@graphql-codegen/client-preset-swc-plugin": "^0.2.0", "@graphql-codegen/typescript": "^4.0.6", "@graphql-codegen/typescript-operations": "^4.2.0", "@graphql-codegen/typescript-react-query": "^6.1.0", "@ianvs/prettier-plugin-sort-imports": "^4.1.1", "@swc-jotai/react-refresh": "^0.1.0", "@types/chroma-js": "^2.4.4", "@types/draft-js": "^0.11.18", "@types/google-map-react": "^2.1.10", "@types/lodash": "^4.14.202", "@types/luxon": "^3.3.7", "@types/node": "20.8.7", "@types/react": "18.2.31", "@types/react-datepicker": "^4.19.3", "@types/react-dom": "18.2.14", "@types/react-draft-wysiwyg": "^1.13.8", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "^10.4.16", "eslint": "8.53.0", "eslint-config-next": "^14.0.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-tailwindcss": "^3.13.0", "lodash": "^4.17.21", "postcss": "^8.4.31", "prettier-plugin-tailwindcss": "^0.5.7", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.3.5", "typescript": "5.2.2"}}